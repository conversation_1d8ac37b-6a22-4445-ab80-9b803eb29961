Perfect! That makes a lot of sense - automating repetitive schema markup tasks for SEO specialists and developers is definitely a pain point worth solving. I can see from your screenshots how the bookmarklet fills in the field names, which is a clever way to create the template structure.

Now I'd like to understand the scope and frequency of this work:

How often do you (or your target users) typically create schema markup? For example:

Is this something you do daily for multiple client sites?

Weekly for blog posts or product pages?

Or more project-based when setting up new sites?

And what types of schema do you find yourself creating most often? I noticed your example was NewsArticle schema, but are you also working with:

Local Business schemas

Product/Offer schemas

FAQ or HowTo schemas

Person/Organization schemas

Understanding the volume and variety will help me figure out how robust the template system needs to be and whether you'll need multiple saved templates for different schema types.

It will be done once on large scale, for the whole website and then added to every time an article is published.

All schema related to local and national businesses everything you have listed, plus possibly, video and recipe schema, but this is a template, so it does not matter, as we should be able to create any schema, but mostly this is for repetive schema types such as blog posts and videos and FAQs, person

locla business and organisation schema is only done once per website, this is designed for those other types which usually have a templated format on a website
It will be done once on large scale, for the whole website and then added to every time an article is published. All schema related to local and national businesses everything you have listed, plus possibly, video and recipe schema, but this is a template, so it does not matter, as we should be able to create any schema, but mostly this is for repetive schema types such as blog posts and videos and FAQs, person locla business and organisation schema is only done once per website, this is designed for those other types which usually have a templated format on a website
Perfect! That clarifies the scope really well. So you're targeting the repetitive, templated content like blog posts, videos, FAQs, and person profiles that follow consistent patterns across a website - not the one-time setup schemas like LocalBusiness.

Looking at your second screenshot with the red arrows, I can see how the bookmarklet fills in field names like "Name" and "URL" - that's a great way to establish the template structure.

Now I want to understand the data capture mechanism better. When you mentioned "clicking all the relevant fields one by one" on the target page:

What type of content/elements are you expecting to capture? For example:

Article headlines, author names, publication dates

Video titles, descriptions, thumbnails

FAQ question/answer pairs

Person names, job titles, bio text

And how do you envision the clicking/selection process working? Are you thinking:

Click directly on text elements (like headlines, paragraphs)

Click on containers/sections that hold the content

Some kind of highlighting or selection tool

Or a combination approach?

I'm particularly curious about how you'll handle different content types - like capturing a date vs. a paragraph of text vs. an image URL.

What type of content/elements are you expecting to capture?
all of the above

I wan to have a pop up with all the fields we already captured, so all the fields we are expected to fill out

Then we can either autodetect common fields, such as URL or autor or bredcrumb or we can click on those fields, there are TwO modes, one is manually filling out the field by presing the button and then filling it out

The second mode will open a sort of selector and once clicked it will capture all the relevant selectors of that particular field, we can cycle through available seelctors untll we find the one we need, there should be a preview so we can see the data we will be capturing if we choose that selector.

we also want the ability to TRASNFORM that content as we capture it with some advanced features
Advanced String Transformation ®
Select Transformation
Remove Numbers
Remove Text
Remove Specific String
Remove First N Characters
Remove Last N Characters
Pattern Matching
Custom
What type of content/elements are you expecting to capture? all of the above I wan to have a pop up with all the fields we already captured, so all the fields we are expected to fill out Then we can either autodetect common fields, such as URL or autor or bredcrumb or we can click on those fields, there are TwO modes, one is manually filling out the field by presing the button and then filling it out The second mode will open a sort of selector and once clicked it will capture all the relevant selectors of that particular field, we can cycle through available seelctors untll we find the one we need, there should be a preview so we can see the data we will be capturing if we choose that selector. we also want the ability to TRASNFORM that content as we capture it with some advanced features Advanced String Transformation ® Select Transformation Remove Numbers Remove Text Remove Specific String Remove First N Characters Remove Last N Characters Pattern Matching Custom
Excellent! Now I can see the full picture from your screenshots - this is a really sophisticated data capture workflow. I love how you're thinking about both the manual flexibility and the automated efficiency.

Looking at your screenshots, I can see the popup with all the template fields, the selector cycling interface, and those transformation options. The "Selected Text" dialog with the CSS selector refinement ("h1") and transformation preview is particularly clever.

Here's what I want to understand about the technical capture mechanism:

When users are in "selector mode" and clicking on elements, how do you want to handle different types of content? For example:

Text content: Click on headlines, paragraphs, author names - straightforward text extraction

Attribute-based data: Things like image URLs (src attribute), links (href attribute), meta dates

Structured content: Like breadcrumbs that might need multiple elements captured as a sequence

Computed/dynamic content: Dates that might need to be formatted or calculated

The reason I'm asking is that your transformation features (Remove Numbers, Pattern Matching, etc.) suggest you're expecting to handle some complex text processing. Are you anticipating scenarios where the raw captured content will need significant cleanup before it's suitable for schema markup?

Also, looking at your Forbes example - are you planning to save these templates per website/domain, so users can reuse the same field mappings across multiple articles on the same site?

field matching, if you see my bookmarklet
javascript:(function(){const inputs=document.querySelectorAll('input[type="text"],input[type="email"],input[type="tel"],input[type="url"],input[type="password"],textarea,select');inputs.forEach(input=>{let labelText='';if(input.id){const label=document.querySelector(`label[for="${input.id}"]`);if(label){labelText=label.textContent.trim().replace(/[*:]/g,'').trim();}}if(!labelText){const closestLabel=input.closest('div,fieldset,section')?.querySelector(%27label,.form-label,.control-label,.field-label%27);if(closestLabel){labelText=closestLabel.textContent.trim().replace(/[*:]/g,%27%27).trim();}}if(!labelText){const%20prevElement=input.previousElementSibling;if(prevElement&&(prevElement.tagName===%27LABEL%27||prevElement.classList.contains(%27form-label%27)||prevElement.classList.contains(%27control-label%27))){labelText=prevElement.textContent.trim().replace(/[*:]/g,%27%27).trim();}}if(!labelText&&input.placeholder){labelText=input.placeholder.trim();}if(!labelText&&input.name){labelText=input.name.charAt(0).toUpperCase()+input.name.slice(1);}if(labelText){if(input.tagName.toLowerCase()===%27select%27){input.value=%27%27;}else{input.value=labelText;}input.dispatchEvent(new%20Event(%27input%27,{bubbles:true}));input.dispatchEvent(new%20Event(%27change%27,{bubbles:true}));}});alert(%27Fields%20filled%20with%20their%20label%20names!%27);})();

you can see it matches any field as most schema builders are just forms that need to be filled with text, so we can save profiles into the app.

reverse bookmarklet functionality will be looking at those fields that it has learned and just fill the content that is stiing there.

So it's just OVERRITING the content that is already just sitting there with our new contnet

it's really the same as the bookmarklet but now instead if the field name we ar putting the data we scraped which matches thta name
field matching, if you see my bookmarklet javascript:(function(){const inputs=document.querySelectorAll('input[type="text"],input[type="email"],input[type="tel"],input[type="url"],input[type="password"],textarea,select');inputs.forEach(input=>{let labelText='';if(input.id){const label=document.querySelector(`label[for="${input.id}"]`);if(label){labelText=label.textContent.trim().replace(/[*:]/g,'').trim();}}if(!labelText){const closestLabel=input.closest('div,fieldset,section')?.querySelector(%27label,.form-label,.control-label,.field-label%27);if(closestLabel){labelText=closestLabel.textContent.trim().replace(/[*:]/g,%27%27).trim();}}if(!labelText){const%20prevElement=input.previousElementSibling;if(prevElement&&(prevElement.tagName===%27LABEL%27||prevElement.classList.contains(%27form-label%27)||prevElement.classList.contains(%27control-label%27))){labelText=prevElement.textContent.trim().replace(/[*:]/g,%27%27).trim();}}if(!labelText&&input.placeholder){labelText=input.placeholder.trim();}if(!labelText&&input.name){labelText=input.name.charAt(0).toUpperCase()+input.name.slice(1);}if(labelText){if(input.tagName.toLowerCase()===%27select%27){input.value=%27%27;}else{input.value=labelText;}input.dispatchEvent(new%20Event(%27input%27,{bubbles:true}));input.dispatchEvent(new%20Event(%27change%27,{bubbles:true}));}});alert(%27Fields%20filled%20with%20their%20label%20names!%27);})(); you can see it matches any field as most schema builders are just forms that need to be filled with text, so we can save profiles into the app. reverse bookmarklet functionality will be looking at those fields that it has learned and just fill the content that is stiing there. So it's just OVERRITING the content that is already just sitting there with our new contnet it's really the same as the bookmarklet but now instead if the field name we ar putting the data we scraped which matches thta name
image.jpg
image.jpg
Perfect! Now I completely understand the field matching system. Looking at your bookmarklet code, I can see exactly how this works:

Template Creation: The bookmarklet intelligently detects field names using multiple fallback strategies:

Associated labels by ID (label[for="fieldId"])

Nearby labels in DOM structure (.form-label, .control-label)

Previous sibling elements that are labels

Placeholder text as fallback

Field name attribute as final fallback

Reverse Operation: The exact same detection logic runs, but instead of filling fields with their own label text, it fills them with the captured content that matches that field name.

This is brilliant because it makes the system truly universal - whether you're in UpTools (with "Headline"), SEOPress (with "Article Title"), or any other schema builder, the bookmarklet automatically detects what each field is called and matches it to your captured data.

So if you captured {"headline": "Senate Plans To Vote...", "author": "Antonio Pequeno IV"}, the reverse bookmarklet would:

Find the headline field (however it's labeled in that particular builder)

Fill it with "Senate Plans To Vote..."

Find the author field

Fill it with "Antonio Pequeno IV"

This solves the universality question perfectly - no need for builder-specific profiles or manual field mapping!

Now I have one final question before I can create your masterplan: What's your timeline and priority for building this? Are you:

Planning to build this yourself as a side project?

Looking to hire developers to build it?

Wanting to validate the concept before committing resources?

Needing it urgently for current work, or is this more exploratory?

Understanding your development approach will help me structure the masterplan appropriately.

myself, I am a beginner-midlevel developer
i will be building with Cursor, so you need to explaina the content in depth, so that an IDE assistant has everything it needs, I don't need to understand it. I am good at organising and instructing

you should have a master plan document that will explain the intricacies of each step in a sepaarte document, so each important feature should have a separate doc that outlines the use and build based on what we dicusses

I will be building it straight awya, no need to valdate

it just a form replacement plugin in the end

we are mapping form fields and then refilling them with content we scraped
myself, I am a beginner-midlevel developer i will be building with Cursor, so you need to explaina the content in depth, so that an IDE assistant has everything it needs, I don't need to understand it. I am good at organising and instructing you should have a master plan document that will explain the intricacies of each step in a sepaarte document, so each important feature should have a separate doc that outlines the use and build based on what we dicusses I will be building it straight awya, no need to valdate it just a form replacement plugin in the end we are mapping form fields and then refilling them with content we scraped
image.jpg
image.jpg
Perfect! Now I have the complete picture. This is essentially a form replacement plugin that maps form fields and refills them with scraped content - a brilliant way to automate schema markup creation. Since you're building with Cursor and need detailed documentation for the AI assistant, I'll create a comprehensive masterplan with separate detailed documents for each major feature.

masterplan.md
Page-to-Schema Assistant - Master Plan
App Overview
A Chrome extension that automates schema markup creation by mapping form fields in schema builders and refilling them with content scraped from target pages. Acts as a universal form replacement plugin for SEO specialists and web developers.

Target Audience
SEO specialists who create schema markup regularly

Web developers working with structured data

Users working with repetitive, templated content (blog posts, videos, FAQs)

Core Workflow
Template Extraction: Use bookmarklet in schema builder to extract field structure

Profile Creation: Map universal templates to domain-specific selectors

Content Capture: Scrape content from target pages using intelligent selectors

Data Application: Fill schema builders or generate clean JSON-LD snippets

Key Features & Architecture
1. Universal Bookmarklet System
Intelligent field detection using multiple fallback strategies

Works across any schema builder (UpTools, SEOPress, WordPress, etc.)

Bidirectional operation: extract templates and fill captured content

2. Hierarchical Storage System
Universal Templates: Schema field structures (same across all sites)

Domain Profiles: Site-specific selector mappings

Sub-Templates: Content-type variations (news, recipes, locations)

All stored in local storage as JSON with export capability

3. Content Capture Engine
Click-to-capture interface with selector cycling

Real-time preview of captured content

Advanced string transformations (cleanup, formatting)

Support for text, attributes, and structured data

4. Dual Output Modes
Mode 1: Reverse bookmarklet fills schema builder fields

Mode 2: Generate clean JSON-LD snippets for direct implementation

5. Smart Profile Management
URL-based defaults (/blog/ → news template, /recipes/ → recipe template)

Add/edit/delete domain profiles and sub-templates

Profile export/import for backup and sharing

Technical Stack
Platform: Chrome Extension (Manifest V3)

Storage: Local Storage with JSON structure

UI: HTML/CSS/JavaScript popup interface

Content Interaction: Content scripts with DOM manipulation

Data Processing: JavaScript for transformation and mapping

Development Phases
Phase 1: Core Bookmarklet System
Implement universal field detection algorithm

Create bidirectional bookmarklet functionality

Test across multiple schema builders

Phase 2: Storage & Profile Management
Design hierarchical JSON storage structure

Build profile creation and management interface

Implement URL-based defaults

Phase 3: Content Capture Interface
Create click-to-capture functionality

Build selector cycling and preview system

Implement content transformations

Phase 4: Integration & Polish
Connect all components into seamless workflow

Add export/import capabilities

Optimize performance and user experience

Detailed Feature Documents
Each major component has a dedicated implementation document:

bookmarklet-system.md - Universal field detection and form filling

storage-architecture.md - Hierarchical data storage and management

content-capture.md - Page interaction and data extraction

profile-management.md - Domain profiles and template handling

popup-interface.md - Extension UI and user interactions

data-transformations.md - Content cleanup and formatting options

Success Metrics
90-100% automated field population

Universal compatibility across schema builders

Significant time savings for repetitive schema creation

Reliable content extraction from templated websites

Now I'll create the individual feature documents. Would you like me to generate all of them, or would you prefer to start with specific ones first? Each document will contain detailed implementation instructions that Cursor can use to build the functionality without requiring your technical understanding.

all of them be as detailed as possible
all of them be as detailed as possible
image.jpg
image.jpg
Perfect! I'll create all the detailed feature documents. Each one will be comprehensive enough for Cursor to implement the functionality without requiring deep technical understanding from you.

1. bookmarklet-system.md
Universal Bookmarklet System - Implementation Guide
Overview
The bookmarklet system is the core engine that enables universal form field detection and bidirectional data filling across any schema builder platform. It operates in two modes: template extraction and data population.

Core Algorithm Implementation
Field Detection Strategy
The system uses a cascading detection approach with multiple fallback strategies to identify form fields and their labels:

javascript
const FIELD_SELECTORS = [
  'input[type="text"]',
  'input[type="email"]', 
  'input[type="tel"]',
  'input[type="url"]',
  'input[type="password"]',
  'textarea',
  'select'
];

function detectFieldLabel(inputElement) {
  let labelText = '';
  
  // Strategy 1: Associated label by ID
  if (inputElement.id) {
    const associatedLabel = document.querySelector(`label[for="${inputElement.id}"]`);
    if (associatedLabel) {
      labelText = cleanLabelText(associatedLabel.textContent);
    }
  }
  
  // Strategy 2: Nearest label in DOM structure
  if (!labelText) {
    const nearbyLabel = inputElement.closest('div,fieldset,section')?.querySelector(
      'label, .form-label, .control-label, .field-label'
    );
    if (nearbyLabel) {
      labelText = cleanLabelText(nearbyLabel.textContent);
    }
  }
  
  // Strategy 3: Previous sibling element
  if (!labelText) {
    const prevElement = inputElement.previousElementSibling;
    if (prevElement && isLabelElement(prevElement)) {
      labelText = cleanLabelText(prevElement.textContent);
    }
  }
  
  // Strategy 4: Placeholder text fallback
  if (!labelText && inputElement.placeholder) {
    labelText = inputElement.placeholder.trim();
  }
  
  // Strategy 5: Name attribute fallback
  if (!labelText && inputElement.name) {
    labelText = capitalizeFirstLetter(inputElement.name);
  }
  
  return labelText;
}

function cleanLabelText(text) {
  return text.trim().replace(/[*:]/g, '').trim();
}

function isLabelElement(element) {
  return element.tagName === 'LABEL' || 
         element.classList.contains('form-label') || 
         element.classList.contains('control-label');
}
Template Extraction Mode
When extracting templates from schema builders, the system identifies all form fields and captures their structure:

javascript
function extractTemplate() {
  const fields = document.querySelectorAll(FIELD_SELECTORS.join(','));
  const template = {
    fields: {},
    metadata: {
      url: window.location.hostname,
      extractedAt: new Date().toISOString(),
      totalFields: fields.length
    }
  };
  
  fields.forEach((field, index) => {
    const label = detectFieldLabel(field);
    if (label) {
      const fieldInfo = {
        selector: generateUniqueSelector(field),
        type: field.tagName.toLowerCase(),
        inputType: field.type || 'text',
        label: label,
        required: field.required || field.hasAttribute('required'),
        placeholder: field.placeholder || '',
        name: field.name || '',
        id: field.id || ''
      };
      
      template.fields[label.toLowerCase()] = fieldInfo;
      
      // Fill field with its own label for template creation
      fillField(field, label);
    }
  });
  
  return template;
}

function generateUniqueSelector(element) {
  // Generate the most specific, reliable selector
  const selectors = [];
  
  if (element.id) {
    selectors.push(`#${element.id}`);
  }
  
  if (element.name) {
    selectors.push(`[name="${element.name}"]`);
  }
  
  if (element.className) {
    const classes = element.className.split(' ').filter(c => c.length > 0);
    if (classes.length > 0) {
      selectors.push(`.${classes.join('.')}`);
    }
  }
  
  // Add position-based selector as fallback
  const tagName = element.tagName.toLowerCase();
  const siblings = Array.from(element.parentNode.children).filter(el => 
    el.tagName.toLowerCase() === tagName
  );
  const index = siblings.indexOf(element);
  selectors.push(`${tagName}:nth-of-type(${index + 1})`);
  
  return selectors[0]; // Return most specific selector
}
Data Population Mode
When filling fields with captured data, the system matches field labels to stored content:

javascript
function populateFields(capturedData) {
  const fields = document.querySelectorAll(FIELD_SELECTORS.join(','));
  const results = {
    filled: 0,
    skipped: 0,
    errors: []
  };
  
  fields.forEach(field => {
    const label = detectFieldLabel(field);
    if (label) {
      const labelKey = label.toLowerCase();
      const matchingData = findMatchingData(labelKey, capturedData);
      
      if (matchingData) {
        try {
          fillField(field, matchingData.value);
          results.filled++;
        } catch (error) {
          results.errors.push({
            field: label,
            error: error.message
          });
        }
      } else {
        results.skipped++;
      }
    }
  });
  
  return results;
}

function findMatchingData(labelKey, capturedData) {
  // Exact match first
  if (capturedData[labelKey]) {
    return capturedData[labelKey];
  }
  
  // Fuzzy matching for similar terms
  const similarKeys = Object.keys(capturedData).filter(key => {
    return areSimilarLabels(key, labelKey);
  });
  
  if (similarKeys.length > 0) {
    return capturedData[similarKeys[0]];
  }
  
  return null;
}

function areSimilarLabels(key1, key2) {
  // Define common field name variations
  const synonyms = {
    'headline': ['title', 'heading', 'article title', 'post title'],
    'author': ['author name', 'writer', 'by'],
    'url': ['link', 'website', 'web address'],
    'image': ['image url', 'picture', 'photo'],
    'description': ['summary', 'excerpt', 'content']
  };
  
  // Check if either key is a synonym of the other
  for (const [main, alts] of Object.entries(synonyms)) {
    if ((key1 === main && alts.includes(key2)) || 
        (key2 === main && alts.includes(key1))) {
      return true;
    }
  }
  
  // Simple similarity check (contains)
  return key1.includes(key2) || key2.includes(key1);
}

function fillField(field, value) {
  if (field.tagName.toLowerCase() === 'select') {
    // Handle select elements
    const option = Array.from(field.options).find(opt => 
      opt.value === value || opt.textContent.trim() === value
    );
    if (option) {
      field.value = option.value;
    }
  } else {
    field.value = value;
  }
  
  // Trigger events to notify form frameworks
  field.dispatchEvent(new Event('input', { bubbles: true }));
  field.dispatchEvent(new Event('change', { bubbles: true }));
  field.dispatchEvent(new Event('blur', { bubbles: true }));
}
Integration with Extension
The bookmarklet functions are injected into pages via content scripts:

javascript
// Content script integration
function injectBookmarklet(mode, data = null) {
  const script = document.createElement('script');
  script.textContent = `
    (${mode === 'extract' ? extractTemplate : populateFields}).call(window, ${JSON.stringify(data)});
  `;
  document.head.appendChild(script);
  document.head.removeChild(script);
}

// Message handling
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'extractTemplate') {
    const template = injectBookmarklet('extract');
    sendResponse({ success: true, template });
  } else if (request.action === 'populateFields') {
    const results = injectBookmarklet('populate', request.data);
    sendResponse({ success: true, results });
  }
});
2. storage-architecture.md
Storage Architecture - Implementation Guide
Overview
The storage system uses a hierarchical JSON structure stored in Chrome's local storage, organizing data into universal templates, domain profiles, and captured content with full export/import capabilities.

Data Structure Design
Master Storage Schema
javascript
const STORAGE_SCHEMA = {
  version: "1.0",
  templates: {
    // Universal schema templates (same across all websites)
    "newsarticle": {
      type: "NewsArticle",
      fields: {
        "headline": { required: true, type: "text" },
        "author": { required: true, type: "text" },
        "datepublished": { required: true, type: "date" },
        "image": { required: false, type: "url" },
        "publisher": { required: true, type: "text" }
      },
      createdAt: "2025-06-29T09:12:00Z"
    },
    "recipe": {
      type: "Recipe",
      fields: {
        "name": { required: true, type: "text" },
        "description": { required: true, type: "text" },
        "preptime": { required: false, type: "duration" },
        "cooktime": { required: false, type: "duration" },
        "ingredients": { required: true, type: "array" }
      },
      createdAt: "2025-06-29T09:12:00Z"
    }
  },
  profiles: {
    // Domain-specific selector mappings
    "forbes.com": {
      "news": {
        templateType: "newsarticle",
        selectors: {
          "headline": "h1.headline",
          "author": ".author-name",
          "datepublished": ".publish-date",
          "image": ".featured-image img",
          "publisher": ".site-name"
        },
        transformations: {
          "datepublished": ["formatDate"],
          "image": ["extractSrc"]
        },
        urlPatterns: ["/news/", "/breaking/"],
        createdAt: "2025-06-29T09:12:00Z",
        lastUsed: "2025-06-29T09:12:00Z"
      },
      "recipes": {
        templateType: "recipe",
        selectors: {
          "name": ".recipe-title",
          "description": ".recipe-description",
          "preptime": ".prep-time",
          "cooktime": ".cook-time",
          "ingredients": ".ingredient-list li"
        },
        urlPatterns: ["/recipes/", "/food/"]
      }
    },
    "cnn.com": {
      "news": {
        templateType: "newsarticle",
        selectors: {
          "headline": ".pg-headline",
          "author": ".byline__name",
          "datepublished": ".timestamp",
          "image": ".media__image img"
        }
      }
    }
  },
  sessions: {
    // Current capture session data
    "current": {
      domain: "forbes.com",
      profileType: "news",
      capturedData: {
        "headline": {
          value: "Senate Plans To Vote On Trump's Signature Spending Bill...",
          selector: "h1.headline",
          timestamp: "2025-06-29T09:12:00Z"
        },
        "author": {
          value: "Antonio Pequeno IV",
          selector: ".author-name",
          timestamp: "2025-06-29T09:12:00Z"
        }
      },
      startedAt: "2025-06-29T09:12:00Z"
    }
  },
  settings: {
    defaultProfile: "news",
    autoDetectProfileType: true,
    urlBasedDefaults: {
      "/blog/": "news",
      "/recipes/": "recipe",
      "/location/": "localbusiness"
    },
    transformationSettings: {
      dateFormat: "ISO8601",
      textCleaning: true
    }
  }
};
Storage Management Implementation
Core Storage Functions
javascript
class SchemaStorage {
  constructor() {
    this.storageKey = 'pageToSchemaData';
    this.initialized = false;
  }
  
  async initialize() {
    if (this.initialized) return;
    
    const stored = await this.getStoredData();
    if (!stored || !stored.version) {
      await this.createInitialStorage();
    } else if (stored.version !== STORAGE_SCHEMA.version) {
      await this.migrateStorage(stored);
    }
    
    this.initialized = true;
  }
  
  async getStoredData() {
    return new Promise((resolve) => {
      chrome.storage.local.get([this.storageKey], (result) => {
        resolve(result[this.storageKey] || null);
      });
    });
  }
  
  async saveData(data) {
    return new Promise((resolve) => {
      chrome.storage.local.set({ [this.storageKey]: data }, () => {
        resolve();
      });
    });
  }
  
  async createInitialStorage() {
    const initialData = {
      ...STORAGE_SCHEMA,
      templates: {},
      profiles: {},
      sessions: {},
      createdAt: new Date().toISOString()
    };
    
    await this.saveData(initialData);
  }
}
Template Management
javascript
class TemplateManager extends SchemaStorage {
  async createTemplate(templateData) {
    const data = await this.getStoredData();
    const templateId = this.generateTemplateId(templateData.type);
    
    data.templates[templateId] = {
      ...templateData,
      createdAt: new Date().toISOString(),
      id: templateId
    };
    
    await this.saveData(data);
    return templateId;
  }
  
  async getTemplate(templateId) {
    const data = await this.getStoredData();
    return data.templates[templateId] || null;
  }
  
  async updateTemplate(templateId, updates) {
    const data = await this.getStoredData();
    if (data.templates[templateId]) {
      data.templates[templateId] = {
        ...data.templates[templateId],
        ...updates,
        updatedAt: new Date().toISOString()
      };
      await this.saveData(data);
      return true;
    }
    return false;
  }
  
  async deleteTemplate(templateId) {
    const data = await this.getStoredData();
    if (data.templates[templateId]) {
      delete data.templates[templateId];
      await this.saveData(data);
      return true;
    }
    return false;
  }
  
  async listTemplates() {
    const data = await this.getStoredData();
    return Object.keys(data.templates).map(id => ({
      id,
      ...data.templates[id]
    }));
  }
  
  generateTemplateId(type) {
    return type.toLowerCase().replace(/[^a-z0-9]/g, '');
  }
}
Profile Management
javascript
class ProfileManager extends SchemaStorage {
  async createProfile(domain, profileType, profileData) {
    const data = await this.getStoredData();
    
    if (!data.profiles[domain]) {
      data.profiles[domain] = {};
    }
    
    data.profiles[domain][profileType] = {
      ...profileData,
      createdAt: new Date().toISOString(),
      lastUsed: new Date().toISOString()
    };
    
    await this.saveData(data);
    return { domain, profileType };
  }
  
  async getProfile(domain, profileType) {
    const data = await this.getStoredData();
    return data.profiles[domain]?.[profileType] || null;
  }
  
  async updateProfile(domain, profileType, updates) {
    const data = await this.getStoredData();
    if (data.profiles[domain]?.[profileType]) {
      data.profiles[domain][profileType] = {
        ...data.profiles[domain][profileType],
        ...updates,
        updatedAt: new Date().toISOString(),
        lastUsed: new Date().toISOString()
      };
      await this.saveData(data);
      return true;
    }
    return false;
  }
  
  async deleteProfile(domain, profileType) {
    const data = await this.getStoredData();
    if (data.profiles[domain]?.[profileType]) {
      delete data.profiles[domain][profileType];
      
      // Clean up empty domain entries
      if (Object.keys(data.profiles[domain]).length === 0) {
        delete data.profiles[domain];
      }
      
      await this.saveData(data);
      return true;
    }
    return false;
  }
  
  async getDomainProfiles(domain) {
    const data = await this.getStoredData();
    return data.profiles[domain] || {};
  }
  
  async getAllProfiles() {
    const data = await this.getStoredData();
    return data.profiles;
  }
  
  async getDefaultProfile(url) {
    const data = await this.getStoredData();
    const domain = new URL(url).hostname;
    
    // Check URL-based defaults
    for (const [pattern, profileType] of Object.entries(data.settings.urlBasedDefaults)) {
      if (url.includes(pattern)) {
        const profile = await this.getProfile(domain, profileType);
        if (profile) return { domain, profileType, profile };
      }
    }
    
    // Return most recently used profile for domain
    const domainProfiles = await this.getDomainProfiles(domain);
    if (Object.keys(domainProfiles).length > 0) {
      const mostRecent = Object.entries(domainProfiles)
        .sort(([,a], [,b]) => new Date(b.lastUsed) - new Date(a.lastUsed))[0];
      return {
        domain,
        profileType: mostRecent[0],
        profile: mostRecent[1]
      };
    }
    
    return null;
  }
}
Session Management
javascript
class SessionManager extends SchemaStorage {
  async startSession(domain, profileType) {
    const data = await this.getStoredData();
    
    data.sessions.current = {
      domain,
      profileType,
      capturedData: {},
      startedAt: new Date().toISOString()
    };
    
    await this.saveData(data);
    return data.sessions.current;
  }
  
  async updateCapturedData(fieldName, fieldData) {
    const data = await this.getStoredData();
    
    if (!data.sessions.current) {
      throw new Error('No active session');
    }
    
    data.sessions.current.capturedData[fieldName] = {
      ...fieldData,
      timestamp: new Date().toISOString()
    };
    
    await this.saveData(data);
    return data.sessions.current.capturedData[fieldName];
  }
  
  async getCurrentSession() {
    const data = await this.getStoredData();
    return data.sessions.current || null;
  }
  
  async clearSession() {
    const data = await this.getStoredData();
    data.sessions.current = null;
    await this.saveData(data);
  }
  
  async saveSessionAsProfile(profileName) {
    const session = await this.getCurrentSession();
    if (!session) throw new Error('No active session');
    
    const profileData = {
      templateType: session.profileType,
      selectors: {},
      transformations: {}
    };
    
    // Extract selectors from captured data
    Object.entries(session.capturedData).forEach(([fieldName, fieldData]) => {
      profileData.selectors[fieldName] = fieldData.selector;
      if (fieldData.transformations) {
        profileData.transformations[fieldName] = fieldData.transformations;
      }
    });
    
    const profileManager = new ProfileManager();
    return await profileManager.createProfile(session.domain, profileName, profileData);
  }
}
Export/Import System
javascript
class ImportExportManager extends SchemaStorage {
  async exportData(includeTemplates = true, includeProfiles = true, includeSessions = false) {
    const data = await this.getStoredData();
    const exportData = {
      version: data.version,
      exportedAt: new Date().toISOString()
    };
    
    if (includeTemplates) {
      exportData.templates = data.templates;
    }
    
    if (includeProfiles) {
      exportData.profiles = data.profiles;
    }
    
    if (includeSessions) {
      exportData.sessions = data.sessions;
    }
    
    return JSON.stringify(exportData, null, 2);
  }
  
  async importData(jsonData, mergeStrategy = 'overwrite') {
    const importData = JSON.parse(jsonData);
    const currentData = await this.getStoredData();
    
    if (mergeStrategy === 'overwrite') {
      // Replace existing data
      const newData = {
        ...currentData,
        ...importData,
        importedAt: new Date().toISOString()
      };
      await this.saveData(newData);
    } else if (mergeStrategy === 'merge') {
      // Merge data, keeping existing when conflicts
      const newData = {
        ...currentData,
        importedAt: new Date().toISOString()
      };
      
      if (importData.templates) {
        newData.templates = { ...newData.templates, ...importData.templates };
      }
      
      if (importData.profiles) {
        Object.entries(importData.profiles).forEach(([domain, profiles]) => {
          if (!newData.profiles[domain]) {
            newData.profiles[domain] = {};
          }
          newData.profiles[domain] = { ...newData.profiles[domain], ...profiles };
        });
      }
      
      await this.saveData(newData);
    }
    
    return true;
  }
  
  async exportProfile(domain, profileType) {
    const profile = await new ProfileManager().getProfile(domain, profileType);
    if (!profile) return null;
    
    return JSON.stringify({
      domain,
      profileType,
      profile,
      exportedAt: new Date().toISOString()
    }, null, 2);
  }
}
3. content-capture.md
Content Capture System - Implementation Guide
Overview
The content capture system enables users to interactively select and extract content from web pages using a visual interface with selector cycling, real-time preview, and advanced content transformations.

Core Capture Interface
Visual Selection System
javascript
class ContentCapture {
  constructor() {
    this.isActive = false;
    this.currentField = null;
    this.highlightedElement = null;
    this.selectorCandidates = [];
    this.currentSelectorIndex = 0;
    this.overlay = null;
    this.previewModal = null;
  }
  
  initialize() {
    this.createOverlayElements();
    this.attachEventListeners();
    this.loadCaptureSession();
  }
  
  createOverlayElements() {
    // Create main overlay
    this.overlay = document.createElement('div');
    this.overlay.id = 'schema-capture-overlay';
    this.overlay.innerHTML = `
      <div class="capture-toolbar">
        <div class="field-selector">
          <select id="field-dropdown">
            <option value="">Select Field to Capture</option>
          </select>
        </div>
        <div class="capture-modes">
          <button id="auto-capture" class="mode-btn">Auto Detect</button>
          <button id="manual-capture" class="mode-btn active">Manual Select</button>
        </div>
        <div class="actions">
          <button id="clear-selection">Clear</button>
          <button id="save-profile">Save Profile</button>
          <button id="close-capture">Done</button>
        </div>
      </div>
      <div class="selection-info" id="selection-info" style="display: none;">
        <div class="selector-cycling">
          <button id="prev-selector">◀</button>
          <span id="selector-display"></span>
          <button id="next-selector">▶</button>
        </div>
        <div class="preview-content" id="preview-content"></div>
        <div class="transformation-options" id="transformation-options">
          <select id="transformation-select">
            <option value="">No Transformation</option>
            <option value="removeNumbers">Remove Numbers</option>
            <option value="removeSpecific">Remove Specific String</option>
            <option value="trimStart">Remove First N Characters</option>
            <option value="trimEnd">Remove Last N Characters</option>
            <option value="patternMatch">Pattern Matching</option>
            <option value="custom">Custom Transformation</option>
          </select>
        </div>
        <div class="capture-actions">
          <button id="confirm-capture">Capture This</button>
          <button id="cancel-capture">Cancel</button>
        </div>
      </div>
    `;
    
    // Apply styles
    this.overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.1);
      z-index: 10000;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;
    
    document.body.appendChild(this.overlay);
    this.attachOverlayListeners();
  }
  
  attachEventListeners() {
    // Prevent page interactions while capturing
    document.addEventListener('click', this.handleClick.bind(this), true);
    document.addEventListener('mouseover', this.handleMouseOver.bind(this), true);
    document.addEventListener('mouseout', this.handleMouseOut.bind(this), true);
    document.addEventListener('keydown', this.handleKeyDown.bind(this), true);
  }
  
  handleClick(event) {
    if (!this.isActive) return;
    
    // Don't interfere with overlay interactions
    if (this.overlay.contains(event.target)) return;
    
    event.preventDefault();
    event.stopPropagation();
    
    if (this.currentField) {
      this.selectElement(event.target);
    }
  }
  
  handleMouseOver(event) {
    if (!this.isActive || !this.currentField) return;
    if (this.overlay.contains(event.target)) return;
    
    this.highlightElement(event.target);
  }
  
  highlightElement(element) {
    // Remove previous highlight
    if (this.highlightedElement) {
      this.highlightedElement.style.outline = '';
    }
    
    // Add highlight to current element
    element.style.outline = '2px solid #007cba';
    element.style.outlineOffset = '2px';
    this.highlightedElement = element;
  }
  
  selectElement(element) {
    this.generateSelectorCandidates(element);
    this.currentSelectorIndex = 0;
    this.showSelectionInterface(element);
  }
  
  generateSelectorCandidates(element) {
    const candidates = [];
    
    // ID selector (highest priority)
    if (element.id) {
      candidates.push({
        selector: `#${element.id}`,
        specificity: 100,
        type: 'id'
      });
    }
    
    // Class selectors
    if (element.className && typeof element.className === 'string') {
      const classes = element.className.split(' ').filter(c => c.length > 0);
      if (classes.length > 0) {
        candidates.push({
          selector: `.${classes.join('.')}`,
          specificity: 80,
          type: 'class'
        });
        
        // Single class selectors
        classes.forEach(cls => {
          candidates.push({
            selector: `.${cls}`,
            specificity: 60,
            type: 'single-class'
          });
        });
      }
    }
    
    // Attribute selectors
    const importantAttrs = ['data-testid', 'data-cy', 'name', 'data-field'];
    importantAttrs.forEach(attr => {
      if (element.hasAttribute(attr)) {
        candidates.push({
          selector: `[${attr}="${element.getAttribute(attr)}"]`,
          specificity: 70,
          type: 'attribute'
        });
      }
    });
    
    // Tag + class combinations
    if (element.className) {
      const classes = element.className.split(' ').filter(c => c.length > 0);
      if (classes.length > 0) {
        candidates.push({
          selector: `${element.tagName.toLowerCase()}.${classes[0]}`,
          specificity: 50,
          type: 'tag-class'
        });
      }
    }
    
    // Positional selectors
    const parent = element.parentElement;
    if (parent) {
      const siblings = Array.from(parent.children);
      const index = siblings.indexOf(element);
      
      candidates.push({
        selector: `${element.tagName.toLowerCase()}:nth-child(${index + 1})`,
        specificity: 30,
        type: 'position'
      });
      
      // Tag-specific position
      const tagSiblings = siblings.filter(el => el.tagName === element.tagName);
      if (tagSiblings.length > 1) {
        const tagIndex = tagSiblings.indexOf(element);
        candidates.push({
          selector: `${element.tagName.toLowerCase()}:nth-of-type(${tagIndex + 1})`,
          specificity: 25,
          type: 'tag-position'
        });
      }
    }
    
    // Tag selector (lowest priority)
    candidates.push({
      selector: element.tagName.toLowerCase(),
      specificity: 10,
      type: 'tag'
    });
    
    // Sort by specificity (highest first)
    this.selectorCandidates = candidates
      .sort((a, b) => b.specificity - a.specificity)
      .filter(candidate => {
        // Test if selector actually works and is unique enough
        const matchedElements = document.querySelectorAll(candidate.selector);
        return matchedElements.length > 0 && matchedElements.length < 10;
      });
  }
  
  showSelectionInterface(element) {
    const selectionInfo = document.getElementById('selection-info');
    const previewContent = document.getElementById('preview-content');
    const selectorDisplay = document.getElementById('selector-display');
    
    selectionInfo.style.display = 'block';
    
    this.updateSelectorDisplay();
    this.updatePreviewContent(element);
  }
  
  updateSelectorDisplay() {
    const selectorDisplay = document.getElementById('selector-display');
    const current = this.selectorCandidates[this.currentSelectorIndex];
    
    if (current) {
      selectorDisplay.textContent = `${current.selector} (${current.type})`;
    }
  }
  
  updatePreviewContent(element) {
    const previewContent = document.getElementById('preview-content');
    const current = this.selectorCandidates[this.currentSelectorIndex];
    
    if (!current) return;
    
    // Get all elements that match current selector
    const matchedElements = document.querySelectorAll(current.selector);
    const content = this.extractContent(element);
    
    previewContent.innerHTML = `
      <div class="preview-stats">
        Matches: ${matchedElements.length} elements
      </div>
      <div class="preview-value">
        <strong>Content:</strong> "${content.text}"
      </div>
      ${content.url ? `<div class="preview-url"><strong>URL:</strong> ${content.url}</div>` : ''}
      ${content.alt ? `<div class="preview-alt"><strong>Alt:</strong> ${content.alt}</div>` : ''}
    `;
  }
  
  extractContent(element) {
    const result = {
      text: '',
      url: '',
      alt: ''
    };
    
    // Extract text content
    if (element.tagName === 'IMG') {
      result.text = element.src;
      result.url = element.src;
      result.alt = element.alt;
    } else if (element.tagName === 'A') {
      result.text = element.textContent.trim();
      result.url = element.href;
    } else if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
      result.text = element.value;
    } else {
      result.text = element.textContent.trim();
    }
    
    // Check for data attributes
    const dataAttrs = ['data-url', 'data-href', 'data-src'];
    dataAttrs.forEach(attr => {
      if (element.hasAttribute(attr) && !result.url) {
        result.url = element.getAttribute(attr);
      }
    });
    
    return result;
  }
}
Content Transformation System
javascript
class ContentTransformations {
  static applyTransformation(content, transformationType, options = {}) {
    switch (transformationType) {
      case 'removeNumbers':
        return content.replace(/\d+/g, '');
      
      case 'removeSpecific':
        const removeString = options.removeString || '';
        return content.replace(new RegExp(removeString, 'gi'), '');
      
      case 'trimStart':
        const startChars = parseInt(options.startChars) || 0;
        return content.substring(startChars);
      
      case 'trimEnd':
        const endChars = parseInt(options.endChars) || 0;
        return content.substring(0, content.length - endChars);
      
      case 'patternMatch':
        const pattern = options.pattern || '';
        const match = content.match(new RegExp(pattern, 'i'));
        return match ? match[0] : content;
      
      case 'sentenceCase':
        return content.charAt(0).toUpperCase() + content.slice(1).toLowerCase();
      
      case 'titleCase':
        return content.replace(/\w\S*/g, (txt) => 
          txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
        );
      
      case 'removeHtml':
        return content.replace(/<[^>]*>/g, '');
      
      case 'extractUrl':
        const urlMatch = content.match(/https?:\/\/[^\s]+/);
        return urlMatch ? urlMatch[0] : content;
      
      case 'formatDate':
        return this.formatDate(content, options.dateFormat || 'ISO8601');
      
      case 'custom':
        // Execute custom JavaScript transformation
        if (options.customFunction) {
          try {
            return new Function('content', options.customFunction)(content);
          } catch (error) {
            console.error('Custom transformation error:', error);
            return content;
          }
        }
        return content;
      
      default:
        return content;
    }
  }
  
  static formatDate(dateString, format) {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return dateString;
    
    switch (format) {
      case 'ISO8601':
        return date.toISOString();
      case 'YYYY-MM-DD':
        return date.toISOString().split('T')[0];
      case 'MM/DD/YYYY':
        return date.toLocaleDateString('en-US');
      case 'DD/MM/YYYY':
        return date.toLocaleDateString('en-GB');
      default:
        return date.toISOString();
    }
  }
  
  static getTransformationOptions(transformationType) {
    const options = {
      removeSpecific: [
        { key: 'removeString', label: 'String to Remove', type: 'text', required: true }
      ],
      trimStart: [
        { key: 'startChars', label: 'Number of Characters', type: 'number', required: true }
      ],
      trimEnd: [
        { key: 'endChars', label: 'Number of Characters', type: 'number', required: true }
      ],
      patternMatch: [
        { key: 'pattern', label: 'RegEx Pattern', type: 'text', required: true }
      ],
      formatDate: [
        { 
          key: 'dateFormat', 
          label: 'Date Format', 
          type: 'select', 
          options: [
            { value: 'ISO8601', label: 'ISO 8601 (2025-06-29T09:12:00Z)' },
            { value: 'YYYY-MM-DD', label: 'YYYY-MM-DD' },
            { value: 'MM/DD/YYYY', label: 'MM/DD/YYYY' },
            { value: 'DD/MM/YYYY', label: 'DD/MM/YYYY' }
          ],
          required: true 
        }
      ],
      custom: [
        { key: 'customFunction', label: 'JavaScript Function Body', type: 'textarea', required: true }
      ]
    };
    
    return options[transformationType] || [];
  }
}
Auto-Detection System
javascript
class AutoDetection {
  constructor() {
    this.commonSelectors = {
      headline: [
        'h1', '.headline', '.title', '.article-title', '.post-title',
        '[data-testid*="headline"]', '.entry-title', '.page-title'
      ],
      author: [
        '.author', '.byline', '.writer', '.author-name', '.by-author',
        '[data-testid*="author"]', '.article-author', '.post-author'
      ],
      date: [
        '.date', '.publish-date', '.timestamp', '.article-date',
        '[datetime]', '.entry-date', '.post-date', 'time'
      ],
      image: [
        '.featured-image img', '.article-image img', '.hero-image img',
        '.post-thumbnail img', '.main-image img'
      ],
      description: [
        '.excerpt', '.summary', '.description', '.article-excerpt',
        '.post-excerpt', '.meta-description'
      ]
    };
  }
  
  async detectCommonFields(template) {
    const detected = {};
    
    for (const [fieldName, selectors] of Object.entries(this.commonSelectors)) {
      if (template.fields[fieldName]) {
        const element = this.findBestMatch(selectors);
        if (element) {
          detected[fieldName] = {
            element,
            selector: this.getBestSelector(element),
            content: this.extractContent(element),
            confidence: this.calculateConfidence(element, fieldName)
          };
        }
      }
    }
    
    return detected;
  }
  
  findBestMatch(selectors) {
    for (const selector of selectors) {
      const elements = document.querySelectorAll(selector);
      if (elements.length === 1) {
        return elements[0]; // Perfect match - single element
      } else if (elements.length > 1) {
        // Return the most prominent element (largest, highest on page)
        return this.selectBestCandidate(elements);
      }
    }
    return null;
  }
  
  selectBestCandidate(elements) {
    return Array.from(elements)
      .map(el => ({
        element: el,
        score: this.calculateElementScore(el)
      }))
      .sort((a, b) => b.score - a.score)[0]?.element;
  }
  
  calculateElementScore(element) {
    let score = 0;
    
    // Position score (higher on page = higher score)
    const rect = element.getBoundingClientRect();
    score += Math.max(0, 1000 - rect.top);
    
    // Size score (larger elements = higher score)
    score += rect.width * rect.height / 1000;
    
    // Content quality score
    const text = element.textContent.trim();
    if (text.length > 10 && text.length < 200) {
      score += 100; // Good content length
    }
    
    // Semantic score (better tags = higher score)
    const tagScores = {
      'h1': 100, 'h2': 80, 'h3': 60,
      'article': 50, 'header': 40, 'main': 30
    };
    score += tagScores[element.tagName.toLowerCase()] || 0;
    
    return score;
  }
  
  calculateConfidence(element, fieldName) {
    let confidence = 0.5; // Base confidence
    
    // Semantic matching
    const text = element.textContent.toLowerCase();
    const className = element.className.toLowerCase();
    const id = element.id.toLowerCase();
    
    const keywords = {
      headline: ['title', 'headline', 'heading'],
      author: ['author', 'writer', 'by'],
      date: ['date', 'time', 'publish'],
      image: ['image', 'photo', 'picture'],
      description: ['description', 'excerpt', 'summary']
    };
    
    const fieldKeywords = keywords[fieldName] || [];
    fieldKeywords.forEach(keyword => {
      if (text.includes(keyword) || className.includes(keyword) || id.includes(keyword)) {
        confidence += 0.2;
      }
    });
    
    return Math.min(confidence, 1.0);
  }
  
  getBestSelector(element) {
    // Use the same selector generation logic from content capture
    const capture = new ContentCapture();
    capture.generateSelectorCandidates(element);
    return capture.selectorCandidates[0]?.selector || element.tagName.toLowerCase();
  }
  
  extractContent(element) {
    const capture = new ContentCapture();
    return capture.extractContent(element);
  }
}
4. profile-management.md
Profile Management System - Implementation Guide
Overview
The profile management system handles domain-specific configurations, URL-based defaults, profile creation/editing, and template type management with an intuitive user interface.

Profile Management Interface
Main Profile Manager Class
javascript
class ProfileManagerUI {
  constructor() {
    this.storage = new ProfileManager();
    this.currentDomain = null;
    this.profiles = {};
    this.templates = {};
  }
  
  async initialize() {
    await this.loadData();
    this.createInterface();
    this.attachEventListeners();
  }
  
  async loadData() {
    this.profiles = await this.storage.getAllProfiles();
    this.templates = await new TemplateManager().listTemplates();
    this.currentDomain = this.getCurrentDomain();
  }
  
  getCurrentDomain() {
    return new URL(window.location.href).hostname;
  }
  
  createInterface() {
    const container = document.createElement('div');
    container.id = 'profile-manager';
    container.innerHTML = this.getInterfaceHTML();
    
    // Apply styles
    this.applyStyles(container);
    
    document.body.appendChild(container);
    this.populateInterface();
  }
  
  getInterfaceHTML() {
    return `
      <div class="profile-manager-header">
        <h3>Profile Manager - ${this.currentDomain}</h3>
        <button id="close-profile-manager" class="close-btn">×</button>
      </div>
      
      <div class="profile-tabs">
        <button id="tab-current" class="tab-btn active">Current Domain</button>
        <button id="tab-all" class="tab-btn">All Profiles</button>
        <button id="tab-settings" class="tab-btn">Settings</button>
      </div>
      
      <div class="tab-content">
        <div id="current-domain-tab" class="tab-panel active">
          <div class="domain-header">
            <h4>${this.currentDomain}</h4>
            <button id="add-new-profile" class="add-btn">+ Add New Profile</button>
          </div>
          
          <div id="profile-list" class="profile-list">
            <!-- Profiles will be populated here -->
          </div>
          
          <div id="profile-editor" class="profile-editor" style="display: none;">
            <!-- Profile editor will be shown here -->
          </div>
        </div>
        
        <div id="all-profiles-tab" class="tab-panel">
          <div class="search-filter">
            <input type="text" id="profile-search" placeholder="Search profiles...">
            <select id="template-filter">
              <option value="">All Templates</option>
            </select>
          </div>
          
          <div id="all-profiles-list" class="all-profiles-list">
            <!-- All profiles will be listed here -->
          </div>
        </div>
        
        <div id="settings-tab" class="tab-panel">
          <div class="settings-section">
            <h4>URL-Based Defaults</h4>
            <div id="url-defaults-list">
              <!-- URL defaults will be listed here -->
            </div>
            <button id="add-url-default" class="add-btn">+ Add URL Default</button>
          </div>
          
          <div class="settings-section">
            <h4>Import/Export</h4>
            <div class="import-export-actions">
              <button id="export-profiles">Export All Profiles</button>
              <button id="export-current-domain">Export Current Domain</button>
              <input type="file" id="import-file" accept=".json" style="display: none;">
              <button id="import-profiles">Import Profiles</button>
            </div>
          </div>
        </div>
      </div>
    `;
  }
  
  populateInterface() {
    this.populateCurrentDomainProfiles();
    this.populateAllProfiles();
    this.populateTemplateFilter();
    this.populateUrlDefaults();
  }
  
  populateCurrentDomainProfiles() {
    const profileList = document.getElementById('profile-list');
    const domainProfiles = this.profiles[this.currentDomain] || {};
    
    if (Object.keys(domainProfiles).length === 0) {
      profileList.innerHTML = `
        <div class="empty-state">
          <p>No profiles found for ${this.currentDomain}</p>
          <p>Create your first profile to get started!</p>
        </div>
      `;
      return;
    }
    
    profileList.innerHTML = Object.entries(domainProfiles)
      .map(([profileType, profile]) => this.createProfileCard(profileType, profile))
      .join('');
  }
  
  createProfileCard(profileType, profile) {
    const template = this.templates.find(t => t.id === profile.templateType);
    const templateName = template ? template.type : profile.templateType;
    
    return `
      <div class="profile-card" data-profile-type="${profileType}">
        <div class="profile-card-header">
          <h5>${profileType}</h5>
          <div class="profile-actions">
            <button class="edit-profile-btn" data-profile="${profileType}">Edit</button>
            <button class="duplicate-profile-btn" data-profile="${profileType}">Duplicate</button>
            <button class="delete-profile-btn" data-profile="${profileType}">Delete</button>
          </div>
        </div>
        
        <div class="profile-card-body">
          <div class="profile-info">
            <span class="template-type">Template: ${templateName}</span>
            <span class="last-used">Last used: ${this.formatDate(profile.lastUsed)}</span>
          </div>
          
          <div class="profile-fields">
            <strong>Mapped Fields (${Object.keys(profile.selectors).length}):</strong>
            <div class="field-list">
              ${Object.entries(profile.selectors)
                .slice(0, 3)
                .map(([field, selector]) => `
                  <div class="field-item">
                    <span class="field-name">${field}:</span>
                    <span class="field-selector">${selector}</span>
                  </div>
                `).join('')}
              ${Object.keys(profile.selectors).length > 3 ? 
                `<div class="field-more">+${Object.keys(profile.selectors).length - 3} more</div>` : ''}
            </div>
          </div>
          
          ${profile.urlPatterns && profile.urlPatterns.length > 0 ? `
            <div class="url-patterns">
              <strong>URL Patterns:</strong>
              <div class="pattern-list">
                ${profile.urlPatterns.map(pattern => `<span class="pattern">${pattern}</span>`).join('')}
              </div>
            </div>
          ` : ''}
        </div>
      </div>
    `;
  }
  
  showProfileEditor(profileType = null, profileData = null) {
    const editor = document.getElementById('profile-editor');
    const isEditing = profileType !== null;
    
    editor.innerHTML = `
      <div class="profile-editor-header">
        <h4>${isEditing ? `Edit Profile: ${profileType}` : 'Create New Profile'}</h4>
        <button id="close-profile-editor" class="close-btn">×</button>
      </div>
      
      <form id="profile-form" class="profile-form">
        <div class="form-row">
          <div class="form-group">
            <label for="profile-name">Profile Name:</label>
            <input type="text" id="profile-name" value="${profileType || ''}" 
                   ${isEditing ? 'readonly' : ''} required>
          </div>
          
          <div class="form-group">
            <label for="template-type">Template Type:</label>
            <select id="template-type" required>
              <option value="">Select Template</option>
              ${this.templates.map(template => `
                <option value="${template.id}" 
                        ${profileData?.templateType === template.id ? 'selected' : ''}>
                  ${template.type}
                </option>
              `).join('')}
            </select>
          </div>
        </div>
        
        <div class="form-group">
          <label for="url-patterns">URL Patterns (optional):</label>
          <div id="url-patterns-input">
            ${(profileData?.urlPatterns || ['']).map((pattern, index) => `
              <div class="url-pattern-row">
                <input type="text" class="url-pattern" value="${pattern}" 
                       placeholder="e.g., /blog/, /news/">
                <button type="button" class="remove-pattern-btn">Remove</button>
              </div>
            `).join('')}
          </div>
          <button type="button" id="add-url-pattern">+ Add URL Pattern</button>
        </div>
        
        <div class="field-mapping-section">
          <h5>Field Mapping</h5>
          <div id="field-mapping-list">
            <!-- Field mappings will be populated based on selected template -->
          </div>
        </div>
        
        <div class="form-actions">
          <button type="submit" class="save-btn">
            ${isEditing ? 'Update Profile' : 'Create Profile'}
          </button>
          <button type="button" id="test-profile" class="test-btn">Test Mapping</button>
          <button type="button" id="cancel-edit" class="cancel-btn">Cancel</button>
        </div>
      </form>
    `;
    
    editor.style.display = 'block';
    this.attachEditorListeners(profileData);
  }
  
  attachEditorListeners(profileData) {
    // Template type change handler
    document.getElementById('template-type').addEventListener('change', (e) => {
      this.updateFieldMapping(e.target.value, profileData);
    });
    
    // URL pattern management
    document.getElementById('add-url-pattern').addEventListener('click', () => {
      this.addUrlPatternRow();
    });
    
    document.addEventListener('click', (e) => {
      if (e.target.classList.contains('remove-pattern-btn')) {
        e.target.parentElement.remove();
      }
    });
    
    // Form submission
    document.getElementById('profile-form').addEventListener('submit', (e) => {
      e.preventDefault();
      this.saveProfile();
    });
    
    // Initialize field mapping if editing
    if (profileData?.templateType) {
      this.updateFieldMapping(profileData.templateType, profileData);
    }
  }
  
  updateFieldMapping(templateId, existingData = null) {
    const template = this.templates.find(t => t.id === templateId);
    if (!template) return;
    
    const mappingList = document.getElementById('field-mapping-list');
    const existingSelectors = existingData?.selectors || {};
    const existingTransformations = existingData?.transformations || {};
    
    mappingList.innerHTML = Object.entries(template.fields)
      .map(([fieldName, fieldConfig]) => `
        <div class="field-mapping-row">
          <div class="field-info">
            <label class="field-label">
              ${fieldName}
              ${fieldConfig.required ? '<span class="required">*</span>' : ''}
            </label>
            <span class="field-type">(${fieldConfig.type})</span>
          </div>
          
          <div class="selector-input">
            <input type="text" class="field-selector" name="selector-${fieldName}" 
                   value="${existingSelectors[fieldName] || ''}"
                   placeholder="CSS selector">
            <button type="button" class="pick-selector-btn" data-field="${fieldName}">
              Pick from Page
            </button>
          </div>
          
          <div class="transformation-input">
            <select class="field-transformation" name="transformation-${fieldName}">
              <option value="">No transformation</option>
              <option value="removeNumbers" ${existingTransformations[fieldName]?.includes('removeNumbers') ? 'selected' : ''}>Remove Numbers</option>
              <option value="trimWhitespace" ${existingTransformations[fieldName]?.includes('trimWhitespace') ? 'selected' : ''}>Trim Whitespace</option>
              <option value="sentenceCase" ${existingTransformations[fieldName]?.includes('sentenceCase') ? 'selected' : ''}>Sentence Case</option>
              <option value="formatDate" ${existingTransformations[fieldName]?.includes('formatDate') ? 'selected' : ''}>Format Date</option>
            </select>
          </div>
          
          <div class="field-preview">
            <button type="button" class="preview-field-btn" data-field="${fieldName}">
              Preview
            </button>
            <div class="preview-result" id="preview-${fieldName}"></div>
          </div>
        </div>
      `).join('');
    
    // Attach field-specific listeners
    this.attachFieldMappingListeners();
  }
  
  attachFieldMappingListeners() {
    // Pick selector from page
    document.querySelectorAll('.pick-selector-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const fieldName = e.target.dataset.field;
        this.startSelectorPicking(fieldName);
      });
    });
    
    // Preview field content
    document.querySelectorAll('.preview-field-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const fieldName = e.target.dataset.field;
        this.previewFieldContent(fieldName);
      });
    });
  }
  
  async startSelectorPicking(fieldName) {
    // Minimize profile manager
    document.getElementById('profile-manager').style.display = 'none';
    
    // Start content capture for specific field
    const capture = new ContentCapture();
    capture.currentField = fieldName;
    capture.isActive = true;
    capture.initialize();
    
    // Wait for selection
    return new Promise((resolve) => {
      const listener = (event) => {
        if (event.type === 'selectorPicked') {
          document.removeEventListener('selectorPicked', listener);
          document.getElementById('profile-manager').style.display = 'block';
          
          // Update the selector input
          const selectorInput = document.querySelector(`[name="selector-${fieldName}"]`);
          if (selectorInput) {
            selectorInput.value = event.detail.selector;
          }
          
          resolve(event.detail.selector);
        }
      };
      
      document.addEventListener('selectorPicked', listener);
    });
  }
  
  previewFieldContent(fieldName) {
    const selectorInput = document.querySelector(`[name="selector-${fieldName}"]`);
    const transformationSelect = document.querySelector(`[name="transformation-${fieldName}"]`);
    const previewDiv = document.getElementById(`preview-${fieldName}`);
    
    if (!selectorInput.value) {
      previewDiv.innerHTML = '<span class="error">No selector specified</span>';
      return;
    }
    
    try {
      const elements = document.querySelectorAll(selectorInput.value);
      if (elements.length === 0) {
        previewDiv.innerHTML = '<span class="error">No elements found</span>';
        return;
      }
      
      const element = elements[0];
      const capture = new ContentCapture();
      let content = capture.extractContent(element).text;
      
      // Apply transformation if selected
      if (transformationSelect.value) {
        content = ContentTransformations.applyTransformation(
          content, 
          transformationSelect.value
        );
      }
      
      previewDiv.innerHTML = `
        <div class="preview-success">
          <strong>Found ${elements.length} element(s)</strong><br>
          <span class="preview-content">"${content}"</span>
        </div>
      `;
    } catch (error) {
      previewDiv.innerHTML = `<span class="error">Error: ${error.message}</span>`;
    }
  }
  
  async saveProfile() {
    const form = document.getElementById('profile-form');
    const formData = new FormData(form);
    
    const profileName = formData.get('profile-name') || document.getElementById('profile-name').value;
    const templateType = formData.get('template-type') || document.getElementById('template-type').value;
    
    // Collect URL patterns
    const urlPatterns = Array.from(document.querySelectorAll('.url-pattern'))
      .map(input => input.value.trim())
      .filter(pattern => pattern.length > 0);
    
    // Collect field selectors and transformations
    const selectors = {};
    const transformations = {};
    
    document.querySelectorAll('.field-selector').forEach(input => {
      const fieldName = input.name.replace('selector-', '');
      const selectorValue = input.value.trim();
      if (selectorValue) {
        selectors[fieldName] = selectorValue;
      }
    });
    
    document.querySelectorAll('.field-transformation').forEach(select => {
      const fieldName = select.name.replace('transformation-', '');
      if (select.value) {
        transformations[fieldName] = [select.value];
      }
    });
    
    const profileData = {
      templateType,
      selectors,
      transformations,
      urlPatterns
    };
    
    try {
      await this.storage.createProfile(this.currentDomain, profileName, profileData);
      
      // Refresh the interface
      await this.loadData();
      this.populateCurrentDomainProfiles();
      
      // Hide editor
      document.getElementById('profile-editor').style.display = 'none';
      
      // Show success message
      this.showNotification('Profile saved successfully!', 'success');
    } catch (error) {
      this.showNotification(`Error saving profile: ${error.message}`, 'error');
    }
  }
}
URL-Based Defaults Management
javascript
class UrlDefaultsManager {
  constructor(storage) {
    this.storage = storage;
  }
  
  async getUrlDefaults() {
    const data = await this.storage.getStoredData();
    return data.settings?.urlBasedDefaults || {};
  }
  
  async updateUrlDefaults(defaults) {
    const data = await this.storage.getStoredData();
    if (!data.settings) {
      data.settings = {};
    }
    data.settings.urlBasedDefaults = defaults;
    await this.storage.saveData(data);
  }
  
  async addUrlDefault(pattern, profileType) {
    const defaults = await this.getUrlDefaults();
    defaults[pattern] = profileType;
    await this.updateUrlDefaults(defaults);
  }
  
  async removeUrlDefault(pattern) {
    const defaults = await this.getUrlDefaults();
    delete defaults[pattern];
    await this.updateUrlDefaults(defaults);
  }
  
  matchUrlToProfile(url, domainProfiles) {
    const defaults = this.getUrlDefaults();
    
    // Check URL patterns in order of specificity
    const sortedPatterns = Object.keys(defaults)
      .sort((a, b) => b.length - a.length); // Longer patterns first
    
    for (const pattern of sortedPatterns) {
      if (url.includes(pattern)) {
        const profileType = defaults[pattern];
        if (domainProfiles[profileType]) {
          return {
            profileType,
            profile: domainProfiles[profileType],
            matchedPattern: pattern
          };
        }
      }
    }
    
    return null;
  }
  
  createUrlDefaultsInterface() {
    return `
      <div class="url-defaults-manager">
        <div class="url-defaults-list" id="url-defaults-list">
          <!-- Will be populated dynamically -->
        </div>
        
        <div class="add-url-default">
          <h5>Add URL Default</h5>
          <div class="form-row">
            <input type="text" id="new-url-pattern" placeholder="URL pattern (e.g., /blog/)">
            <select id="new-profile-type">
              <option value="">Select Profile Type</option>
              <!-- Will be populated with available profile types -->
            </select>
            <button id="add-url-default-btn">Add</button>
          </div>
        </div>
      </div>
    `;
  }
  
  async populateUrlDefaults() {
    const defaults = await this.getUrlDefaults();
    const container = document.getElementById('url-defaults-list');
    
    if (Object.keys(defaults).length === 0) {
      container.innerHTML = '<p class="empty-state">No URL defaults configured</p>';
      return;
    }
    
    container.innerHTML = Object.entries(defaults)
      .map(([pattern, profileType]) => `
        <div class="url-default-item">
          <div class="pattern-info">
            <span class="pattern">${pattern}</span>
            <span class="arrow">→</span>
            <span class="profile-type">${profileType}</span>
          </div>
          <button class="remove-default-btn" data-pattern="${pattern}">Remove</button>
        </div>
      `).join('');
    
    // Attach remove listeners
    container.querySelectorAll('.remove-default-btn').forEach(btn => {
      btn.addEventListener('click', async (e) => {
        const pattern = e.target.dataset.pattern;
        await this.removeUrlDefault(pattern);
        this.populateUrlDefaults(); // Refresh
      });
    });
  }
}
5. popup-interface.md
Extension Popup Interface - Implementation Guide
Overview
The popup interface serves as the main control center for the Page-to-Schema Assistant, providing quick access to all major functions including template extraction, content capture, profile management, and data output.

Main Popup Structure
HTML Structure (popup.html)
xml
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Page-to-Schema Assistant</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div id="popup-container">
        <header class="popup-header">
            <div class="logo-section">
                <h1>Page-to-Schema</h1>
                <span class="version">v1.0</span>
            </div>
            <div class="status-indicator" id="status-indicator">
                <span class="status-dot"></span>
                <span class="status-text">Ready</span>
            </div>
        </header>

        <nav class="popup-nav">
            <button class="nav-btn active" data-tab="dashboard">Dashboard</button>
            <button class="nav-btn" data-tab="capture">Capture</button>
            <button class="nav-btn" data-tab="profiles">Profiles</button>
            <button class="nav-btn" data-tab="output">Output</button>
        </nav>

        <main class="popup-content">
            <!-- Dashboard Tab -->
            <div id="dashboard-tab" class="tab-content active">
                <div class="current-site-info">
                    <h3 id="current-domain">Loading...</h3>
                    <div class="quick-stats">
                        <div class="stat-item">
                            <span class="stat-value" id="profiles-count">0</span>
                            <span class="stat-label">Profiles</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value" id="templates-count">0</span>
                            <span class="stat-label">Templates</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value" id="captured-fields">0</span>
                            <span class="stat-label">Captured</span>
                        </div>
                    </div>
                </div>

                <div class="quick-actions">
                    <h4>Quick Actions</h4>
                    <div class="action-buttons">
                        <button id="extract-template" class="action-btn primary">
                            <span class="btn-icon">🔍</span>
                            Extract Template
                        </button>
                        <button id="start-capture" class="action-btn secondary">
                            <span class="btn-icon">🎯</span>
                            Start Capture
                        </button>
                        <button id="fill-fields" class="action-btn secondary">
                            <span class="btn-icon">📝</span>
                            Fill Fields
                        </button>
                    </div>
                </div>

                <div class="recent-activity">
                    <h4>Recent Activity</h4>
                    <div id="activity-list" class="activity-list">
                        <!-- Activity items will be populated here -->
                    </div>
                </div>
            </div>

            <!-- Capture Tab -->
            <div id="capture-tab" class="tab-content">
                <div class="capture-header">
                    <h3>Content Capture</h3>
                    <div class="capture-mode-toggle">
                        <label class="toggle-label">
                            <input type="radio" name="capture-mode" value="manual" checked>
                            Manual
                        </label>
                        <label class="toggle-label">
                            <input type="radio" name="capture-mode" value="auto">
                            Auto-Detect
                        </label>
                    </div>
                </div>

                <div class="profile-selection">
                    <label for="profile-select">Select Profile:</label>
                    <select id="profile-select">
                        <option value="">Choose existing profile...</option>
                    </select>
                    <button id="create-new-profile" class="link-btn">+ Create New</button>
                </div>

                <div class="template-selection" style="display: none;">
                    <label for="template-select">Template Type:</label>
                    <select id="template-select">
                        <option value="">Select template type...</option>
                    </select>
                </div>

                <div class="capture-progress" id="capture-progress" style="display: none;">
                    <div class="progress-header">
                        <h4>Capture Progress</h4>
                        <span class="progress-counter">
                            <span id="completed-fields">0</span>/<span id="total-fields">0</span>
                        </span>
                    </div>
                    
                    <div class="fields-list" id="fields-list">
                        <!-- Fields will be populated here -->
                    </div>
                </div>

                <div class="capture-actions">
                    <button id="start-manual-capture" class="action-btn primary">Start Manual Capture</button>
                    <button id="auto-detect-fields" class="action-btn secondary">Auto-Detect Fields</button>
                    <button id="save-capture-session" class="action-btn secondary" disabled>Save Session</button>
                </div>
            </div>

            <!-- Profiles Tab -->
            <div id="profiles-tab" class="tab-content">
                <div class="profiles-header">
                    <h3>Profile Management</h3>
                    <button id="import-profiles" class="link-btn">Import</button>
                    <button id="export-profiles" class="link-btn">Export</button>
                </div>

                <div class="domain-selector">
                    <label for="domain-filter">Domain:</label>
                    <select id="domain-filter">
                        <option value="">All Domains</option>
                    </select>
                </div>

                <div class="profiles-list" id="profiles-list">
                    <!-- Profiles will be populated here -->
                </div>

                <div class="profile-actions">
                    <button id="add-profile" class="action-btn primary">+ Add Profile</button>
                    <button id="manage-templates" class="action-btn secondary">Manage Templates</button>
                </div>
            </div>

            <!-- Output Tab -->
            <div id="output-tab" class="tab-content">
                <div class="output-header">
                    <h3>Generate Output</h3>
                    <div class="output-mode-toggle">
                        <label class="toggle-label">
                            <input type="radio" name="output-mode" value="fill-builder" checked>
                            Fill Builder
                        </label>
                        <label class="toggle-label">
                            <input type="radio" name="output-mode" value="json-snippet">
                            JSON Snippet
                        </label>
                    </div>
                </div>

                <div class="session-data" id="session-data">
                    <h4>Captured Data</h4>
                    <div id="captured-data-preview">
                        <p class="empty-state">No data captured yet</p>
                    </div>
                </div>

                <div class="output-options">
                    <div class="fill-builder-options" id="fill-builder-options">
                        <p>Switch to schema builder tab and click "Fill Fields" to populate your form.</p>
                        <button id="fill-builder-fields" class="action-btn primary">Fill Builder Fields</button>
                    </div>

                    <div class="json-snippet-options" id="json-snippet-options" style="display: none;">
                        <div class="snippet-settings">
                            <label for="schema-type">Schema Type:</label>
                            <select id="schema-type">
                                <option value="NewsArticle">News Article</option>
                                <option value="Recipe">Recipe</option>
                                <option value="Person">Person</option>
                                <option value="LocalBusiness">Local Business</option>
                            </select>
                        </div>
                        
                        <div class="output-format">
                            <label for="format-type">Format:</label>
                            <select id="format-type">
                                <option value="json-ld">JSON-LD</option>
                                <option value="microdata">Microdata</option>
                            </select>
                        </div>

                        <button id="generate-snippet" class="action-btn primary">Generate Snippet</button>
                    </div>
                </div>

                <div class="output-result" id="output-result" style="display: none;">
                    <div class="result-header">
                        <h4>Generated Code</h4>
                        <button id="copy-output" class="link-btn">Copy</button>
                    </div>
                    <pre id="output-code" class="code-block"></pre>
                </div>
            </div>
        </main>

        <footer class="popup-footer">
            <div class="footer-links">
                <button id="settings-btn" class="footer-btn">Settings</button>
                <button id="help-btn" class="footer-btn">Help</button>
            </div>
        </footer>
    </div>

    <script src="popup.js"></script>
</body>
</html>
Main Popup Controller (popup.js)
javascript
class PopupController {
    constructor() {
        this.storage = new SchemaStorage();
        this.profileManager = new ProfileManager();
        this.sessionManager = new SessionManager();
        this.currentTab = 'dashboard';
        this.currentDomain = null;
        this.activeSession = null;
    }

    async initialize() {
        await this.storage.initialize();
        await this.loadCurrentTabInfo();
        this.setupEventListeners();
        this.setupTabNavigation();
        await this.refreshDashboard();
    }

    async loadCurrentTabInfo() {
        // Get current tab information
        const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
        if (tabs[0]) {
            this.currentDomain = new URL(tabs[0].url).hostname;
            document.getElementById('current-domain').textContent = this.currentDomain;
        }
    }

    setupEventListeners() {
        // Quick action buttons
        document.getElementById('extract-template').addEventListener('click', () => {
            this.handleExtractTemplate();
        });

        document.getElementById('start-capture').addEventListener('click', () => {
            this.switchTab('capture');
        });

        document.getElementById('fill-fields').addEventListener('click',
you need to describe in detail what the app does, how it works overview what the purpose it what the purpose isn't

follow something like this pattern here

# Cursor's Memory Bank

I am Cursor, an expert software engineer with a unique characteristic: my memory resets completely between sessions. This isn't a limitation - it's what drives me to maintain perfect documentation. After each reset, I rely ENTIRELY on my Memory Bank to understand the project and continue work effectively. I MUST read ALL memory bank files at the start of EVERY task - this is not optional.

## Memory Bank Structure

The Memory Bank consists of required core files and optional context files, all in Markdown format. Files build upon each other in a clear hierarchy:

```mermaid
flowchart TD
PB[projectbrief.md] --> PC[productContext.md]
PB --> SP[systemPatterns.md]
PB --> TC[techContext.md]

PC --> AC[activeContext.md]
SP --> AC
TC --> AC

AC --> P[progress.md]
```

### Core Files (Required)
1. `projectbrief.md`
- Foundation document that shapes all other files
- Created at project start if it doesn't exist
- Defines core requirements and goals
- Source of truth for project scope

2. `productContext.md`
- Why this project exists
- Problems it solves
- How it should work
- User experience goals

3. `activeContext.md`
- Current work focus
- Recent changes
- Next steps
- Active decisions and considerations

4. `systemPatterns.md`
- System architecture
- Key technical decisions
- Design patterns in use
- Component relationships

5. `techContext.md`
- Technologies used
- Development setup
- Technical constraints
- Dependencies

6. `progress.md`
- What works
- What's left to build
- Current status
- Known issues

### Additional Context
Create additional files/folders within memory-bank/ when they help organize:
- Complex feature documentation
- Integration specifications
- API documentation
- Testing strategies
- Deployment procedures

## Core Workflows

### Plan Mode
```mermaid
flowchart TD
Start[Start] --> ReadFiles[Read Memory Bank]
ReadFiles --> CheckFiles{Files Complete?}

CheckFiles -->|No| Plan[Create Plan]
Plan --> Document[Document in Chat]

CheckFiles -->|Yes| Verify[Verify Context]
Verify --> Strategy[Develop Strategy]
Strategy --> Present[Present Approach]
```

### Act Mode
```mermaid
flowchart TD
Start[Start] --> Context[Check Memory Bank]
Context --> Update[Update Documentation]
Update --> Rules[Update .cursorrules if needed]
Rules --> Execute[Execute Task]
Execute --> Document[Document Changes]
```

## Documentation Updates

Memory Bank updates occur when:
1. Discovering new project patterns
2. After implementing significant changes
3. When user requests with **update memory bank** (MUST review ALL files)
4. When context needs clarification

```mermaid
flowchart TD
Start[Update Process]

subgraph Process
P1[Review ALL Files]
P2[Document Current State]
P3[Clarify Next Steps]
P4[Update .cursorrules]

P1 --> P2 --> P3 --> P4
end

Start --> Process
```

Note: When triggered by **update memory bank**, I MUST review every memory bank file, even if some don't require updates. Focus particularly on activeContext.md and progress.md as they track current state.

## Project Intelligence (.cursorrules)

The .cursorrules file is my learning journal for each project. It captures important patterns, preferences, and project intelligence that help me work more effectively. As I work with you and the project, I'll discover and document key insights that aren't obvious from the code alone.

```mermaid
flowchart TD
Start{Discover New Pattern}

subgraph Learn [Learning Process]
D1[Identify Pattern]
D2[Validate with User]
D3[Document in .cursorrules]
end

subgraph Apply [Usage]
A1[Read .cursorrules]
A2[Apply Learned Patterns]
A3[Improve Future Work]
end

Start --> Learn
Learn --> Apply
```

### What to Capture
- Critical implementation paths
- User preferences and workflow
- Project-specific patterns
- Known challenges
- Evolution of project decisions
- Tool usage patterns

The format is flexible - focus on capturing valuable insights that help me work more effectively with you and the project. Think of .cursorrules as a living document that grows smarter as we work together.

REMEMBER: After every memory reset, I begin completely fresh. The Memory Bank is my only link to previous work. It must be maintained with precision and clarity, as my effectiveness depends entirely on its accuracy.

# Planning
When asked to enter "Planner Mode" or using the /plan command, deeply reflect upon the changes being asked and analyze existing code to map the full scope of changes needed. Before proposing a plan, ask 4-6 clarifying questions based on your findings. Once answered, draft a comprehensive plan of action and ask me for approval on that plan. Once approved, implement all steps in that plan. After completing each phase/step, mention what was just completed and what the next steps are + phases remaining after these steps
you need to describe in detail what the app does, how it works overview what the purpose it what the purpose isn't follow something like this pattern here # Cursor's Memory Bank I am Cursor, an expert software engineer with a unique characteristic: my memory resets completely between sessions. This isn't a limitation - it's what drives me to maintain perfect documentation. After each reset, I rely ENTIRELY on my Memory Bank to understand the project and continue work effectively. I MUST read ALL memory bank files at the start of EVERY task - this is not optional. ## Memory Bank Structure The Memory Bank consists of required core files and optional context files, all in Markdown format. Files build upon each other in a clear hierarchy: ```mermaid flowchart TD PB[projectbrief.md] --> PC[productContext.md] PB --> SP[systemPatterns.md] PB --> TC[techContext.md] PC --> AC[activeContext.md] SP --> AC TC --> AC AC --> P[progress.md] ``` ### Core Files (Required) 1. `projectbrief.md` - Foundation document that shapes all other files - Created at project start if it doesn't exist - Defines core requirements and goals - Source of truth for project scope 2. `productContext.md` - Why this project exists - Problems it solves - How it should work - User experience goals 3. `activeContext.md` - Current work focus - Recent changes - Next steps - Active decisions and considerations 4. `systemPatterns.md` - System architecture - Key technical decisions - Design patterns in use - Component relationships 5. `techContext.md` - Technologies used - Development setup - Technical constraints - Dependencies 6. `progress.md` - What works - What's left to build - Current status - Known issues ### Additional Context Create additional files/folders within memory-bank/ when they help organize: - Complex feature documentation - Integration specifications - API documentation - Testing strategies - Deployment procedures ## Core Workflows ### Plan Mode ```mermaid flowchart TD Start[Start] --> ReadFiles[Read Memory Bank] ReadFiles --> CheckFiles{Files Complete?} CheckFiles -->|No| Plan[Create Plan] Plan --> Document[Document in Chat] CheckFiles -->|Yes| Verify[Verify Context] Verify --> Strategy[Develop Strategy] Strategy --> Present[Present Approach] ``` ### Act Mode ```mermaid flowchart TD Start[Start] --> Context[Check Memory Bank] Context --> Update[Update Documentation] Update --> Rules[Update .cursorrules if needed] Rules --> Execute[Execute Task] Execute --> Document[Document Changes] ``` ## Documentation Updates Memory Bank updates occur when: 1. Discovering new project patterns 2. After implementing significant changes 3. When user requests with **update memory bank** (MUST review ALL files) 4. When context needs clarification ```mermaid flowchart TD Start[Update Process] subgraph Process P1[Review ALL Files] P2[Document Current State] P3[Clarify Next Steps] P4[Update .cursorrules] P1 --> P2 --> P3 --> P4 end Start --> Process ``` Note: When triggered by **update memory bank**, I MUST review every memory bank file, even if some don't require updates. Focus particularly on activeContext.md and progress.md as they track current state. ## Project Intelligence (.cursorrules) The .cursorrules file is my learning journal for each project. It captures important patterns, preferences, and project intelligence that help me work more effectively. As I work with you and the project, I'll discover and document key insights that aren't obvious from the code alone. ```mermaid flowchart TD Start{Discover New Pattern} subgraph Learn [Learning Process] D1[Identify Pattern] D2[Validate with User] D3[Document in .cursorrules] end subgraph Apply [Usage] A1[Read .cursorrules] A2[Apply Learned Patterns] A3[Improve Future Work] end Start --> Learn Learn --> Apply ``` ### What to Capture - Critical implementation paths - User preferences and workflow - Project-specific patterns - Known challenges - Evolution of project decisions - Tool usage patterns The format is flexible - focus on capturing valuable insights that help me work more effectively with you and the project. Think of .cursorrules as a living document that grows smarter as we work together. REMEMBER: After every memory reset, I begin completely fresh. The Memory Bank is my only link to previous work. It must be maintained with precision and clarity, as my effectiveness depends entirely on its accuracy. # Planning When asked to enter "Planner Mode" or using the /plan command, deeply reflect upon the changes being asked and analyze existing code to map the full scope of changes needed. Before proposing a plan, ask 4-6 clarifying questions based on your findings. Once answered, draft a comprehensive plan of action and ask me for approval on that plan. Once approved, implement all steps in that plan. After completing each phase/step, mention what was just completed and what the next steps are + phases remaining after these steps
Page-to-Schema Assistant - Complete Product Overview
I am building the Page-to-Schema Assistant, a Chrome extension that automates schema markup creation for SEO specialists and web developers. This document serves as the definitive guide to understanding what this tool does, how it works, and its specific purpose in the schema markup automation ecosystem.

What The App Does
The Page-to-Schema Assistant is a universal form replacement plugin that eliminates the repetitive manual work of creating schema markup. It operates as a bridge between any schema builder platform and content on web pages, automatically mapping and transferring data with minimal user intervention.

Core Functionality
text
flowchart TD
    A[Schema Builder] -->|Extract Template| B[Universal Template]
    B -->|Map to Domain| C[Domain Profile]
    C -->|Capture Content| D[Page Data]
    D -->|Fill Forms| A
    D -->|Generate Snippet| E[JSON-LD Output]
Primary Functions:

Template Extraction - Uses intelligent bookmarklets to identify and extract field structures from any schema builder

Content Capture - Provides visual tools for selecting and scraping content from web pages

Profile Management - Stores domain-specific selector mappings and templates for reuse

Data Application - Fills schema builders automatically or generates clean JSON-LD snippets

How It Works
The Three-Phase Workflow
Phase 1: Template Creation

text
Schema Builder → Bookmarklet → Universal Template → Save for Reuse
Visit any schema builder (UpTools, SEOPress, WordPress, etc.)

Run extraction bookmarklet to identify all form fields

Creates universal template that works across platforms

No platform-specific coding required

Phase 2: Domain Mapping

text
Target Website → Content Capture → Selector Mapping → Domain Profile
Visit target content pages (Forbes, CNN, client sites)

Use visual selection tool to capture content elements

System learns and stores CSS selectors for each field type

Creates reusable profiles per domain and content type

Phase 3: Automated Population

text
Captured Data → Reverse Bookmarklet → Filled Schema Builder
OR
Captured Data → JSON Generator → Clean Schema Snippet
Return to schema builder with captured content

One-click population of all mapped fields

Alternative: Generate production-ready JSON-LD code

Technical Mechanics
Universal Field Detection:
The system uses cascading detection strategies to identify form fields across different platforms:

Associated labels by ID

Nearby labels in DOM structure

Previous sibling elements

Placeholder text fallback

Field name attributes

Smart Selector Generation:
When users click page elements, the system generates multiple selector candidates:

ID selectors (highest priority)

Class combinations

Attribute-based selectors

Positional selectors

Allows cycling through options for best match

Hierarchical Storage:

json
{
  "templates": {
    "newsarticle": { /* Universal schema fields */ }
  },
  "profiles": {
    "forbes.com": {
      "news": { /* Specific selectors for Forbes news */ },
      "recipes": { /* Different selectors for Forbes recipes */ }
    }
  }
}
What The Purpose IS
Primary Goals
Eliminate 90-100% of manual schema field population for templated content

Work universally across any schema builder platform - no vendor lock-in

Scale efficiently across multiple websites through reusable profiles

Handle repetitive content types like blog posts, videos, FAQs, person profiles

Target Use Cases
SEO Specialists creating schema for multiple client websites

Web Developers implementing structured data at scale

Content Teams who publish articles, videos, or products regularly

Agency Work where the same schema types are created repeatedly

Specific Scenarios
News websites publishing daily articles with consistent structure

Recipe sites with standardized recipe formats

Local business directories with repeated business profiles

Video platforms with consistent metadata requirements

Corporate blogs with standard author and article information

What The Purpose IS NOT
Explicit Limitations
NOT a one-time schema builder replacement - This enhances existing builders, doesn't replace them

NOT for complex schema with extensive node referencing - Use Mode 1 (return to builder) for complex relationships

NOT for unique, one-off schemas - Designed for templated, repetitive content

NOT for LocalBusiness or Organization setup - These are typically one-time configurations

Scenarios Where This Tool Doesn't Apply
Creating complex, interconnected schema entities with multiple relationships

One-time website setup schemas (Organization, LocalBusiness base configs)

Highly customized schema that varies significantly page-to-page

Sites where content structure changes frequently

Complex e-commerce with variable product attributes

Technical Constraints
Requires consistent DOM structure across similar content types

Works best with templated websites (WordPress themes, consistent CMS output)

Cannot handle dynamic content that loads after page render without additional work

Limited to content visible in the DOM (no API-sourced data)

User Workflow Example
Typical Daily Usage:

Morning Setup (5 minutes): Create template from schema builder

Initial Domain Mapping (15 minutes): Map fields on one Forbes article

Ongoing Usage (30 seconds per article): One-click capture and fill for subsequent Forbes articles

Cross-Domain Scaling: Repeat mapping process for CNN, repeat one-click usage

Time Savings Calculation:

Manual schema creation: 10-15 minutes per article

With Page-to-Schema Assistant: 30 seconds per article after initial setup

ROI achieved after processing 3-5 articles per domain

Technology Approach
Architecture Principles
Local-First: All data stored in Chrome local storage, no external dependencies

Universal Compatibility: Works with any form-based schema builder

Intelligent Automation: Smart field detection with fallback strategies

User-Controlled: Manual override available for all automated processes

Data Philosophy
Templates are Universal: NewsArticle fields are the same everywhere

Profiles are Domain-Specific: Forbes uses different selectors than CNN

Content is Ephemeral: Captured data used immediately, not stored long-term

Profiles are Persistent: Domain mappings reused indefinitely

Success Metrics
Quantitative Goals:

90-100% automated field population rate

Sub-60-second capture and fill workflow

Universal compatibility across major schema builders

Zero external API dependencies

Qualitative Outcomes:

Significant time savings for repetitive schema work

Reduced human error in schema markup

Scalable solution for agencies and multi-site operations

Lower barrier to implementing comprehensive schema markup