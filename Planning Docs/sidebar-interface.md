# Schema Speed Sidebar Interface - Implementation Guide

## Overview
The sidebar interface serves as the main control center for the Schema Speed Assistant, providing quick access to all major functions including template extraction, content capture, profile management, and data output.

## Main Sidebar Structure

### HTML Structure (sidebar.html)
```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Schema Speed Assistant</title>
    <link rel="stylesheet" href="sidebar.css">
</head>
<body>
    <div id="sidebar-container">
        <header class="sidebar-header">
            <div class="logo-section">
                <h1>Schema Speed</h1>
                <span class="version">v1.0</span>
            </div>
            <div class="status-indicator" id="status-indicator">
                <span class="status-dot"></span>
                <span class="status-text">Ready</span>
            </div>
        </header>

        <nav class="sidebar-nav">
            <button class="nav-btn active" data-tab="dashboard">Dashboard</button>
            <button class="nav-btn" data-tab="capture">Capture</button>
            <button class="nav-btn" data-tab="profiles">Profiles</button>
            <button class="nav-btn" data-tab="output">Output</button>
            <button class="nav-btn" data-tab="debug">Debug</button>
        </nav>

        <main class="sidebar-content">
            <!-- Dashboard Tab -->
            <div id="dashboard-tab" class="tab-content active">
                <div class="current-site-info">
                    <h3 id="current-domain">Loading...</h3>
                    <div class="quick-stats">
                        <div class="stat-item">
                            <span class="stat-value" id="profiles-count">0</span>
                            <span class="stat-label">Profiles</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value" id="templates-count">0</span>
                            <span class="stat-label">Templates</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value" id="captured-fields">0</span>
                            <span class="stat-label">Captured</span>
                        </div>
                    </div>
                </div>

                <div class="quick-actions">
                    <h4>Quick Actions</h4>
                    <div class="action-buttons">
                        <button id="extract-template" class="action-btn primary">
                            <span class="btn-icon">🔍</span>
                            Extract Template
                        </button>
                        <button id="start-capture" class="action-btn secondary">
                            <span class="btn-icon">🎯</span>
                            Start Capture
                        </button>
                        <button id="fill-fields" class="action-btn secondary">
                            <span class="btn-icon">📝</span>
                            Fill Fields
                        </button>
                    </div>
                </div>

                <div class="recent-activity">
                    <h4>Recent Activity</h4>
                    <div id="activity-list" class="activity-list">
                        <!-- Activity items will be populated here -->
                    </div>
                </div>
            </div>

            <!-- Capture Tab -->
            <div id="capture-tab" class="tab-content">
                <div class="capture-header">
                    <h3>Content Capture</h3>
                    <div class="capture-mode-toggle">
                        <label class="toggle-label">
                            <input type="radio" name="capture-mode" value="manual" checked>
                            Manual
                        </label>
                        <label class="toggle-label">
                            <input type="radio" name="capture-mode" value="auto">
                            Auto-Detect
                        </label>
                    </div>
                </div>

                <div class="profile-selection">
                    <label for="profile-select">Select Profile:</label>
                    <select id="profile-select">
                        <option value="">Choose existing profile...</option>
                    </select>
                    <button id="create-new-profile" class="link-btn">+ Create New</button>
                </div>

                <div class="template-selection" style="display: none;">
                    <label for="template-select">Template Type:</label>
                    <select id="template-select">
                        <option value="">Select template type...</option>
                    </select>
                </div>

                <div class="capture-progress" id="capture-progress" style="display: none;">
                    <div class="progress-header">
                        <h4>Capture Progress</h4>
                        <span class="progress-counter">
                            <span id="completed-fields">0</span>/<span id="total-fields">0</span>
                        </span>
                    </div>
                    
                    <div class="fields-list" id="fields-list">
                        <!-- Fields will be populated here -->
                    </div>
                </div>

                <div class="capture-actions">
                    <button id="start-manual-capture" class="action-btn primary">Start Manual Capture</button>
                    <button id="auto-detect-fields" class="action-btn secondary">Auto-Detect Fields</button>
                    <button id="save-capture-session" class="action-btn secondary" disabled>Save Session</button>
                </div>
            </div>

            <!-- Profiles Tab -->
            <div id="profiles-tab" class="tab-content">
                <div class="profiles-header">
                    <h3>Profile Management</h3>
                    <button id="import-profiles" class="link-btn">Import</button>
                    <button id="export-profiles" class="link-btn">Export</button>
                </div>

                <div class="domain-selector">
                    <label for="domain-filter">Domain:</label>
                    <select id="domain-filter">
                        <option value="">All Domains</option>
                    </select>
                </div>

                <div class="profiles-list" id="profiles-list">
                    <!-- Profiles will be populated here -->
                </div>

                <div class="profile-actions">
                    <button id="add-profile" class="action-btn primary">+ Add Profile</button>
                    <button id="manage-templates" class="action-btn secondary">Manage Templates</button>
                </div>
            </div>

            <!-- Output Tab -->
            <div id="output-tab" class="tab-content">
                <div class="output-header">
                    <h3>Generate Output</h3>
                    <div class="output-mode-toggle">
                        <label class="toggle-label">
                            <input type="radio" name="output-mode" value="fill-builder" checked>
                            Fill Builder
                        </label>
                        <label class="toggle-label">
                            <input type="radio" name="output-mode" value="json-snippet">
                            JSON Snippet
                        </label>
                    </div>
                </div>

                <div class="session-data" id="session-data">
                    <h4>Captured Data</h4>
                    <div id="captured-data-preview">
                        <p class="empty-state">No data captured yet</p>
                    </div>
                </div>

                <div class="output-options">
                    <div class="fill-builder-options" id="fill-builder-options">
                        <p>Switch to schema builder tab and click "Fill Fields" to populate your form.</p>
                        <button id="fill-builder-fields" class="action-btn primary">Fill Builder Fields</button>
                    </div>

                    <div class="json-snippet-options" id="json-snippet-options" style="display: none;">
                        <div class="snippet-settings">
                            <label for="schema-type">Schema Type:</label>
                            <select id="schema-type">
                                <option value="NewsArticle">News Article</option>
                                <option value="Recipe">Recipe</option>
                                <option value="Person">Person</option>
                                <option value="LocalBusiness">Local Business</option>
                            </select>
                        </div>
                        
                        <div class="output-format">
                            <label for="format-type">Format:</label>
                            <select id="format-type">
                                <option value="json-ld">JSON-LD</option>
                                <option value="microdata">Microdata</option>
                            </select>
                        </div>

                        <button id="generate-snippet" class="action-btn primary">Generate Snippet</button>
                    </div>
                </div>

                <div class="output-result" id="output-result" style="display: none;">
                    <div class="result-header">
                        <h4>Generated Code</h4>
                        <button id="copy-output" class="link-btn">Copy</button>
                    </div>
                    <pre id="output-code" class="code-block"></pre>
                </div>
            </div>

            <!-- Debug Tab -->
            <div id="debug-tab" class="tab-content">
                <!-- Debug controls will be injected here by JS -->
            </div>
        </main>

        <footer class="sidebar-footer">
            <div class="footer-links">
                <button id="settings-btn" class="footer-btn">Settings</button>
                <button id="help-btn" class="footer-btn">Help</button>
            </div>
        </footer>
    </div>

    <script src="sidebar.js"></script>
</body>
</html>
```

### Main Sidebar Controller (sidebar.js)
```javascript
class SidebarController {
    constructor() {
        this.storage = new SchemaStorage();
        this.profileManager = new ProfileManager();
        this.sessionManager = new SessionManager();
        this.currentTab = 'dashboard';
        this.currentDomain = null;
        this.activeSession = null;
    }

    async initialize() {
        await this.storage.initialize();
        await this.loadCurrentTabInfo();
        this.setupEventListeners();
        this.setupTabNavigation();
        await this.refreshDashboard();
    }

    async loadCurrentTabInfo() {
        // Get current tab information
        const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
        if (tabs[0]) {
            this.currentDomain = new URL(tabs[0].url).hostname;
            document.getElementById('current-domain').textContent = this.currentDomain;
        }
    }

    setupEventListeners() {
        // Quick action buttons
        document.getElementById('extract-template').addEventListener('click', () => {
            this.handleExtractTemplate();
        });

        document.getElementById('start-capture').addEventListener('click', () => {
            this.switchTab('capture');
        });

        document.getElementById('fill-fields').addEventListener('click', () => {
            this.handleFillFields();
        });

        // Tab navigation
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const tabName = e.target.dataset.tab;
                this.switchTab(tabName);
            });
        });
    }

    switchTab(tabName) {
        // Update navigation
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        
        const activeNavBtn = document.querySelector(`[data-tab="${tabName}"]`);
        if (activeNavBtn) {
            activeNavBtn.classList.add('active');
        }

        // Update content
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        
        const activeTabContent = document.getElementById(`${tabName}-tab`);
        if (activeTabContent) {
            activeTabContent.classList.add('active');
        }

        this.currentTab = tabName;
    }

    async refreshDashboard() {
        // Update stats
        const profiles = await this.profileManager.getDomainProfiles(this.currentDomain);
        const templates = await this.storage.getStoredData();
        const session = await this.sessionManager.getCurrentSession();

        document.getElementById('profiles-count').textContent = Object.keys(profiles).length;
        document.getElementById('templates-count').textContent = Object.keys(templates?.templates || {}).length;
        document.getElementById('captured-fields').textContent = session ? Object.keys(session.capturedData || {}).length : 0;
    }

    async handleExtractTemplate() {
        // Template extraction logic
        console.log('Extracting template...');
    }

    async handleFillFields() {
        // Field filling logic
        console.log('Filling fields...');
    }
}

// Initialize sidebar controller
const sidebarController = new SidebarController();
sidebarController.initialize();
```

## Key Features

### 1. **Tabbed Navigation**
- **Dashboard**: Overview and quick actions
- **Capture**: Content capture and field mapping
- **Profiles**: Profile management and templates
- **Output**: Data output and code generation
- **Debug**: Debugging tools and system monitoring

### 2. **Responsive Design**
- **Adaptive Layout**: Works on different screen sizes
- **Dark/Light Theme**: Theme toggle support
- **Docking Support**: Can dock to left or right side
- **Smooth Animations**: CSS transitions and animations

### 3. **Real-time Updates**
- **Live Stats**: Current domain, profiles, templates count
- **Session Management**: Active capture sessions
- **Status Indicators**: System status and progress
- **Activity Feed**: Recent actions and events

### 4. **Integration Points**
- **Content Scripts**: Communication with page content
- **Background Service**: Storage and messaging
- **Debug System**: Comprehensive logging and monitoring
- **Extension Reload**: Development and troubleshooting tools

## Implementation Notes

### File Structure
```
sidebar/
├── sidebar.html          # Main sidebar interface
├── sidebar.css           # Styling and themes
├── sidebar.js            # Controller and logic
└── sidebar-injector.js   # Content script injection
```

### Dependencies
- **Chrome Extensions API**: Tabs, storage, messaging
- **Schema Storage**: Data persistence and management
- **Debug System**: Logging and monitoring
- **Extension Reload**: Development utilities

### Browser Compatibility
- **Chrome 88+**: Manifest V3 support
- **Service Worker**: Background script compatibility
- **Content Scripts**: Sidebar injection and communication
