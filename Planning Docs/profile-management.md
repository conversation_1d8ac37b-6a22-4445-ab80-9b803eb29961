Profile Management System - Implementation Guide
Overview
The profile management system handles domain-specific configurations, URL-based defaults, profile creation/editing, and template type management with an intuitive user interface.

Profile Management Interface
Main Profile Manager Class
javascript
class ProfileManagerUI {
  constructor() {
    this.storage = new ProfileManager();
    this.currentDomain = null;
    this.profiles = {};
    this.templates = {};
  }
  
  async initialize() {
    await this.loadData();
    this.createInterface();
    this.attachEventListeners();
  }
  
  async loadData() {
    this.profiles = await this.storage.getAllProfiles();
    this.templates = await new TemplateManager().listTemplates();
    this.currentDomain = this.getCurrentDomain();
  }
  
  getCurrentDomain() {
    return new URL(window.location.href).hostname;
  }
  
  createInterface() {
    const container = document.createElement('div');
    container.id = 'profile-manager';
    container.innerHTML = this.getInterfaceHTML();
    
    // Apply styles
    this.applyStyles(container);
    
    document.body.appendChild(container);
    this.populateInterface();
  }
  
  getInterfaceHTML() {
    return `
      <div class="profile-manager-header">
        <h3>Profile Manager - ${this.currentDomain}</h3>
        <button id="close-profile-manager" class="close-btn">×</button>
      </div>
      
      <div class="profile-tabs">
        <button id="tab-current" class="tab-btn active">Current Domain</button>
        <button id="tab-all" class="tab-btn">All Profiles</button>
        <button id="tab-settings" class="tab-btn">Settings</button>
      </div>
      
      <div class="tab-content">
        <div id="current-domain-tab" class="tab-panel active">
          <div class="domain-header">
            <h4>${this.currentDomain}</h4>
            <button id="add-new-profile" class="add-btn">+ Add New Profile</button>
          </div>
          
          <div id="profile-list" class="profile-list">
            <!-- Profiles will be populated here -->
          </div>
          
          <div id="profile-editor" class="profile-editor" style="display: none;">
            <!-- Profile editor will be shown here -->
          </div>
        </div>
        
        <div id="all-profiles-tab" class="tab-panel">
          <div class="search-filter">
            <input type="text" id="profile-search" placeholder="Search profiles...">
            <select id="template-filter">
              <option value="">All Templates</option>
            </select>
          </div>
          
          <div id="all-profiles-list" class="all-profiles-list">
            <!-- All profiles will be listed here -->
          </div>
        </div>
        
        <div id="settings-tab" class="tab-panel">
          <div class="settings-section">
            <h4>URL-Based Defaults</h4>
            <div id="url-defaults-list">
              <!-- URL defaults will be listed here -->
            </div>
            <button id="add-url-default" class="add-btn">+ Add URL Default</button>
          </div>
          
          <div class="settings-section">
            <h4>Import/Export</h4>
            <div class="import-export-actions">
              <button id="export-profiles">Export All Profiles</button>
              <button id="export-current-domain">Export Current Domain</button>
              <input type="file" id="import-file" accept=".json" style="display: none;">
              <button id="import-profiles">Import Profiles</button>
            </div>
          </div>
        </div>
      </div>
    `;
  }
  
  populateInterface() {
    this.populateCurrentDomainProfiles();
    this.populateAllProfiles();
    this.populateTemplateFilter();
    this.populateUrlDefaults();
  }
  
  populateCurrentDomainProfiles() {
    const profileList = document.getElementById('profile-list');
    const domainProfiles = this.profiles[this.currentDomain] || {};
    
    if (Object.keys(domainProfiles).length === 0) {
      profileList.innerHTML = `
        <div class="empty-state">
          <p>No profiles found for ${this.currentDomain}</p>
          <p>Create your first profile to get started!</p>
        </div>
      `;
      return;
    }
    
    profileList.innerHTML = Object.entries(domainProfiles)
      .map(([profileType, profile]) => this.createProfileCard(profileType, profile))
      .join('');
  }
  
  createProfileCard(profileType, profile) {
    const template = this.templates.find(t => t.id === profile.templateType);
    const templateName = template ? template.type : profile.templateType;
    
    return `
      <div class="profile-card" data-profile-type="${profileType}">
        <div class="profile-card-header">
          <h5>${profileType}</h5>
          <div class="profile-actions">
            <button class="edit-profile-btn" data-profile="${profileType}">Edit</button>
            <button class="duplicate-profile-btn" data-profile="${profileType}">Duplicate</button>
            <button class="delete-profile-btn" data-profile="${profileType}">Delete</button>
          </div>
        </div>
        
        <div class="profile-card-body">
          <div class="profile-info">
            <span class="template-type">Template: ${templateName}</span>
            <span class="last-used">Last used: ${this.formatDate(profile.lastUsed)}</span>
          </div>
          
          <div class="profile-fields">
            <strong>Mapped Fields (${Object.keys(profile.selectors).length}):</strong>
            <div class="field-list">
              ${Object.entries(profile.selectors)
                .slice(0, 3)
                .map(([field, selector]) => `
                  <div class="field-item">
                    <span class="field-name">${field}:</span>
                    <span class="field-selector">${selector}</span>
                  </div>
                `).join('')}
              ${Object.keys(profile.selectors).length > 3 ? 
                `<div class="field-more">+${Object.keys(profile.selectors).length - 3} more</div>` : ''}
            </div>
          </div>
          
          ${profile.urlPatterns && profile.urlPatterns.length > 0 ? `
            <div class="url-patterns">
              <strong>URL Patterns:</strong>
              <div class="pattern-list">
                ${profile.urlPatterns.map(pattern => `<span class="pattern">${pattern}</span>`).join('')}
              </div>
            </div>
          ` : ''}
        </div>
      </div>
    `;
  }
  
  showProfileEditor(profileType = null, profileData = null) {
    const editor = document.getElementById('profile-editor');
    const isEditing = profileType !== null;
    
    editor.innerHTML = `
      <div class="profile-editor-header">
        <h4>${isEditing ? `Edit Profile: ${profileType}` : 'Create New Profile'}</h4>
        <button id="close-profile-editor" class="close-btn">×</button>
      </div>
      
      <form id="profile-form" class="profile-form">
        <div class="form-row">
          <div class="form-group">
            <label for="profile-name">Profile Name:</label>
            <input type="text" id="profile-name" value="${profileType || ''}" 
                   ${isEditing ? 'readonly' : ''} required>
          </div>
          
          <div class="form-group">
            <label for="template-type">Template Type:</label>
            <select id="template-type" required>
              <option value="">Select Template</option>
              ${this.templates.map(template => `
                <option value="${template.id}" 
                        ${profileData?.templateType === template.id ? 'selected' : ''}>
                  ${template.type}
                </option>
              `).join('')}
            </select>
          </div>
        </div>
        
        <div class="form-group">
          <label for="url-patterns">URL Patterns (optional):</label>
          <div id="url-patterns-input">
            ${(profileData?.urlPatterns || ['']).map((pattern, index) => `
              <div class="url-pattern-row">
                <input type="text" class="url-pattern" value="${pattern}" 
                       placeholder="e.g., /blog/, /news/">
                <button type="button" class="remove-pattern-btn">Remove</button>
              </div>
            `).join('')}
          </div>
          <button type="button" id="add-url-pattern">+ Add URL Pattern</button>
        </div>
        
        <div class="field-mapping-section">
          <h5>Field Mapping</h5>
          <div id="field-mapping-list">
            <!-- Field mappings will be populated based on selected template -->
          </div>
        </div>
        
        <div class="form-actions">
          <button type="submit" class="save-btn">
            ${isEditing ? 'Update Profile' : 'Create Profile'}
          </button>
          <button type="button" id="test-profile" class="test-btn">Test Mapping</button>
          <button type="button" id="cancel-edit" class="cancel-btn">Cancel</button>
        </div>
      </form>
    `;
    
    editor.style.display = 'block';
    this.attachEditorListeners(profileData);
  }
  
  attachEditorListeners(profileData) {
    // Template type change handler
    document.getElementById('template-type').addEventListener('change', (e) => {
      this.updateFieldMapping(e.target.value, profileData);
    });
    
    // URL pattern management
    document.getElementById('add-url-pattern').addEventListener('click', () => {
      this.addUrlPatternRow();
    });
    
    document.addEventListener('click', (e) => {
      if (e.target.classList.contains('remove-pattern-btn')) {
        e.target.parentElement.remove();
      }
    });
    
    // Form submission
    document.getElementById('profile-form').addEventListener('submit', (e) => {
      e.preventDefault();
      this.saveProfile();
    });
    
    // Initialize field mapping if editing
    if (profileData?.templateType) {
      this.updateFieldMapping(profileData.templateType, profileData);
    }
  }
  
  updateFieldMapping(templateId, existingData = null) {
    const template = this.templates.find(t => t.id === templateId);
    if (!template) return;
    
    const mappingList = document.getElementById('field-mapping-list');
    const existingSelectors = existingData?.selectors || {};
    const existingTransformations = existingData?.transformations || {};
    
    mappingList.innerHTML = Object.entries(template.fields)
      .map(([fieldName, fieldConfig]) => `
        <div class="field-mapping-row">
          <div class="field-info">
            <label class="field-label">
              ${fieldName}
              ${fieldConfig.required ? '<span class="required">*</span>' : ''}
            </label>
            <span class="field-type">(${fieldConfig.type})</span>
          </div>
          
          <div class="selector-input">
            <input type="text" class="field-selector" name="selector-${fieldName}" 
                   value="${existingSelectors[fieldName] || ''}"
                   placeholder="CSS selector">
            <button type="button" class="pick-selector-btn" data-field="${fieldName}">
              Pick from Page
            </button>
          </div>
          
          <div class="transformation-input">
            <select class="field-transformation" name="transformation-${fieldName}">
              <option value="">No transformation</option>
              <option value="removeNumbers" ${existingTransformations[fieldName]?.includes('removeNumbers') ? 'selected' : ''}>Remove Numbers</option>
              <option value="trimWhitespace" ${existingTransformations[fieldName]?.includes('trimWhitespace') ? 'selected' : ''}>Trim Whitespace</option>
              <option value="sentenceCase" ${existingTransformations[fieldName]?.includes('sentenceCase') ? 'selected' : ''}>Sentence Case</option>
              <option value="formatDate" ${existingTransformations[fieldName]?.includes('formatDate') ? 'selected' : ''}>Format Date</option>
            </select>
          </div>
          
          <div class="field-preview">
            <button type="button" class="preview-field-btn" data-field="${fieldName}">
              Preview
            </button>
            <div class="preview-result" id="preview-${fieldName}"></div>
          </div>
        </div>
      `).join('');
    
    // Attach field-specific listeners
    this.attachFieldMappingListeners();
  }
  
  attachFieldMappingListeners() {
    // Pick selector from page
    document.querySelectorAll('.pick-selector-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const fieldName = e.target.dataset.field;
        this.startSelectorPicking(fieldName);
      });
    });
    
    // Preview field content
    document.querySelectorAll('.preview-field-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const fieldName = e.target.dataset.field;
        this.previewFieldContent(fieldName);
      });
    });
  }
  
  async startSelectorPicking(fieldName) {
    // Minimize profile manager
    document.getElementById('profile-manager').style.display = 'none';
    
    // Start content capture for specific field
    const capture = new ContentCapture();
    capture.currentField = fieldName;
    capture.isActive = true;
    capture.initialize();
    
    // Wait for selection
    return new Promise((resolve) => {
      const listener = (event) => {
        if (event.type === 'selectorPicked') {
          document.removeEventListener('selectorPicked', listener);
          document.getElementById('profile-manager').style.display = 'block';
          
          // Update the selector input
          const selectorInput = document.querySelector(`[name="selector-${fieldName}"]`);
          if (selectorInput) {
            selectorInput.value = event.detail.selector;
          }
          
          resolve(event.detail.selector);
        }
      };
      
      document.addEventListener('selectorPicked', listener);
    });
  }
  
  previewFieldContent(fieldName) {
    const selectorInput = document.querySelector(`[name="selector-${fieldName}"]`);
    const transformationSelect = document.querySelector(`[name="transformation-${fieldName}"]`);
    const previewDiv = document.getElementById(`preview-${fieldName}`);
    
    if (!selectorInput.value) {
      previewDiv.innerHTML = '<span class="error">No selector specified</span>';
      return;
    }
    
    try {
      const elements = document.querySelectorAll(selectorInput.value);
      if (elements.length === 0) {
        previewDiv.innerHTML = '<span class="error">No elements found</span>';
        return;
      }
      
      const element = elements[0];
      const capture = new ContentCapture();
      let content = capture.extractContent(element).text;
      
      // Apply transformation if selected
      if (transformationSelect.value) {
        content = ContentTransformations.applyTransformation(
          content, 
          transformationSelect.value
        );
      }
      
      previewDiv.innerHTML = `
        <div class="preview-success">
          <strong>Found ${elements.length} element(s)</strong><br>
          <span class="preview-content">"${content}"</span>
        </div>
      `;
    } catch (error) {
      previewDiv.innerHTML = `<span class="error">Error: ${error.message}</span>`;
    }
  }
  
  async saveProfile() {
    const form = document.getElementById('profile-form');
    const formData = new FormData(form);
    
    const profileName = formData.get('profile-name') || document.getElementById('profile-name').value;
    const templateType = formData.get('template-type') || document.getElementById('template-type').value;
    
    // Collect URL patterns
    const urlPatterns = Array.from(document.querySelectorAll('.url-pattern'))
      .map(input => input.value.trim())
      .filter(pattern => pattern.length > 0);
    
    // Collect field selectors and transformations
    const selectors = {};
    const transformations = {};
    
    document.querySelectorAll('.field-selector').forEach(input => {
      const fieldName = input.name.replace('selector-', '');
      const selectorValue = input.value.trim();
      if (selectorValue) {
        selectors[fieldName] = selectorValue;
      }
    });
    
    document.querySelectorAll('.field-transformation').forEach(select => {
      const fieldName = select.name.replace('transformation-', '');
      if (select.value) {
        transformations[fieldName] = [select.value];
      }
    });
    
    const profileData = {
      templateType,
      selectors,
      transformations,
      urlPatterns
    };
    
    try {
      await this.storage.createProfile(this.currentDomain, profileName, profileData);
      
      // Refresh the interface
      await this.loadData();
      this.populateCurrentDomainProfiles();
      
      // Hide editor
      document.getElementById('profile-editor').style.display = 'none';
      
      // Show success message
      this.showNotification('Profile saved successfully!', 'success');
    } catch (error) {
      this.showNotification(`Error saving profile: ${error.message}`, 'error');
    }
  }
}
URL-Based Defaults Management
javascript
class UrlDefaultsManager {
  constructor(storage) {
    this.storage = storage;
  }
  
  async getUrlDefaults() {
    const data = await this.storage.getStoredData();
    return data.settings?.urlBasedDefaults || {};
  }
  
  async updateUrlDefaults(defaults) {
    const data = await this.storage.getStoredData();
    if (!data.settings) {
      data.settings = {};
    }
    data.settings.urlBasedDefaults = defaults;
    await this.storage.saveData(data);
  }
  
  async addUrlDefault(pattern, profileType) {
    const defaults = await this.getUrlDefaults();
    defaults[pattern] = profileType;
    await this.updateUrlDefaults(defaults);
  }
  
  async removeUrlDefault(pattern) {
    const defaults = await this.getUrlDefaults();
    delete defaults[pattern];
    await this.updateUrlDefaults(defaults);
  }
  
  matchUrlToProfile(url, domainProfiles) {
    const defaults = this.getUrlDefaults();
    
    // Check URL patterns in order of specificity
    const sortedPatterns = Object.keys(defaults)
      .sort((a, b) => b.length - a.length); // Longer patterns first
    
    for (const pattern of sortedPatterns) {
      if (url.includes(pattern)) {
        const profileType = defaults[pattern];
        if (domainProfiles[profileType]) {
          return {
            profileType,
            profile: domainProfiles[profileType],
            matchedPattern: pattern
          };
        }
      }
    }
    
    return null;
  }
  
  createUrlDefaultsInterface() {
    return `
      <div class="url-defaults-manager">
        <div class="url-defaults-list" id="url-defaults-list">
          <!-- Will be populated dynamically -->
        </div>
        
        <div class="add-url-default">
          <h5>Add URL Default</h5>
          <div class="form-row">
            <input type="text" id="new-url-pattern" placeholder="URL pattern (e.g., /blog/)">
            <select id="new-profile-type">
              <option value="">Select Profile Type</option>
              <!-- Will be populated with available profile types -->
            </select>
            <button id="add-url-default-btn">Add</button>
          </div>
        </div>
      </div>
    `;
  }
  
  async populateUrlDefaults() {
    const defaults = await this.getUrlDefaults();
    const container = document.getElementById('url-defaults-list');
    
    if (Object.keys(defaults).length === 0) {
      container.innerHTML = '<p class="empty-state">No URL defaults configured</p>';
      return;
    }
    
    container.innerHTML = Object.entries(defaults)
      .map(([pattern, profileType]) => `
        <div class="url-default-item">
          <div class="pattern-info">
            <span class="pattern">${pattern}</span>
            <span class="arrow">→</span>
            <span class="profile-type">${profileType}</span>
          </div>
          <button class="remove-default-btn" data-pattern="${pattern}">Remove</button>
        </div>
      `).join('');
    
    // Attach remove listeners
    container.querySelectorAll('.remove-default-btn').forEach(btn => {
      btn.addEventListener('click', async (e) => {
        const pattern = e.target.dataset.pattern;
        await this.removeUrlDefault(pattern);
        this.populateUrlDefaults(); // Refresh
      });
    });
  }
}