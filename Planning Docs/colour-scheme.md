:root {
  --background: oklch(1.0000 0 0);
  --foreground: oklch(0.2101 0.0318 264.6645);
  --card: oklch(1.0000 0 0);
  --card-foreground: oklch(0.2101 0.0318 264.6645);
  --popover: oklch(1.0000 0 0);
  --popover-foreground: oklch(0.2101 0.0318 264.6645);
  --primary: oklch(0.5413 0.2466 293.0090);
  --primary-foreground: oklch(1.0000 0 0);
  --secondary: oklch(0.9670 0.0029 264.5419);
  --secondary-foreground: oklch(0.2101 0.0318 264.6645);
  --muted: oklch(0.5510 0.0234 264.3637);
  --muted-foreground: oklch(0.5510 0.0234 264.3637);
  --accent: oklch(0.5413 0.2466 293.0090);
  --accent-foreground: oklch(1.0000 0 0);
  --destructive: oklch(0.5858 0.2220 17.5846);
  --destructive-foreground: oklch(1.0000 0 0);
  --border: oklch(0.9276 0.0058 264.5313);
  --input: oklch(0.9276 0.0058 264.5313);
  --ring: oklch(0.5413 0.2466 293.0090);
  --chart-1: oklch(0.5413 0.2466 293.0090);
  --chart-2: oklch(0.6959 0.1491 162.4796);
  --chart-3: oklch(0.6231 0.1880 259.8145);
  --chart-4: oklch(0.7686 0.1647 70.0804);
  --chart-5: oklch(0.4907 0.2412 292.5809);
  --sidebar: oklch(1.0000 0 0);
  --sidebar-foreground: oklch(0.2101 0.0318 264.6645);
  --sidebar-primary: oklch(0.5413 0.2466 293.0090);
  --sidebar-primary-foreground: oklch(1.0000 0 0);
  --sidebar-accent: oklch(0.9131 0.1869 100.4349);
  --sidebar-accent-foreground: oklch(0.2178 0 0);
  --sidebar-border: oklch(0.9276 0.0058 264.5313);
  --sidebar-ring: oklch(0.5413 0.2466 293.0090);
  --font-sans: Inter, sans-serif;
  --font-serif: Playfair Display, serif;
  --font-mono: Inter, sans-serif;
  --radius: 0.5rem;
  --shadow-2xs: 0 1 1 0 hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1 1 0 hsl(0 0% 0% / 0.05);
  --shadow-sm: 0 1 1 0 hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow: 0 1 1 0 hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow-md: 0 1 1 0 hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0 1 1 0 hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0 1 1 0 hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0 1 1 0 hsl(0 0% 0% / 0.25);
  --spacing: undefined;
}

.dark {
  --background: oklch(0.2178 0 0);
  --foreground: oklch(0.9288 0.0126 255.5078);
  --card: oklch(0.2103 0.0059 285.8852);
  --card-foreground: oklch(1.0000 0 0);
  --popover: oklch(0.2103 0.0059 285.8852);
  --popover-foreground: oklch(1.0000 0 0);
  --primary: oklch(0.5413 0.2466 293.0090);
  --primary-foreground: oklch(1.0000 0 0);
  --secondary: oklch(0.2931 0 0);
  --secondary-foreground: ;
  --muted: oklch(0.5510 0.0234 264.3637);
  --muted-foreground: oklch(0.7137 0.0192 261.3246);
  --accent: ;
  --accent-foreground: oklch(1.0000 0 0);
  --destructive: oklch(0.5858 0.2220 17.5846);
  --destructive-foreground: oklch(1.0000 0 0);
  --border: oklch(0.2739 0.0055 286.0326);
  --input: oklch(0.2739 0.0055 286.0326);
  --ring: oklch(0.5413 0.2466 293.0090);
  --chart-1: oklch(0.5413 0.2466 293.0090);
  --chart-2: oklch(0.6959 0.1491 162.4796);
  --chart-3: oklch(0.6231 0.1880 259.8145);
  --chart-4: oklch(0.7686 0.1647 70.0804);
  --chart-5: oklch(0.4907 0.2412 292.5809);
  --sidebar: oklch(0.2103 0.0059 285.8852);
  --sidebar-foreground: oklch(0.9288 0.0126 255.5078);
  --sidebar-primary: oklch(0.5413 0.2466 293.0090);
  --sidebar-primary-foreground: oklch(1.0000 0 0);
  --sidebar-accent: oklch(0.2931 0 0);
  --sidebar-accent-foreground: oklch(1.0000 0 0);
  --sidebar-border: oklch(0.2739 0.0055 286.0326);
  --sidebar-ring: oklch(0.5413 0.2466 293.0090);
  --font-sans: Inter, sans-serif;
  --font-serif: Playfair Display, serif;
  --font-mono: Inter, sans-serif;
  --radius: 0.5rem;
  --shadow-2xs: 0 1 1 0 hsl(0 0% 0% / 0.25);
  --shadow-xs: 0 1 1 0 hsl(0 0% 0% / 0.25);
  --shadow-sm: 0 1 1 0 hsl(0 0% 0% / 0.50), 0 1px 2px -1px hsl(0 0% 0% / 0.50);
  --shadow: 0 1 1 0 hsl(0 0% 0% / 0.50), 0 1px 2px -1px hsl(0 0% 0% / 0.50);
  --shadow-md: 0 1 1 0 hsl(0 0% 0% / 0.50), 0 2px 4px -1px hsl(0 0% 0% / 0.50);
  --shadow-lg: 0 1 1 0 hsl(0 0% 0% / 0.50), 0 4px 6px -1px hsl(0 0% 0% / 0.50);
  --shadow-xl: 0 1 1 0 hsl(0 0% 0% / 0.50), 0 8px 10px -1px hsl(0 0% 0% / 0.50);
  --shadow-2xl: 0 1 1 0 hsl(0 0% 0% / 1.25);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
}