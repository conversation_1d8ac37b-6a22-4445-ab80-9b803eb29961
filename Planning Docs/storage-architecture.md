Storage Architecture - Implementation Guide
Overview
The storage system uses a hierarchical JSON structure stored in Chrome's local storage, organizing data into universal templates, domain profiles, and captured content with full export/import capabilities.

Data Structure Design
Master Storage Schema
javascript
const STORAGE_SCHEMA = {
  version: "1.0",
  templates: {
    // Universal schema templates (same across all websites)
    "newsarticle": {
      type: "NewsArticle",
      fields: {
        "headline": { required: true, type: "text" },
        "author": { required: true, type: "text" },
        "datepublished": { required: true, type: "date" },
        "image": { required: false, type: "url" },
        "publisher": { required: true, type: "text" }
      },
      createdAt: "2025-06-29T09:12:00Z"
    },
    "recipe": {
      type: "Recipe",
      fields: {
        "name": { required: true, type: "text" },
        "description": { required: true, type: "text" },
        "preptime": { required: false, type: "duration" },
        "cooktime": { required: false, type: "duration" },
        "ingredients": { required: true, type: "array" }
      },
      createdAt: "2025-06-29T09:12:00Z"
    }
  },
  profiles: {
    // Domain-specific selector mappings
    "forbes.com": {
      "news": {
        templateType: "newsarticle",
        selectors: {
          "headline": "h1.headline",
          "author": ".author-name",
          "datepublished": ".publish-date",
          "image": ".featured-image img",
          "publisher": ".site-name"
        },
        transformations: {
          "datepublished": ["formatDate"],
          "image": ["extractSrc"]
        },
        urlPatterns: ["/news/", "/breaking/"],
        createdAt: "2025-06-29T09:12:00Z",
        lastUsed: "2025-06-29T09:12:00Z"
      },
      "recipes": {
        templateType: "recipe",
        selectors: {
          "name": ".recipe-title",
          "description": ".recipe-description",
          "preptime": ".prep-time",
          "cooktime": ".cook-time",
          "ingredients": ".ingredient-list li"
        },
        urlPatterns: ["/recipes/", "/food/"]
      }
    },
    "cnn.com": {
      "news": {
        templateType: "newsarticle",
        selectors: {
          "headline": ".pg-headline",
          "author": ".byline__name",
          "datepublished": ".timestamp",
          "image": ".media__image img"
        }
      }
    }
  },
  sessions: {
    // Current capture session data
    "current": {
      domain: "forbes.com",
      profileType: "news",
      capturedData: {
        "headline": {
          value: "Senate Plans To Vote On Trump's Signature Spending Bill...",
          selector: "h1.headline",
          timestamp: "2025-06-29T09:12:00Z"
        },
        "author": {
          value: "Antonio Pequeno IV",
          selector: ".author-name",
          timestamp: "2025-06-29T09:12:00Z"
        }
      },
      startedAt: "2025-06-29T09:12:00Z"
    }
  },
  settings: {
    defaultProfile: "news",
    autoDetectProfileType: true,
    urlBasedDefaults: {
      "/blog/": "news",
      "/recipes/": "recipe",
      "/location/": "localbusiness"
    },
    transformationSettings: {
      dateFormat: "ISO8601",
      textCleaning: true
    }
  }
};
Storage Management Implementation
Core Storage Functions
javascript
class SchemaStorage {
  constructor() {
    this.storageKey = 'pageToSchemaData';
    this.initialized = false;
  }
  
  async initialize() {
    if (this.initialized) return;
    
    const stored = await this.getStoredData();
    if (!stored || !stored.version) {
      await this.createInitialStorage();
    } else if (stored.version !== STORAGE_SCHEMA.version) {
      await this.migrateStorage(stored);
    }
    
    this.initialized = true;
  }
  
  async getStoredData() {
    return new Promise((resolve) => {
      chrome.storage.local.get([this.storageKey], (result) => {
        resolve(result[this.storageKey] || null);
      });
    });
  }
  
  async saveData(data) {
    return new Promise((resolve) => {
      chrome.storage.local.set({ [this.storageKey]: data }, () => {
        resolve();
      });
    });
  }
  
  async createInitialStorage() {
    const initialData = {
      ...STORAGE_SCHEMA,
      templates: {},
      profiles: {},
      sessions: {},
      createdAt: new Date().toISOString()
    };
    
    await this.saveData(initialData);
  }
}
Template Management
javascript
class TemplateManager extends SchemaStorage {
  async createTemplate(templateData) {
    const data = await this.getStoredData();
    const templateId = this.generateTemplateId(templateData.type);
    
    data.templates[templateId] = {
      ...templateData,
      createdAt: new Date().toISOString(),
      id: templateId
    };
    
    await this.saveData(data);
    return templateId;
  }
  
  async getTemplate(templateId) {
    const data = await this.getStoredData();
    return data.templates[templateId] || null;
  }
  
  async updateTemplate(templateId, updates) {
    const data = await this.getStoredData();
    if (data.templates[templateId]) {
      data.templates[templateId] = {
        ...data.templates[templateId],
        ...updates,
        updatedAt: new Date().toISOString()
      };
      await this.saveData(data);
      return true;
    }
    return false;
  }
  
  async deleteTemplate(templateId) {
    const data = await this.getStoredData();
    if (data.templates[templateId]) {
      delete data.templates[templateId];
      await this.saveData(data);
      return true;
    }
    return false;
  }
  
  async listTemplates() {
    const data = await this.getStoredData();
    return Object.keys(data.templates).map(id => ({
      id,
      ...data.templates[id]
    }));
  }
  
  generateTemplateId(type) {
    return type.toLowerCase().replace(/[^a-z0-9]/g, '');
  }
}
Profile Management
javascript
class ProfileManager extends SchemaStorage {
  async createProfile(domain, profileType, profileData) {
    const data = await this.getStoredData();
    
    if (!data.profiles[domain]) {
      data.profiles[domain] = {};
    }
    
    data.profiles[domain][profileType] = {
      ...profileData,
      createdAt: new Date().toISOString(),
      lastUsed: new Date().toISOString()
    };
    
    await this.saveData(data);
    return { domain, profileType };
  }
  
  async getProfile(domain, profileType) {
    const data = await this.getStoredData();
    return data.profiles[domain]?.[profileType] || null;
  }
  
  async updateProfile(domain, profileType, updates) {
    const data = await this.getStoredData();
    if (data.profiles[domain]?.[profileType]) {
      data.profiles[domain][profileType] = {
        ...data.profiles[domain][profileType],
        ...updates,
        updatedAt: new Date().toISOString(),
        lastUsed: new Date().toISOString()
      };
      await this.saveData(data);
      return true;
    }
    return false;
  }
  
  async deleteProfile(domain, profileType) {
    const data = await this.getStoredData();
    if (data.profiles[domain]?.[profileType]) {
      delete data.profiles[domain][profileType];
      
      // Clean up empty domain entries
      if (Object.keys(data.profiles[domain]).length === 0) {
        delete data.profiles[domain];
      }
      
      await this.saveData(data);
      return true;
    }
    return false;
  }
  
  async getDomainProfiles(domain) {
    const data = await this.getStoredData();
    return data.profiles[domain] || {};
  }
  
  async getAllProfiles() {
    const data = await this.getStoredData();
    return data.profiles;
  }
  
  async getDefaultProfile(url) {
    const data = await this.getStoredData();
    const domain = new URL(url).hostname;
    
    // Check URL-based defaults
    for (const [pattern, profileType] of Object.entries(data.settings.urlBasedDefaults)) {
      if (url.includes(pattern)) {
        const profile = await this.getProfile(domain, profileType);
        if (profile) return { domain, profileType, profile };
      }
    }
    
    // Return most recently used profile for domain
    const domainProfiles = await this.getDomainProfiles(domain);
    if (Object.keys(domainProfiles).length > 0) {
      const mostRecent = Object.entries(domainProfiles)
        .sort(([,a], [,b]) => new Date(b.lastUsed) - new Date(a.lastUsed))[0];
      return {
        domain,
        profileType: mostRecent[0],
        profile: mostRecent[1]
      };
    }
    
    return null;
  }
}
Session Management
javascript
class SessionManager extends SchemaStorage {
  async startSession(domain, profileType) {
    const data = await this.getStoredData();
    
    data.sessions.current = {
      domain,
      profileType,
      capturedData: {},
      startedAt: new Date().toISOString()
    };
    
    await this.saveData(data);
    return data.sessions.current;
  }
  
  async updateCapturedData(fieldName, fieldData) {
    const data = await this.getStoredData();
    
    if (!data.sessions.current) {
      throw new Error('No active session');
    }
    
    data.sessions.current.capturedData[fieldName] = {
      ...fieldData,
      timestamp: new Date().toISOString()
    };
    
    await this.saveData(data);
    return data.sessions.current.capturedData[fieldName];
  }
  
  async getCurrentSession() {
    const data = await this.getStoredData();
    return data.sessions.current || null;
  }
  
  async clearSession() {
    const data = await this.getStoredData();
    data.sessions.current = null;
    await this.saveData(data);
  }
  
  async saveSessionAsProfile(profileName) {
    const session = await this.getCurrentSession();
    if (!session) throw new Error('No active session');
    
    const profileData = {
      templateType: session.profileType,
      selectors: {},
      transformations: {}
    };
    
    // Extract selectors from captured data
    Object.entries(session.capturedData).forEach(([fieldName, fieldData]) => {
      profileData.selectors[fieldName] = fieldData.selector;
      if (fieldData.transformations) {
        profileData.transformations[fieldName] = fieldData.transformations;
      }
    });
    
    const profileManager = new ProfileManager();
    return await profileManager.createProfile(session.domain, profileName, profileData);
  }
}
Export/Import System
javascript
class ImportExportManager extends SchemaStorage {
  async exportData(includeTemplates = true, includeProfiles = true, includeSessions = false) {
    const data = await this.getStoredData();
    const exportData = {
      version: data.version,
      exportedAt: new Date().toISOString()
    };
    
    if (includeTemplates) {
      exportData.templates = data.templates;
    }
    
    if (includeProfiles) {
      exportData.profiles = data.profiles;
    }
    
    if (includeSessions) {
      exportData.sessions = data.sessions;
    }
    
    return JSON.stringify(exportData, null, 2);
  }
  
  async importData(jsonData, mergeStrategy = 'overwrite') {
    const importData = JSON.parse(jsonData);
    const currentData = await this.getStoredData();
    
    if (mergeStrategy === 'overwrite') {
      // Replace existing data
      const newData = {
        ...currentData,
        ...importData,
        importedAt: new Date().toISOString()
      };
      await this.saveData(newData);
    } else if (mergeStrategy === 'merge') {
      // Merge data, keeping existing when conflicts
      const newData = {
        ...currentData,
        importedAt: new Date().toISOString()
      };
      
      if (importData.templates) {
        newData.templates = { ...newData.templates, ...importData.templates };
      }
      
      if (importData.profiles) {
        Object.entries(importData.profiles).forEach(([domain, profiles]) => {
          if (!newData.profiles[domain]) {
            newData.profiles[domain] = {};
          }
          newData.profiles[domain] = { ...newData.profiles[domain], ...profiles };
        });
      }
      
      await this.saveData(newData);
    }
    
    return true;
  }
  
  async exportProfile(domain, profileType) {
    const profile = await new ProfileManager().getProfile(domain, profileType);
    if (!profile) return null;
    
    return JSON.stringify({
      domain,
      profileType,
      profile,
      exportedAt: new Date().toISOString()
    }, null, 2);
  }
}