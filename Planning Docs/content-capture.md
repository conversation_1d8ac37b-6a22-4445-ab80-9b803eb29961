Content Capture System - Implementation Guide
Overview
The content capture system enables users to interactively select and extract content from web pages using a visual interface with selector cycling, real-time preview, and advanced content transformations.

Core Capture Interface
Visual Selection System
javascript
class ContentCapture {
  constructor() {
    this.isActive = false;
    this.currentField = null;
    this.highlightedElement = null;
    this.selectorCandidates = [];
    this.currentSelectorIndex = 0;
    this.overlay = null;
    this.previewModal = null;
  }
  
  initialize() {
    this.createOverlayElements();
    this.attachEventListeners();
    this.loadCaptureSession();
  }
  
  createOverlayElements() {
    // Create main overlay
    this.overlay = document.createElement('div');
    this.overlay.id = 'schema-capture-overlay';
    this.overlay.innerHTML = `
      <div class="capture-toolbar">
        <div class="field-selector">
          <select id="field-dropdown">
            <option value="">Select Field to Capture</option>
          </select>
        </div>
        <div class="capture-modes">
          <button id="auto-capture" class="mode-btn">Auto Detect</button>
          <button id="manual-capture" class="mode-btn active">Manual Select</button>
        </div>
        <div class="actions">
          <button id="clear-selection">Clear</button>
          <button id="save-profile">Save Profile</button>
          <button id="close-capture">Done</button>
        </div>
      </div>
      <div class="selection-info" id="selection-info" style="display: none;">
        <div class="selector-cycling">
          <button id="prev-selector">◀</button>
          <span id="selector-display"></span>
          <button id="next-selector">▶</button>
        </div>
        <div class="preview-content" id="preview-content"></div>
        <div class="transformation-options" id="transformation-options">
          <select id="transformation-select">
            <option value="">No Transformation</option>
            <option value="removeNumbers">Remove Numbers</option>
            <option value="removeSpecific">Remove Specific String</option>
            <option value="trimStart">Remove First N Characters</option>
            <option value="trimEnd">Remove Last N Characters</option>
            <option value="patternMatch">Pattern Matching</option>
            <option value="custom">Custom Transformation</option>
          </select>
        </div>
        <div class="capture-actions">
          <button id="confirm-capture">Capture This</button>
          <button id="cancel-capture">Cancel</button>
        </div>
      </div>
    `;
    
    // Apply styles
    this.overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.1);
      z-index: 10000;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;
    
    document.body.appendChild(this.overlay);
    this.attachOverlayListeners();
  }
  
  attachEventListeners() {
    // Prevent page interactions while capturing
    document.addEventListener('click', this.handleClick.bind(this), true);
    document.addEventListener('mouseover', this.handleMouseOver.bind(this), true);
    document.addEventListener('mouseout', this.handleMouseOut.bind(this), true);
    document.addEventListener('keydown', this.handleKeyDown.bind(this), true);
  }
  
  handleClick(event) {
    if (!this.isActive) return;
    
    // Don't interfere with overlay interactions
    if (this.overlay.contains(event.target)) return;
    
    event.preventDefault();
    event.stopPropagation();
    
    if (this.currentField) {
      this.selectElement(event.target);
    }
  }
  
  handleMouseOver(event) {
    if (!this.isActive || !this.currentField) return;
    if (this.overlay.contains(event.target)) return;
    
    this.highlightElement(event.target);
  }
  
  highlightElement(element) {
    // Remove previous highlight
    if (this.highlightedElement) {
      this.highlightedElement.style.outline = '';
    }
    
    // Add highlight to current element
    element.style.outline = '2px solid #007cba';
    element.style.outlineOffset = '2px';
    this.highlightedElement = element;
  }
  
  selectElement(element) {
    this.generateSelectorCandidates(element);
    this.currentSelectorIndex = 0;
    this.showSelectionInterface(element);
  }
  
  generateSelectorCandidates(element) {
    const candidates = [];
    
    // ID selector (highest priority)
    if (element.id) {
      candidates.push({
        selector: `#${element.id}`,
        specificity: 100,
        type: 'id'
      });
    }
    
    // Class selectors
    if (element.className && typeof element.className === 'string') {
      const classes = element.className.split(' ').filter(c => c.length > 0);
      if (classes.length > 0) {
        candidates.push({
          selector: `.${classes.join('.')}`,
          specificity: 80,
          type: 'class'
        });
        
        // Single class selectors
        classes.forEach(cls => {
          candidates.push({
            selector: `.${cls}`,
            specificity: 60,
            type: 'single-class'
          });
        });
      }
    }
    
    // Attribute selectors
    const importantAttrs = ['data-testid', 'data-cy', 'name', 'data-field'];
    importantAttrs.forEach(attr => {
      if (element.hasAttribute(attr)) {
        candidates.push({
          selector: `[${attr}="${element.getAttribute(attr)}"]`,
          specificity: 70,
          type: 'attribute'
        });
      }
    });
    
    // Tag + class combinations
    if (element.className) {
      const classes = element.className.split(' ').filter(c => c.length > 0);
      if (classes.length > 0) {
        candidates.push({
          selector: `${element.tagName.toLowerCase()}.${classes[0]}`,
          specificity: 50,
          type: 'tag-class'
        });
      }
    }
    
    // Positional selectors
    const parent = element.parentElement;
    if (parent) {
      const siblings = Array.from(parent.children);
      const index = siblings.indexOf(element);
      
      candidates.push({
        selector: `${element.tagName.toLowerCase()}:nth-child(${index + 1})`,
        specificity: 30,
        type: 'position'
      });
      
      // Tag-specific position
      const tagSiblings = siblings.filter(el => el.tagName === element.tagName);
      if (tagSiblings.length > 1) {
        const tagIndex = tagSiblings.indexOf(element);
        candidates.push({
          selector: `${element.tagName.toLowerCase()}:nth-of-type(${tagIndex + 1})`,
          specificity: 25,
          type: 'tag-position'
        });
      }
    }
    
    // Tag selector (lowest priority)
    candidates.push({
      selector: element.tagName.toLowerCase(),
      specificity: 10,
      type: 'tag'
    });
    
    // Sort by specificity (highest first)
    this.selectorCandidates = candidates
      .sort((a, b) => b.specificity - a.specificity)
      .filter(candidate => {
        // Test if selector actually works and is unique enough
        const matchedElements = document.querySelectorAll(candidate.selector);
        return matchedElements.length > 0 && matchedElements.length < 10;
      });
  }
  
  showSelectionInterface(element) {
    const selectionInfo = document.getElementById('selection-info');
    const previewContent = document.getElementById('preview-content');
    const selectorDisplay = document.getElementById('selector-display');
    
    selectionInfo.style.display = 'block';
    
    this.updateSelectorDisplay();
    this.updatePreviewContent(element);
  }
  
  updateSelectorDisplay() {
    const selectorDisplay = document.getElementById('selector-display');
    const current = this.selectorCandidates[this.currentSelectorIndex];
    
    if (current) {
      selectorDisplay.textContent = `${current.selector} (${current.type})`;
    }
  }
  
  updatePreviewContent(element) {
    const previewContent = document.getElementById('preview-content');
    const current = this.selectorCandidates[this.currentSelectorIndex];
    
    if (!current) return;
    
    // Get all elements that match current selector
    const matchedElements = document.querySelectorAll(current.selector);
    const content = this.extractContent(element);
    
    previewContent.innerHTML = `
      <div class="preview-stats">
        Matches: ${matchedElements.length} elements
      </div>
      <div class="preview-value">
        <strong>Content:</strong> "${content.text}"
      </div>
      ${content.url ? `<div class="preview-url"><strong>URL:</strong> ${content.url}</div>` : ''}
      ${content.alt ? `<div class="preview-alt"><strong>Alt:</strong> ${content.alt}</div>` : ''}
    `;
  }
  
  extractContent(element) {
    const result = {
      text: '',
      url: '',
      alt: ''
    };
    
    // Extract text content
    if (element.tagName === 'IMG') {
      result.text = element.src;
      result.url = element.src;
      result.alt = element.alt;
    } else if (element.tagName === 'A') {
      result.text = element.textContent.trim();
      result.url = element.href;
    } else if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
      result.text = element.value;
    } else {
      result.text = element.textContent.trim();
    }
    
    // Check for data attributes
    const dataAttrs = ['data-url', 'data-href', 'data-src'];
    dataAttrs.forEach(attr => {
      if (element.hasAttribute(attr) && !result.url) {
        result.url = element.getAttribute(attr);
      }
    });
    
    return result;
  }
}
Content Transformation System
javascript
class ContentTransformations {
  static applyTransformation(content, transformationType, options = {}) {
    switch (transformationType) {
      case 'removeNumbers':
        return content.replace(/\d+/g, '');
      
      case 'removeSpecific':
        const removeString = options.removeString || '';
        return content.replace(new RegExp(removeString, 'gi'), '');
      
      case 'trimStart':
        const startChars = parseInt(options.startChars) || 0;
        return content.substring(startChars);
      
      case 'trimEnd':
        const endChars = parseInt(options.endChars) || 0;
        return content.substring(0, content.length - endChars);
      
      case 'patternMatch':
        const pattern = options.pattern || '';
        const match = content.match(new RegExp(pattern, 'i'));
        return match ? match[0] : content;
      
      case 'sentenceCase':
        return content.charAt(0).toUpperCase() + content.slice(1).toLowerCase();
      
      case 'titleCase':
        return content.replace(/\w\S*/g, (txt) => 
          txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
        );
      
      case 'removeHtml':
        return content.replace(/<[^>]*>/g, '');
      
      case 'extractUrl':
        const urlMatch = content.match(/https?:\/\/[^\s]+/);
        return urlMatch ? urlMatch[0] : content;
      
      case 'formatDate':
        return this.formatDate(content, options.dateFormat || 'ISO8601');
      
      case 'custom':
        // Execute custom JavaScript transformation
        if (options.customFunction) {
          try {
            return new Function('content', options.customFunction)(content);
          } catch (error) {
            console.error('Custom transformation error:', error);
            return content;
          }
        }
        return content;
      
      default:
        return content;
    }
  }
  
  static formatDate(dateString, format) {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return dateString;
    
    switch (format) {
      case 'ISO8601':
        return date.toISOString();
      case 'YYYY-MM-DD':
        return date.toISOString().split('T')[0];
      case 'MM/DD/YYYY':
        return date.toLocaleDateString('en-US');
      case 'DD/MM/YYYY':
        return date.toLocaleDateString('en-GB');
      default:
        return date.toISOString();
    }
  }
  
  static getTransformationOptions(transformationType) {
    const options = {
      removeSpecific: [
        { key: 'removeString', label: 'String to Remove', type: 'text', required: true }
      ],
      trimStart: [
        { key: 'startChars', label: 'Number of Characters', type: 'number', required: true }
      ],
      trimEnd: [
        { key: 'endChars', label: 'Number of Characters', type: 'number', required: true }
      ],
      patternMatch: [
        { key: 'pattern', label: 'RegEx Pattern', type: 'text', required: true }
      ],
      formatDate: [
        { 
          key: 'dateFormat', 
          label: 'Date Format', 
          type: 'select', 
          options: [
            { value: 'ISO8601', label: 'ISO 8601 (2025-06-29T09:12:00Z)' },
            { value: 'YYYY-MM-DD', label: 'YYYY-MM-DD' },
            { value: 'MM/DD/YYYY', label: 'MM/DD/YYYY' },
            { value: 'DD/MM/YYYY', label: 'DD/MM/YYYY' }
          ],
          required: true 
        }
      ],
      custom: [
        { key: 'customFunction', label: 'JavaScript Function Body', type: 'textarea', required: true }
      ]
    };
    
    return options[transformationType] || [];
  }
}
Auto-Detection System
javascript
class AutoDetection {
  constructor() {
    this.commonSelectors = {
      headline: [
        'h1', '.headline', '.title', '.article-title', '.post-title',
        '[data-testid*="headline"]', '.entry-title', '.page-title'
      ],
      author: [
        '.author', '.byline', '.writer', '.author-name', '.by-author',
        '[data-testid*="author"]', '.article-author', '.post-author'
      ],
      date: [
        '.date', '.publish-date', '.timestamp', '.article-date',
        '[datetime]', '.entry-date', '.post-date', 'time'
      ],
      image: [
        '.featured-image img', '.article-image img', '.hero-image img',
        '.post-thumbnail img', '.main-image img'
      ],
      description: [
        '.excerpt', '.summary', '.description', '.article-excerpt',
        '.post-excerpt', '.meta-description'
      ]
    };
  }
  
  async detectCommonFields(template) {
    const detected = {};
    
    for (const [fieldName, selectors] of Object.entries(this.commonSelectors)) {
      if (template.fields[fieldName]) {
        const element = this.findBestMatch(selectors);
        if (element) {
          detected[fieldName] = {
            element,
            selector: this.getBestSelector(element),
            content: this.extractContent(element),
            confidence: this.calculateConfidence(element, fieldName)
          };
        }
      }
    }
    
    return detected;
  }
  
  findBestMatch(selectors) {
    for (const selector of selectors) {
      const elements = document.querySelectorAll(selector);
      if (elements.length === 1) {
        return elements[0]; // Perfect match - single element
      } else if (elements.length > 1) {
        // Return the most prominent element (largest, highest on page)
        return this.selectBestCandidate(elements);
      }
    }
    return null;
  }
  
  selectBestCandidate(elements) {
    return Array.from(elements)
      .map(el => ({
        element: el,
        score: this.calculateElementScore(el)
      }))
      .sort((a, b) => b.score - a.score)[0]?.element;
  }
  
  calculateElementScore(element) {
    let score = 0;
    
    // Position score (higher on page = higher score)
    const rect = element.getBoundingClientRect();
    score += Math.max(0, 1000 - rect.top);
    
    // Size score (larger elements = higher score)
    score += rect.width * rect.height / 1000;
    
    // Content quality score
    const text = element.textContent.trim();
    if (text.length > 10 && text.length < 200) {
      score += 100; // Good content length
    }
    
    // Semantic score (better tags = higher score)
    const tagScores = {
      'h1': 100, 'h2': 80, 'h3': 60,
      'article': 50, 'header': 40, 'main': 30
    };
    score += tagScores[element.tagName.toLowerCase()] || 0;
    
    return score;
  }
  
  calculateConfidence(element, fieldName) {
    let confidence = 0.5; // Base confidence
    
    // Semantic matching
    const text = element.textContent.toLowerCase();
    const className = element.className.toLowerCase();
    const id = element.id.toLowerCase();
    
    const keywords = {
      headline: ['title', 'headline', 'heading'],
      author: ['author', 'writer', 'by'],
      date: ['date', 'time', 'publish'],
      image: ['image', 'photo', 'picture'],
      description: ['description', 'excerpt', 'summary']
    };
    
    const fieldKeywords = keywords[fieldName] || [];
    fieldKeywords.forEach(keyword => {
      if (text.includes(keyword) || className.includes(keyword) || id.includes(keyword)) {
        confidence += 0.2;
      }
    });
    
    return Math.min(confidence, 1.0);
  }
  
  getBestSelector(element) {
    // Use the same selector generation logic from content capture
    const capture = new ContentCapture();
    capture.generateSelectorCandidates(element);
    return capture.selectorCandidates[0]?.selector || element.tagName.toLowerCase();
  }
  
  extractContent(element) {
    const capture = new ContentCapture();
    return capture.extractContent(element);
  }
}