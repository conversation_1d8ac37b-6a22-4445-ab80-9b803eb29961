{"manifest_version": 3, "name": "<PERSON><PERSON><PERSON> Assistant", "version": "1.0.0", "description": "Universal form replacement plugin that automates schema markup creation for SEO specialists and web developers", "permissions": ["storage", "activeTab", "scripting", "clipboardWrite"], "host_permissions": ["<all_urls>"], "background": {"service_worker": "background.js"}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["content-scripts/content-capture.js", "content-scripts/sidebar-injector.js"], "css": ["styles/content-overlay.css"], "run_at": "document_end"}], "action": {"default_title": "<PERSON><PERSON><PERSON> Assistant", "default_icon": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}, "icons": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}, "web_accessible_resources": [{"resources": ["styles/*", "scripts/*", "popout/*", "sidebar/*", "debug/*", "settings/*"], "matches": ["<all_urls>"]}]}