{"update_url": "https://clients2.google.com/service/update2/crx", "name": "Schema Builder for Structured Data", "description": "Easily build validated json-ld structured data markup for any webpage. Based on schema.org specification", "version": "0.80.1", "options_page": "options.html", "background": {"service_worker": "background.bundle.js"}, "action": {}, "icons": {"128": "icon-128.png"}, "permissions": ["activeTab", "scripting", "storage"], "content_scripts": [{"js": ["persistanceContentScript.bundle.js"], "css": [], "matches": ["*://*/*"], "run_at": "document_start"}], "web_accessible_resources": [{"resources": ["content.styles.css", "extension.styles.css", "schema-tester.styles.css", "icon-128.png", "icon-34.png", "seoclarity-logo.png", "shakehand.png", "google-logo.png", "contentScript.bundle.js"], "matches": ["<all_urls>"]}], "manifest_version": 3}