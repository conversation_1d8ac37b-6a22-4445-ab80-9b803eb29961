.schema-tester{
  text-align: center;
  overflow: auto;
  flex: 1 1;
  width: 100%;
  height: calc(100vh - 111px);
  margin-top: 10px;
}
.result-controller{
  text-align: right;
  padding: 0 15px 2px 15px;
}

.schema-tester .spinner-border{
  color: var(--primary-color);
  margin-top: 50px;
  margin-bottom: 10px;
}
.failed{
  margin-top: 10px;
  color: red;
}
.empty{
  margin-top: 10px;
  font-style: italic;
  padding: 5px;
  font-size: 15px;
}
.empty a{
  margin: 0 3px;
}

.accordion__button{
  border-top: 1px solid #ccc;
  padding: 10px 45px 10px 17px;
  background-color: #fff;
  box-shadow: 0 1px 3px 0 rgba(0,0,0,.05);
  cursor: pointer;
  display: flex;
  position: relative;
  outline: none;
}
.accordion__button:before {
  content: '';
  display: inline-block;
  content: "";
  height: 10px;
  width: 10px;
  margin-right: 12px;
  border-bottom: 2px solid #3b3b3b;
  border-right: 2px solid #3b3b3b;
  position: absolute;
  right: 5px;
  top: 50%;
  transform: translate(0, -50%) rotate(-45deg);
  transition : all 0.3s ease-in-out;
}

.accordion__button[aria-expanded='true']:before,
.accordion__button[aria-selected='true']:before {
  transform: translate(0, -50%) rotate(45deg);
  transition: all 0.3s ease-in-out;
}
.accordion__button[aria-expanded='true']{
  border-bottom: none;
}

.accordion__button span{
  margin-left: 10px;
  color: rgba(0,0,0,.5);
  font-size: 11px;
  text-transform: uppercase;
}
.accordion__button :first-child{
  margin-right: auto;
  font-size: 14px;
  text-transform: none;
  color: #3b3b3b;
  max-width: 33%;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.mark_as_error{
  color: red !important;
}
.mark_as_warning{
  color: #e59400 !important;
}
.accordion__panel{
  text-align: left;
  padding: 5px;
  background-color: #f9f9f9;
}
.single-item>div{
  padding: 5px 10px;
  font-size: 13px;
  word-break: break-word;
  white-space: normal;
  border-bottom: 1px solid #ccc;
  margin-left: 0;
  margin-right: 0;
  flex-wrap: nowrap;
}
.single-item label{
  margin-bottom: 0;
}
.list-group>:last-child>div{
  border:none;
  margin-bottom: 15px;
}
.single-item .row .col-lg-6{
  align-items: center;
  padding-top: 2px;
  padding-bottom: 2px;
  display: flex;
}
.single-item .row:hover{
  background-color: #eee;
}
.single-item> .row :first-child{
  font-weight: 600;
}
.error-field{
  color: red;
  font-style: italic;
}
.error-field svg,.warning-field svg{
  width: 15px;
  margin-right: 5px;
}
.error-field label>span,.warning-field label>span{
  display: flex;
  align-items: baseline;
}
.warning-field{
  color: #e59400;
  font-style: italic;
}
.btn-modal-download{
  font-size: 14px;
  padding: 4px 10px;
}
.btn-modal-download .spinner-border,.btn-modal-download svg{
  height: 16px;
  width: 16px;
  border-width: 2px;
  margin-right: 4px;
  color: #fff;
}