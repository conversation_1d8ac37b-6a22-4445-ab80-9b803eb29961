// Schema Speed Assistant - Background Service Worker
// Handles extension lifecycle, message passing, and storage management

class BackgroundController {
  constructor() {
    this.storage = new SchemaStorage();
    this.initialized = false;
    this.setupEventListeners();
  }

  async initialize() {
    if (this.initialized) return;
    
    try {
      await this.storage.initialize();
      this.setupMessageHandlers();
      this.initialized = true;
    } catch (error) {
      console.error('Failed to initialize background controller:', error);
    }
  }

  setupEventListeners() {
    // Extension installation/startup
    chrome.runtime.onInstalled.addListener((details) => {
      this.handleInstallation(details);
    });

    // Extension icon click - toggle sidebar
    chrome.action.onClicked.addListener((tab) => {
      this.toggleSidebar(tab);
    });

    // Message handling from content scripts and popup
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      console.log('Background received message:', request);
      
      if (request.action === 'reloadExtension') {
        this.handleReloadExtension()
          .then(response => sendResponse(response))
          .catch(error => sendResponse({ success: false, error: error.message }));
        return true;
      }

      if (request.action === 'saveTemplate') {
        this.handleSaveTemplate(request.template)
          .then(response => {
            console.log('Template saved:', response);
            sendResponse(response);
          })
          .catch(error => {
            console.error('Error saving template:', error);
            sendResponse({ success: false, error: error.message });
          });
        return true;
      }

      // Handle other messages
      this.handleMessage(request, sender, sendResponse);
      return true;
    });

    // Tab updates for URL-based profile detection
    chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
      if (changeInfo.status === 'complete' && tab.url) {
        this.handleTabUpdate(tabId, tab);
      }
    });
  }

  async toggleSidebar(tab) {
    try {
      // Send message to content script to toggle sidebar
      const response = await chrome.tabs.sendMessage(tab.id, {
        action: 'toggleSidebar'
      });
      
      if (response && response.success) {
        console.log('Sidebar toggled successfully');
      } else {
        console.log('Failed to toggle sidebar');
      }
    } catch (error) {
      console.error('Error toggling sidebar:', error);
      
      // If content script isn't ready, try to inject it
      try {
        await chrome.scripting.executeScript({
          target: { tabId: tab.id },
          files: ['content-scripts/sidebar-injector.js']
        });
        
        // Try again after a short delay
        setTimeout(async () => {
          try {
            await chrome.tabs.sendMessage(tab.id, {
              action: 'showSidebar'
            });
          } catch (retryError) {
            console.error('Retry failed:', retryError);
          }
        }, 100);
      } catch (injectionError) {
        console.error('Failed to inject sidebar script:', injectionError);
      }
    }
  }

  async handleInstallation(details) {
    console.log('Schema Speed Assistant installed:', details.reason);
    
    // Initialize storage on first install
    if (details.reason === 'install') {
      await this.initializeStorage();
    }
  }

  async initializeStorage() {
    // Initialize sync storage for persistent data
    const syncData = {
      version: "1.0.0",
      templates: {},
      profiles: {},
      settings: {
        defaultProfile: "news",
        autoDetectProfileType: true,
        urlBasedDefaults: {},
        transformationSettings: {
          dateFormat: "ISO8601",
          textCleaning: true
        }
      },
      createdAt: new Date().toISOString()
    };

    // Initialize local storage for session/temporary data
    const localData = {
      version: "1.0.0",
      sessions: {},
      uiState: {
        sidebarDocked: false,
        sidebarPosition: 'right',
        theme: 'light'
      },
      createdAt: new Date().toISOString()
    };

    await Promise.all([
      chrome.storage.sync.set({ schemaSpeedData: syncData }),
      chrome.storage.local.set({ schemaSpeedSession: localData })
    ]);
    
    console.log('Schema Speed Assistant storage initialized');
  }

  async handleMessage(request, sender, sendResponse) {
    try {
      switch (request.action) {
        case 'extractTemplate':
          const template = await this.extractTemplate(sender.tab.id);
          sendResponse({ success: true, template });
          break;

        case 'populateFields':
          const results = await this.populateFields(sender.tab.id, request.data);
          sendResponse({ success: true, results });
          break;

        case 'startCapture':
          await this.startContentCapture(sender.tab.id, request.fieldName);
          sendResponse({ success: true });
          break;

        case 'fillFields':
          const fillResults = await this.fillFormFields(sender.tab.id);
          sendResponse({ success: true, results: fillResults });
          break;

        case 'clearFields':
          const clearResults = await this.clearFormFields(sender.tab.id);
          sendResponse({ success: true, results: clearResults });
          break;

        case 'saveProfile':
          await this.saveProfile(request.profileData);
          sendResponse({ success: true });
          break;

        case 'getProfiles':
          const profiles = await this.getProfiles(request.domain);
          sendResponse({ success: true, profiles });
          break;

        case 'getCurrentSession':
          const session = await this.getCurrentSession();
          sendResponse({ success: true, session });
          break;

        case 'updateSession':
          await this.updateSession(request.sessionData);
          sendResponse({ success: true });
          break;

        case 'getTemplates':
          const templates = await this.getTemplates();
          const lastUsed = await this.getLastUsedTemplate();
          sendResponse({ success: true, templates, lastUsed });
          break;

        case 'getTemplate':
          const templateData = await this.getTemplate(request.name);
          sendResponse({ success: true, template: templateData });
          break;

        case 'deleteTemplate':
          await this.deleteTemplate(request.name);
          sendResponse({ success: true });
          break;

        case 'setLastUsedTemplate':
          await chrome.storage.sync.set({ schemaSpeedLastUsedTemplate: request.name });
          sendResponse({ success: true });
          break;

        default:
          console.warn('Unknown action:', request.action);
          sendResponse({ success: false, error: 'Unknown action' });
      }
    } catch (error) {
      console.error('Error handling message:', error);
      sendResponse({ success: false, error: error.message });
    }
  }

  async extractTemplate(tabId) {
    // Inject template extraction script
    const results = await chrome.scripting.executeScript({
      target: { tabId },
      func: this.injectTemplateExtraction
    });

    return results[0]?.result || null;
  }

  injectTemplateExtraction() {
    // This function runs in the page context
    const FIELD_SELECTORS = [
      'input[type="text"]',
      'input[type="email"]', 
      'input[type="tel"]',
      'input[type="url"]',
      'input[type="password"]',
      'textarea',
      'select'
    ];

    function detectFieldLabel(inputElement) {
      let labelText = '';
      
      // Strategy 1: Associated label by ID
      if (inputElement.id) {
        const associatedLabel = document.querySelector(`label[for="${inputElement.id}"]`);
        if (associatedLabel) {
          labelText = cleanLabelText(associatedLabel.textContent);
        }
      }
      
      // Strategy 2: Nearest label in DOM structure
      if (!labelText) {
        const nearbyLabel = inputElement.closest('div,fieldset,section')?.querySelector(
          'label, .form-label, .control-label, .field-label'
        );
        if (nearbyLabel) {
          labelText = cleanLabelText(nearbyLabel.textContent);
        }
      }
      
      // Strategy 3: Previous sibling element
      if (!labelText) {
        const prevElement = inputElement.previousElementSibling;
        if (prevElement && isLabelElement(prevElement)) {
          labelText = cleanLabelText(prevElement.textContent);
        }
      }
      
      // Strategy 4: Placeholder text fallback
      if (!labelText && inputElement.placeholder) {
        labelText = inputElement.placeholder.trim();
      }
      
      // Strategy 5: Name attribute fallback
      if (!labelText && inputElement.name) {
        labelText = capitalizeFirstLetter(inputElement.name);
      }
      
      return labelText;
    }

    function cleanLabelText(text) {
      return text.trim().replace(/[*:]/g, '').trim();
    }

    function isLabelElement(element) {
      return element.tagName === 'LABEL' || 
             element.classList.contains('form-label') || 
             element.classList.contains('control-label');
    }

    function capitalizeFirstLetter(string) {
      return string.charAt(0).toUpperCase() + string.slice(1);
    }

    function generateUniqueSelector(element) {
      const selectors = [];
      
      if (element.id) {
        selectors.push(`#${element.id}`);
      }
      
      if (element.name) {
        selectors.push(`[name="${element.name}"]`);
      }
      
      if (element.className) {
        const classes = element.className.split(' ').filter(c => c.length > 0);
        if (classes.length > 0) {
          selectors.push(`.${classes.join('.')}`);
        }
      }
      
      // Add position-based selector as fallback
      const tagName = element.tagName.toLowerCase();
      const siblings = Array.from(element.parentNode.children).filter(el => 
        el.tagName.toLowerCase() === tagName
      );
      const index = siblings.indexOf(element);
      selectors.push(`${tagName}:nth-of-type(${index + 1})`);
      
      return selectors[0]; // Return most specific selector
    }

    // Extract template
    const fields = document.querySelectorAll(FIELD_SELECTORS.join(','));
    const template = {
      fields: {},
      metadata: {
        url: window.location.hostname,
        extractedAt: new Date().toISOString(),
        totalFields: fields.length
      }
    };
    
    fields.forEach((field, index) => {
      const label = detectFieldLabel(field);
      if (label) {
        const fieldInfo = {
          selector: generateUniqueSelector(field),
          type: field.tagName.toLowerCase(),
          inputType: field.type || 'text',
          label: label,
          required: field.required || field.hasAttribute('required'),
          placeholder: field.placeholder || '',
          name: field.name || '',
          id: field.id || ''
        };
        
        template.fields[label.toLowerCase()] = fieldInfo;
        
        // Fill field with its own label for template creation
        if (field.tagName.toLowerCase() === 'select') {
          // Don't modify select elements
        } else {
          field.value = label;
        }
        
        // Trigger events
        field.dispatchEvent(new Event('input', { bubbles: true }));
        field.dispatchEvent(new Event('change', { bubbles: true }));
      }
    });
    
    return template;
  }

  async populateFields(tabId, capturedData) {
    const results = await chrome.scripting.executeScript({
      target: { tabId },
      func: this.injectFieldPopulation,
      args: [capturedData]
    });

    return results[0]?.result || null;
  }

  injectFieldPopulation(capturedData) {
    // This function runs in the page context
    const FIELD_SELECTORS = [
      'input[type="text"]',
      'input[type="email"]', 
      'input[type="tel"]',
      'input[type="url"]',
      'input[type="password"]',
      'textarea',
      'select'
    ];

    function detectFieldLabel(inputElement) {
      // Same label detection logic as in template extraction
      let labelText = '';
      
      if (inputElement.id) {
        const associatedLabel = document.querySelector(`label[for="${inputElement.id}"]`);
        if (associatedLabel) {
          labelText = associatedLabel.textContent.trim().replace(/[*:]/g, '').trim();
        }
      }
      
      if (!labelText) {
        const nearbyLabel = inputElement.closest('div,fieldset,section')?.querySelector(
          'label, .form-label, .control-label, .field-label'
        );
        if (nearbyLabel) {
          labelText = nearbyLabel.textContent.trim().replace(/[*:]/g, '').trim();
        }
      }
      
      if (!labelText) {
        const prevElement = inputElement.previousElementSibling;
        if (prevElement && (prevElement.tagName === 'LABEL' || 
            prevElement.classList.contains('form-label') || 
            prevElement.classList.contains('control-label'))) {
          labelText = prevElement.textContent.trim().replace(/[*:]/g, '').trim();
        }
      }
      
      if (!labelText && inputElement.placeholder) {
        labelText = inputElement.placeholder.trim();
      }
      
      if (!labelText && inputElement.name) {
        labelText = inputElement.name.charAt(0).toUpperCase() + inputElement.name.slice(1);
      }
      
      return labelText;
    }

    function findMatchingData(labelKey, capturedData) {
      // Exact match first
      if (capturedData[labelKey]) {
        return capturedData[labelKey];
      }
      
      // Fuzzy matching for similar terms
      const synonyms = {
        'headline': ['title', 'heading', 'article title', 'post title'],
        'author': ['author name', 'writer', 'by'],
        'url': ['link', 'website', 'web address'],
        'image': ['image url', 'picture', 'photo'],
        'description': ['summary', 'excerpt', 'content']
      };
      
      for (const [main, alts] of Object.entries(synonyms)) {
        if ((labelKey === main && capturedData[alts.find(alt => capturedData[alt])]) || 
            (alts.includes(labelKey) && capturedData[main])) {
          return capturedData[main] || capturedData[alts.find(alt => capturedData[alt])];
        }
      }
      
      // Simple similarity check
      const similarKeys = Object.keys(capturedData).filter(key => {
        return key.includes(labelKey) || labelKey.includes(key);
      });
      
      if (similarKeys.length > 0) {
        return capturedData[similarKeys[0]];
      }
      
      return null;
    }

    function fillField(field, value) {
      if (field.tagName.toLowerCase() === 'select') {
        const option = Array.from(field.options).find(opt => 
          opt.value === value || opt.textContent.trim() === value
        );
        if (option) {
          field.value = option.value;
        }
      } else {
        field.value = value;
      }
      
      // Trigger events to notify form frameworks
      field.dispatchEvent(new Event('input', { bubbles: true }));
      field.dispatchEvent(new Event('change', { bubbles: true }));
      field.dispatchEvent(new Event('blur', { bubbles: true }));
    }

    // Populate fields
    const fields = document.querySelectorAll(FIELD_SELECTORS.join(','));
    const results = {
      filled: 0,
      skipped: 0,
      errors: []
    };
    
    fields.forEach(field => {
      const label = detectFieldLabel(field);
      if (label) {
        const labelKey = label.toLowerCase();
        const matchingData = findMatchingData(labelKey, capturedData);
        
        if (matchingData) {
          try {
            fillField(field, matchingData.value || matchingData);
            results.filled++;
          } catch (error) {
            results.errors.push({
              field: label,
              error: error.message
            });
          }
        } else {
          results.skipped++;
        }
      }
    });
    
    return results;
  }

  async startContentCapture(tabId, fieldName) {
    try {
      // Send message directly to content script
      await chrome.tabs.sendMessage(tabId, {
        action: 'startCapture',
        fieldName: fieldName
      });
    } catch (error) {
      console.error('Error starting content capture:', error);
      // Fallback: try to inject the content capture script
      try {
        await chrome.scripting.executeScript({
          target: { tabId },
          files: ['content-scripts/content-capture.js']
        });
        
        // Try sending the message again after a short delay
        setTimeout(async () => {
          try {
            await chrome.tabs.sendMessage(tabId, {
              action: 'startCapture',
              fieldName: fieldName
            });
          } catch (retryError) {
            console.error('Retry failed:', retryError);
          }
        }, 100);
      } catch (injectionError) {
        console.error('Failed to inject content capture script:', injectionError);
      }
    }
  }

  async handleTabUpdate(tabId, tab) {
    // Check if we should auto-detect profile for this URL
    const data = await chrome.storage.sync.get(['schemaSpeedData']);
    const settings = data.schemaSpeedData?.settings;
    
    if (settings?.autoDetectProfileType) {
      // Logic for auto-detecting profile type based on URL patterns
      // This would be implemented based on stored URL patterns
    }
  }

  async saveProfile(profileData) {
    const data = await chrome.storage.sync.get(['schemaSpeedData']);
    const storage = data.schemaSpeedData || {};
    
    if (!storage.profiles) {
      storage.profiles = {};
    }
    
    if (!storage.profiles[profileData.domain]) {
      storage.profiles[profileData.domain] = {};
    }
    
    storage.profiles[profileData.domain][profileData.profileType] = {
      ...profileData.profile,
      createdAt: new Date().toISOString(),
      lastUsed: new Date().toISOString()
    };
    
    await chrome.storage.sync.set({ schemaSpeedData: storage });
  }

  async getProfiles(domain) {
    const data = await chrome.storage.sync.get(['schemaSpeedData']);
    const storage = data.schemaSpeedData || {};
    
    if (domain) {
      return storage.profiles?.[domain] || {};
    }
    
    return storage.profiles || {};
  }

  async getCurrentSession() {
    const data = await chrome.storage.local.get(['schemaSpeedSession']);
    return data.schemaSpeedSession?.sessions?.current || null;
  }

  async updateSession(sessionData) {
    const data = await chrome.storage.local.get(['schemaSpeedSession']);
    const storage = data.schemaSpeedSession || {};
    
    if (!storage.sessions) {
      storage.sessions = {};
    }
    
    storage.sessions.current = {
      ...storage.sessions.current,
      ...sessionData,
      updatedAt: new Date().toISOString()
    };
    
    await chrome.storage.local.set({ schemaSpeedSession: storage });
  }

  async fillFormFields(tabId) {
    try {
      console.log('[DEBUG] Starting form field filling for tab:', tabId);
      
      const results = await chrome.scripting.executeScript({
        target: { tabId: tabId },
        world: "MAIN", // Execute in the page context
        func: () => {
          console.log('[DEBUG] Bookmarklet code starting');
          
          function dispatchEvents(input) {
            const events = ['input', 'change', 'blur'];
            events.forEach(eventType => {
              const event = new Event(eventType, { bubbles: true, cancelable: true });
              input.dispatchEvent(event);
            });
          }

          // Step 1: Fill fields with their label text
          const inputs = document.querySelectorAll('input[type="text"],input[type="email"],input[type="tel"],input[type="url"],input[type="password"],textarea,select');
          const fieldData = new Map();

          inputs.forEach(input => {
            let labelText = '';
            if (input.id) {
              const label = document.querySelector(`label[for="${input.id}"]`);
              if (label) {
                labelText = label.textContent.trim().replace(/[*:]/g, '').trim();
                console.log('[DEBUG] Found label by ID:', labelText);
              }
            }
            
            if (!labelText) {
              const closestLabel = input.closest('div,fieldset,section')?.querySelector('label,.form-label,.control-label,.field-label');
              if (closestLabel) {
                labelText = closestLabel.textContent.trim().replace(/[*:]/g, '').trim();
              }
            }
            
            if (!labelText) {
              const prevElement = input.previousElementSibling;
              if (prevElement && (prevElement.tagName === 'LABEL' || prevElement.classList.contains('form-label') || prevElement.classList.contains('control-label'))) {
                labelText = prevElement.textContent.trim().replace(/[*:]/g, '').trim();
              }
            }
            
            if (!labelText && input.placeholder) {
              labelText = input.placeholder.trim();
            }
            
            if (!labelText && input.name) {
              labelText = input.name.charAt(0).toUpperCase() + input.name.slice(1);
            }
            
            if (labelText) {
              fieldData.set(input, labelText);
              if (input.tagName.toLowerCase() === 'select') {
                console.log('[DEBUG] Setting value for SELECT :', labelText);
                input.value = '';
              } else {
                input.value = labelText;
              }
              dispatchEvents(input);
            }
          });

          // Step 2: Add SS- prefix to all filled fields
          setTimeout(() => {
            fieldData.forEach((labelText, input) => {
              const currentValue = input.value;
              if (currentValue && !currentValue.startsWith('SS-')) {
                input.value = `SS-${currentValue}`;
                dispatchEvents(input);
              }
            });
          }, 100); // Small delay to ensure first step completes

          return { success: true, fieldsProcessed: fieldData.size };
        }
      });

      return results;
    } catch (error) {
      console.error('[ERROR] Error in fillFormFields:', error);
      return { success: false, error: error.message };
    }
  }

  async clearFormFields(tabId) {
    try {
      const results = await chrome.scripting.executeScript({
        target: { tabId },
        func: this.injectClearFields
      });
      
      // Extract the return value from the executed script
      if (results && results[0] && results[0].result) {
        return results[0].result;
      }
      
      return { cleared: 0, total: 0 };
    } catch (error) {
      console.error('Error clearing form fields:', error);
      return { cleared: 0, total: 0, error: error.message };
    }
  }

  injectClearFields() {
    const FIELD_SELECTORS = [
      'input[type="text"]',
      'input[type="email"]', 
      'input[type="tel"]',
      'input[type="url"]',
      'input[type="password"]',
      'textarea',
      'select'
    ];

    function clearField(field) {
      if (field.tagName.toLowerCase() === 'select') {
        field.value = '';
      } else {
        field.value = '';
      }
      
      // Trigger events to notify form frameworks
      field.dispatchEvent(new Event('input', { bubbles: true }));
      field.dispatchEvent(new Event('change', { bubbles: true }));
    }

    // Clear fields
    const fields = document.querySelectorAll(FIELD_SELECTORS.join(','));
    let clearedCount = 0;
    
    fields.forEach(field => {
      clearField(field);
      clearedCount++;
    });
    
    return { cleared: clearedCount, total: fields.length };
  }

  async handleReloadExtension() {
    try {
      // Reload the extension
      chrome.runtime.reload();
      return { success: true };
    } catch (error) {
      console.error('Error reloading extension:', error);
      throw error;
    }
  }

  async handleSaveTemplate(template) {
    try {
      console.debug('[DEBUG] Saving template:', template);
      
      // Get current templates from local storage
      const data = await chrome.storage.local.get(['schemaSpeedTemplates']);
      const templates = data.schemaSpeedTemplates || {};
      
      // Add new template
      templates[template.name] = {
        type: template.type,
        content: template.content,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      // Save back to local storage
      await chrome.storage.local.set({ schemaSpeedTemplates: templates });
      
      // Save as last used template
      await chrome.storage.local.set({ schemaSpeedLastUsedTemplate: template.name });
      
      // Extract mappable fields for future use
      const mappableFields = await this.extractMappableFields(template.content);
      
      console.debug('[DEBUG] Template saved successfully:', templates[template.name]);
      
      return { 
        success: true, 
        template: templates[template.name],
        mappableFields
      };
    } catch (error) {
      console.error('Error saving template:', error);
      throw error;
    }
  }

  async getTemplates() {
    try {
      const data = await chrome.storage.local.get(['schemaSpeedTemplates']);
      return data.schemaSpeedTemplates || {};
    } catch (error) {
      console.error('Error getting templates:', error);
      throw error;
    }
  }

  async getTemplate(name) {
    try {
      const templates = await this.getTemplates();
      return templates[name];
    } catch (error) {
      console.error('Error getting template:', error);
      throw error;
    }
  }

  async deleteTemplate(name) {
    try {
      const data = await chrome.storage.local.get(['schemaSpeedTemplates']);
      const templates = data.schemaSpeedTemplates || {};
      
      if (templates[name]) {
        delete templates[name];
        await chrome.storage.local.set({ schemaSpeedTemplates: templates });
        
        // If this was the last used template, clear that as well
        const lastUsed = await this.getLastUsedTemplate();
        if (lastUsed === name) {
          await chrome.storage.local.remove(['schemaSpeedLastUsedTemplate']);
        }
      }
    } catch (error) {
      console.error('Error deleting template:', error);
      throw error;
    }
  }

  async getLastUsedTemplate() {
    try {
      const data = await chrome.storage.local.get(['schemaSpeedLastUsedTemplate']);
      return data.schemaSpeedLastUsedTemplate || null;
    } catch (error) {
      console.error('Error getting last used template:', error);
      return null;
    }
  }

  async extractMappableFields(templateContent) {
    try {
      // Parse template content
      let content = templateContent;
      if (typeof content === 'string') {
        // Remove script tags if present
        content = content.replace(/<script[^>]*>|<\/script>/g, '');
        content = JSON.parse(content);
      }
      
      // Extract fields that can be mapped
      const mappableFields = {};
      const findMappableFields = (obj, parentKey = '') => {
        for (const [key, value] of Object.entries(obj)) {
          const fullKey = parentKey ? `${parentKey}.${key}` : key;
          
          if (typeof value === 'string') {
            mappableFields[fullKey] = {
              type: 'string',
              example: value
            };
          } else if (typeof value === 'number') {
            mappableFields[fullKey] = {
              type: 'number',
              example: value
            };
          } else if (typeof value === 'object' && value !== null) {
            findMappableFields(value, fullKey);
          }
        }
      };
      
      findMappableFields(content);
      return mappableFields;
    } catch (error) {
      console.error('Error extracting mappable fields:', error);
      return {};
    }
  }
}

// Storage management class
class SchemaStorage {
  constructor() {
    this.localKey = 'schemaSpeedSession';
  }

  async initialize() {
    // Check if storage needs initialization
    const data = await chrome.storage.local.get([
      this.localKey,
      'schemaSpeedTemplates'
    ]);

    if (!data.schemaSpeedTemplates) {
      await chrome.storage.local.set({ 
        schemaSpeedTemplates: {
          version: "1.0.0",
          templates: {},
          createdAt: new Date().toISOString()
        }
      });
    }

    if (!data[this.localKey]) {
      await chrome.storage.local.set({
        [this.localKey]: {
          version: "1.0.0",
          sessions: {},
          uiState: {
            sidebarDocked: false,
            sidebarPosition: 'right',
            theme: 'light'
          },
          createdAt: new Date().toISOString()
        }
      });
    }

    return true;
  }

  async getStoredData() {
    const data = await chrome.storage.local.get([
      this.localKey,
      'schemaSpeedTemplates',
      'schemaSpeedLastUsedTemplate'
    ]);
    
    return {
      ...data.schemaSpeedTemplates,
      sessions: data[this.localKey]?.sessions || {},
      uiState: data[this.localKey]?.uiState || {},
      lastUsedTemplate: data.schemaSpeedLastUsedTemplate
    };
  }

  async saveData(data) {
    const { sessions, uiState, lastUsedTemplate, ...templateData } = data;
    
    await chrome.storage.local.set({ 
      schemaSpeedTemplates: templateData,
      [this.localKey]: {
        sessions,
        uiState,
        version: data.version
      },
      schemaSpeedLastUsedTemplate: lastUsedTemplate
    });
  }
}

// Initialize the background controller
const backgroundController = new BackgroundController();
backgroundController.initialize().catch(error => {
  console.error('[Schema Speed] Error initializing background controller:', error);
});
