# Schema Speed Assistant

A Chrome extension that automates schema markup creation for SEO specialists and web developers. This universal form replacement plugin eliminates repetitive manual work by automatically mapping and transferring data between web pages and schema builders.

## Features

### 🔍 Template Extraction
- Intelligent form field detection across any schema builder platform
- Universal template creation that works with UpTools, SEOPress, WordPress, and more
- No platform-specific coding required

### 🎯 Visual Content Capture
- Interactive element selection with hover highlighting
- CSS selector generation with multiple fallback strategies
- Content transformation tools (remove numbers, format dates, etc.)
- Real-time preview of captured content

### 📋 Profile Management
- Domain-specific selector mappings for reusable workflows
- URL pattern matching for automatic profile detection
- Import/export functionality for team collaboration
- Template type management

### ⚡ Automated Population
- One-click form filling with captured data
- Intelligent field matching with fuzzy logic
- Support for React, Vue, Angular, and vanilla JavaScript forms
- Alternative JSON-LD snippet generation

## Installation

### For Development/Testing

1. Clone or download this repository
2. Open Chrome and navigate to `chrome://extensions/`
3. Enable "Developer mode" in the top right
4. Click "Load unpacked" and select the extension folder
5. The Schema Speed Assistant icon should appear in your extensions toolbar

### For Production Use

*Extension will be available on the Chrome Web Store once published*

## Quick Start Guide

### 1. Extract a Template (One-time setup per schema type)

1. Visit your preferred schema builder (e.g., UpTools, SEOPress)
2. Open the Schema Speed Assistant sidebar
3. Click "Extract Template" 
4. The extension will identify all form fields and create a universal template

### 2. Create a Domain Profile (One-time setup per website)

1. Navigate to your target website (e.g., Forbes article)
2. Open the extension sidebar and go to the "Capture" tab
3. Click "Start Manual Capture"
4. Select field types from the dropdown and click on corresponding page elements
5. Save the profile for future use

### 3. Automated Workflow (Daily usage)

1. Visit any article on the same domain
2. Click "Start Capture" to collect content using your saved profile
3. Return to your schema builder
4. Click "Fill Fields" to automatically populate the form
5. Review and submit your schema markup

## How It Works

### Three-Phase Workflow

**Phase 1: Template Creation**
```
Schema Builder → Field Detection → Universal Template → Save for Reuse
```

**Phase 2: Domain Mapping**  
```
Target Website → Visual Selection → CSS Selectors → Domain Profile
```

**Phase 3: Automated Population**
```
Captured Data → Field Matching → Form Population OR JSON Generation
```

### Technical Architecture

- **Local-First**: All data stored in Chrome local storage
- **Universal Compatibility**: Works with any form-based schema builder
- **Intelligent Automation**: Smart field detection with multiple fallback strategies
- **Framework Support**: Compatible with React, Vue, Angular, and vanilla JavaScript

## Use Cases

### Perfect For:
- SEO specialists managing multiple client websites
- Web developers implementing structured data at scale
- Content teams publishing templated content (news, recipes, products)
- Agencies with repetitive schema markup workflows

### Time Savings:
- Manual schema creation: 10-15 minutes per article
- With Schema Speed Assistant: 30 seconds per article after setup
- ROI achieved after processing 3-5 articles per domain

## Browser Support

- Chrome 88+ (Manifest V3 required)
- Chromium-based browsers (Edge, Brave, etc.)

## Privacy & Security

- **No external servers**: All data processing happens locally
- **No data collection**: Extension doesn't track or store personal information
- **Secure storage**: Uses Chrome's built-in storage APIs
- **Minimal permissions**: Only requests necessary permissions for functionality

## Troubleshooting

### Common Issues

**Template extraction not working:**
- Ensure you're on a page with form fields
- Try refreshing the page and extracting again
- Check that the schema builder uses standard HTML form elements

**Content capture not highlighting elements:**
- Make sure you've selected a field type from the dropdown
- Try disabling other extensions that might interfere
- Refresh the page and start capture mode again

**Form filling not working:**
- Verify that captured data exists (check Output tab)
- Ensure you're on the same schema builder where template was extracted
- Some dynamic forms may require manual triggering of change events

### Getting Help

1. Check the extension sidebar for status indicators
2. Open browser DevTools Console for error messages
3. Try disabling other extensions to isolate conflicts
4. Refresh both the target page and schema builder

## Development

### Project Structure
```
schema-speed-assistant/
├── manifest.json              # Extension configuration
├── background.js             # Service worker for message handling
├── sidebar/                  # Extension sidebar interface
│   ├── sidebar.html
│   ├── sidebar.css
│   └── sidebar.js
├── content-scripts/          # Page interaction scripts
│   ├── content-capture.js
│   ├── form-field-system.js
│   └── sidebar-injector.js
├── styles/                   # CSS for content overlays
│   └── content-overlay.css
├── icons/                    # Extension icons
└── README.md
```

### Key Technologies
- **Manifest V3**: Modern Chrome extension architecture
- **Vanilla JavaScript**: No external dependencies
- **CSS Custom Properties**: Consistent theming
- **Chrome Storage API**: Local data persistence
- **Content Scripts**: Page interaction and DOM manipulation

## Changelog

### Version 1.0.0 (Initial Release)
- ✅ Universal template extraction from schema builders
- ✅ Visual content capture with hover highlighting
- ✅ CSS selector generation with multiple strategies
- ✅ Domain-specific profile management
- ✅ Automated form field population
- ✅ Content transformation tools
- ✅ Modern sidebar interface
- ✅ Local storage with import/export
- ✅ Framework compatibility (React, Vue, Angular)

### Planned Features
- 🔄 Auto-detection of common schema fields
- 🔄 Bulk profile operations
- 🔄 Advanced transformation rules
- 🔄 Template sharing marketplace
- 🔄 Analytics and usage insights

## Contributing

This is currently a private project. For feature requests or bug reports, please contact the development team.

## License

Copyright © 2025 Schema Speed Assistant. All rights reserved.

---

**Schema Speed Assistant** - Transforming schema markup from manual work into automated workflow.
