:root {
  --main-text-color: #3b3b3b;
  --secondary-text-color: rgba(0, 0, 0, 0.65);
  /* --primary-color:rgba(1, 115, 175, 0.88);
  --secondary-color:rgba(0, 114, 176, 1); */
  --primary-color: #0e76a8;
  --secondary-color: #0e76a8;
  --brown-color: #a00000;
  --red-color:#dc3545;
  --orange-color:#ffc107;
}
#sc-extension-body input:focus,
#sc-extension-body select:focus,
#sc-extension-body textarea:focus {
  outline: none;
}

/*tooltip*/
.sc-tooltipsc2 {
  cursor: pointer;
  position: relative;
  display: inline-block;
}

.sc-tooltip-text2sx {
  opacity: 0;
  z-index: 913523213513519;
  color: rgb(224, 224, 224);
  width: 190px;
  display: block;
  font-size: 11px;
  padding: 5px 10px;
  border-radius: 3px;
  text-align: center;
  text-shadow: 1px 1px 2px #111;
  background: rgba(51, 51, 51, 0.9);
  border: 1px solid rgba(34, 34, 34, 0.9);
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.5);
  transition: all 0.2s ease-in-out;
  transform: scale(0);
  position: absolute;
  right: -80px;
  bottom: 40px;
}

.sc-tooltip-text2sx:before,
.sc-tooltip-text2sx:after {
  content: "";
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-top: 10px solid rgba(51, 51, 51, 0.9);
  position: absolute;
  bottom: -10px;
  left: 48%;
}

.sc-tooltipsc2:hover .sc-tooltip-text2sx,
#sc-extension-body a:hover .sc-tooltip-text2sx {
  opacity: 1;
  transform: scale(1);
}
/*horizontal line */
.sc-solid-hr {
  content: "";
  border-top: 1px solid #ddd;
  padding-bottom: 5px;
  margin: 0 10px;
  margin-top: -1px;
}

/*css selector slider */
.sc-custom-slider {
  display: grid;
  text-align: center;
  padding: 5px 10px;
  grid-template-columns: 10% 80% 10%;
  /* align-items: center; */
}

.sc-custom-slider .sc-slider-content :first-child {
  font-size: 16px;
  font-weight: 400;
  color: var(--main-text-color);
}

.sc-custom-slider .sc-slider-content :last-child {
  margin-top: 10px;
  font-size: 12px;
  font-weight: 200;
  color: var(--secondary-text-color);
}

.sc-custom-slider > :nth-child(3),
.sc-custom-slider > :nth-child(1) {
  user-select: none;
}

/*congratulations page*/

.sc-welcome-page3q {
  text-align: center;
  color: var(--main-text-color);
  padding: 0px;
  min-height: calc(100vh - 40px);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  animation: fadein 1s;
}

.sc-welcome-page3q__nav{
  display: grid;
  align-items: center;
  width: 100%;
  grid-template-columns: 1fr 1fr;
}

.sc-welcome-page3q__nav>div{
  padding: 5px;
  cursor: pointer;
  position: relative;
}
.sc-welcome-page3q__nav>div span{
  position: absolute;
  font-size: 11px;
  top: 3px;
  right: auto;
  color: red;
}
.sc-welcome-page3q__nav>div.active,.sc-welcome-page3q__nav>div:hover{
  color: #0688c9;
  box-shadow: 0px 2px 0px 0px #0688c9;
}

.sc-welcome-page3q .sc-schema-selector31q {
  width: 80%;
}
.sc-welcome-page3q .sc-schema-selector31q h6 {
  font-size: 16px;
  margin-bottom: 15px;
  text-align: left;
}

.sc-welcome-page3q>:last-child{
  /* margin-top: auto; */
  border: 1px solid #c3c3c3;
  border-left: none;
  border-right: none;
  padding: 5px 0 0 0;
  border-radius: 2px;
}
.sc-welcome-page3q > .sc-schema-list-container {
  display: flex;
  flex-direction: column;
  flex: 1 1;
  width: 100%;
  justify-content: space-around;
  align-items: center;
}

.sc-welcome-page3q-footer{
  width: 100%;
}

.sc-welcome-page3q-footer .sc-footer-items{
  display: flex;
  justify-content: space-between;
}

.sc-welcome-page3q-footer .sc-footer-items .sc-footer-item{
  text-align: center;
  padding: 0 10px;
}

.sc-welcome-page3q-footer .sc-footer-items .sc-footer-item a{
  text-decoration: none;
  color: var(--secondary-text-color);
}
.sc-welcome-page3q-footer .sc-footer-items .sc-footer-item a:hover{
  color: #2196f3;
}
.sc-welcome-page3q-footer .sc-footer-items .sc-footer-item svg{
  color: gray;
}

.sc-footer-item-img img{
  width: 1.5em;
  height: 1.5em;
}
.sc-footer-item .sc-footer-item-label{
  font-size: 13px;
}

.sc-schema-selector31q .sc-schema-list43d {
  text-align: left;
  padding: 0;
  margin: 0;
  max-height: calc(50vh - 45px - 20px - 40px - 114px);
  overflow-y: scroll;
  
}

.sc-schema-selector31q .sc-schema-list43d > li .sc-list-label {
  padding: 5px 30px;
  cursor: pointer;
  display: flex;
  border-radius: 2px;
  align-items: center;
  justify-content: space-between;
  background-color: rgba(243, 243, 243, 0.445);
  margin: 3px 0;
  font-size: 14px;
  position: relative;
}
.sc-list-label .sc-label{
  max-width: 130px;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.sc-green-dot{
  position: absolute;
  background-color: rgb(68, 199, 68);
  content: '';
  height: 10px;
  width: 10px;
  left: 8px;
  border-radius: 50%;
}
.sc-schema-selector31q .sc-schema-list43d > li .sc-list-label:hover {
  background-color: rgba(243, 243, 243, 0.623);
}

.sc-schema-selector31q .sc-sub-item {
  margin-left: 30px !important;
  margin-right: 5px !important;
  list-style: none;
  padding: 0 !important;
}
.sc-schema-selector31q .sc-sub-item .sc-list-label{
  background-color: rgba(243, 243, 243, 0.91) !important;
  font-size: 13px !important;
}
.sc-list-label span.view{
  opacity: 0;
  font-size: 11px;
  font-style: italic;
  color: rgb(148, 148, 148);
  margin-left: auto;
  margin-right: 5px;
}
.sc-list-label span.view:hover{
  text-decoration: underline;
}
.sc-list-label:hover span.view{
  opacity: 1;
}

/*  trial version modal window */

.sc-trial-version__modal {
  min-height: 100px;
  text-align: center;
}

.sc-trial-version__modal h2 {
  margin-top: 40px;
  color: #00000092;
}
/*  LOGIN   */
.sc-plugin-login {
  color: #fff;
  background-image: linear-gradient(to bottom, var(--primary-color), var(--secondary-color));
  padding: 20vh 30px;
  height: calc(100vh - 40px);
}

.sc-plugin-login h1 {
  text-align: left;
  font-size: 20px;
  padding: 10px 0 5px 0;
  margin: 0;
  margin-bottom: 10px;
}
.sc-plugin-login .sc-plugin-login__input {
  display: grid;
  grid-template-columns: 30% 70%;
  padding: 7px 0;
  align-items: center;
}
.sc-plugin-login .sc-plugin-login__input > label {
  font-size: 15px;
  font-weight: 500;
  margin: 0;
}

.sc-plugin-login .sc-plugin-login__error {
  padding: 3px 5px;
  font-size: 12px;
  font-weight: 200;
  margin-left: 30%;
  margin: 5px 0 10px 30%;
}
.sc-plugin-login .sc-plugin-login__button {
  display: flex;
  align-items: center;
  text-align: center;
  padding: 2px 20px;
  height: 30px;
  margin: 5px 0 0 auto;
}
/*script modal*/
.sc-script-modal {
  color: #4c4e50;
}
.sc-script-modal > :first-child {
  margin-bottom: 10px;
  font-size: 18px;
  font-weight: bold;
}

.sc-script-modal .sc-script-title {
  font-size: 14px;
}

.sc-script-modal .sc-script-modal-content {
  margin-top: 10px;
}

.sc-warning-message1 {
  font-size: 12px;
  font-weight: 500;
  color: var(--red-color);
  margin-top: 5px;
  margin-bottom: 10px;
  padding: 0;
  margin-left: 5px;
  display: grid;
  grid-template-columns: 20px calc(100% - 20px);
  align-items: flex-start;
}

.sc-script-modal .sc-script-wrapper {
  display: flex;
  margin-top: 15px;
  background-color: #f8fbfb;
}

.sc-script-modal .sc-script-wrapper pre {
  background-color: transparent;
  border: none !important;
  font-size: 12px;
}

.sc-script-modal .sc-script-wrapper .sc-script-icon {
  font-size: 23px;
  width: 20%;
  text-align: right;
  margin-top: 10px;
}
.sc-script-modal .sc-script-wrapper .sc-script-icon .sc-icon-icon {
  color: #fff;
}
.sc-script-modal .sc-script-wrapper .sc-script-icon > span {
  background-color: #1c6db3;
  padding: 8px 5px 0 5px;
}

.sc-script-modal .sc-script-wrapper .sc-script {
  margin-top: 5px;
  padding-top: 10px !important;
  border: 1px solid #eee;
  overflow-x: auto;
  white-space: pre-wrap;
  white-space: -moz-pre-wrap;
  white-space: -pre-wrap;
  white-space: -o-pre-wrap;
  word-wrap: break-word;
  width: 80%;
}

/*custom loader*/

.sc-loader-wrapper .sc-my-loader {
  display: inline-block;
  width: 1em;
  height: 1em;
  color: inherit;
  vertical-align: middle;
  pointer-events: none;
  border: 0.2em dotted currentcolor;
  border-radius: 50%;
  animation: 1s custom-loader linear infinite;
}

@keyframes custom-loader {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* custom alert */
.sc-custom-alert {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: var(--secondary-text-color);
  font-size: 14px;
  font-variant: tabular-nums;
  line-height: 1.5715;
  list-style: none;
  -webkit-font-feature-settings: "tnum";
  font-feature-settings: "tnum";
  position: relative;
  padding: 12px 20px;
  word-wrap: break-word;
  border-radius: .25rem;
  word-break: break-word;
  margin-bottom: 16px;
}

.sc-custom-alert.alert-success {
  color: #155724;
  background-color: #d4edda;
  border-color: #c3e6cb;
}

.sc-custom-alert.alert-warning {
  background-color: #fffbe6;
  border: 1px solid #ffe58f;
}
.sc-custom-alert.alert-error {
  color: #721c24;
  background-color: #f8d7da;
  border-color: #f5c6cb;
}
.sc-custom-alert.alert-info {
  background-color: #d1ecf1;
  border: 1px solid #bee5eb;
  color: #0c5460;
}

/*custom input*/
.sc-custom-input {
  border-radius: 3px;
  border: 1px solid #ddd;
  color: rgb(80, 80, 80);
  height: 28px;
  width: 100%;
  padding-left: 5px;
  font-size: 12px;
}

/*custom radio*/
.sc-custom-radio {
  display: flex;
  cursor: pointer;
}
.sc-custom-radio input {
  margin-right: 5px;
  cursor: pointer;
  border-color: #1890ff;
}

.sc-custom-radio div {
  font-size: 13px;
}
/*custom textarea*/
.sc-custom-textarea {
  width: 100%;
  border-color: #eee;
  color: #525252;
  padding: 2px 5px;
  font-size: 14px;
}

/*custom-button*/
.sc-custom-button {
  padding: 5px 5px;
  background-color: white;
  border: 1px solid #eee;
  cursor: pointer;
  min-width: 60px;
  border-radius: .25rem;
  color: var(--main-text-color);
  position: relative;
  height: 36px;
  line-height: 20px;
  outline: 0px !important;
  text-transform: capitalize;
  transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out;
}
.sc-custom-button:disabled{
  pointer-events: none;
  cursor: pointer;
  opacity: .5;
}
.sc-custom-button .loader {
  margin-right: 2px;
}
.sc-custom-button:hover {
  color: var(--primary-color);
  border-color: #ccc;
}

.sc-custom-button.custom-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.sc-custom-button.custom-outline-success {
  color: #28a745;
  border-color: #28a745;
}
.sc-custom-button.custom-success {
  color: #fff;
  background-color: #28a745;
  border-color: #28a745;
}
.sc-custom-button.custom-success:hover {
  background-color: #218838;
  border-color: #1e7e34;
}
.sc-custom-button.custom-error {
  color: #fff;
  background-color: #dc3545;
  border-color: #dc3545;
}
.sc-custom-button.custom-error:hover {
  background-color: #c82333;
  border-color: #bd2130;
}

.sc-custom-button.custom-primary:hover {
  background-color: var(--secondary-color);
}
.sc-custom-button.custom-outline-success:hover {
  background-color: #28a745;
  color: #fff;
}

/* custom modal window */
.sc-custom-modal {
  position: fixed;
  z-index: 21474836485;
  padding-top: 50px;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgb(0, 0, 0);
  background-color: rgba(0, 0, 0, 0.4);
}

.sc-custom-modal-header {
  display: flex;
  justify-content: space-between;
}
/* Modal Content */
.sc-custom-modal-wrapper {
  background-color: #fefefe;
  margin: auto;
  border: 1px solid #888;
  width: 70%;
  min-height: 100px;
  border-radius: 5px;
}
.sc-custom-modal-content {
  position: relative;
  padding: 20px 20px;
}
.sc-custom-modal-content .sc-custom-modal-body{
  overflow: auto;
  max-height: 60vh;
}
.sc-custom-modal-content .title{
  font-size: 20px;
  color: #575151;
}

.sc-custom-modal-footer {
  background-color: #fefefe;
  margin: 5px 0;
  border-top: 1px solid #eee;
  width: 99%;
  min-height: 45px;
  text-align: right;
}
.sc-custom-modal-footer button {
  margin-top: 9px;
  margin-right: 5px;
}

/* The Close Button */
.sc-custom-modal .close {
  color: #aaaaaa;
  font-size: 28px;
  position: absolute;
  right: 5px;
  top: 0;
  font-weight: bold;
}

.sc-custom-modal .close:hover,
.sc-custom-modal .close:focus {
  color: gray;
  text-decoration: none;
  cursor: pointer;
}

/* css selectors modal window */

.sc-css-selectors h2 {
  color: var(--main-text-color);
  font-size: 15px;
  font-weight: 400;
}

.sc-css-selector-slider {
  border: 1px solid #eee;
  padding: 10px 0 10px 0;
}

.sc-css-selector-slider .sc-css-selector-caption {
  font-size: 14px;
  text-align: center;
  margin: 0 0 10px 0;
  color: var(--brown-color);
  font-weight: 400;
}

.sc-css-selector-path{
  font-size: 12px;
}

.sc-css-selector-slider .sc-css-slider {
  align-items: center;
}

.sc-css-selectors .sc-expandable-button {
  color: var(--primary-color);
  font-size: 14px;
  font-weight: 500;
  margin: 20px 0 0 0;
  display: flex;
}
.sc-css-selectors .sc-expandable-button > :first-child {
  cursor: pointer;
  margin-right: 5px;
}
.sc-css-selectors .sc-expandable-button > :first-child:hover {
  color: var(--secondary-text-color);
}
.sc-css-selectors .sc-expandable-button  .sc-js-add{
  position: absolute;
  right: 22px;
}
.sc-css-selectors .sc-expandable-button  .sc-js-add i{
  color: green;
}

.sc-css-selectors .sc-js-input {
  margin-top: 0;
  animation: fadein .2s;
}

.sc-css-selectors .sc-js-input .sc-js-input-field {
  margin-top: 5px;
  height: 100px;
}

.sc-css-selectors .sc-selected-text {
  margin-bottom: 20px;
}

.sc-css-selectors pre {
  margin-top: 5px;
  background-color: #fffb84e0;
  height: 70px;
  overflow: auto;
  word-break: break-all;
  padding: 5px 10px;
  color: #000000c5;
  margin-bottom: 0;
  font-size: 13px;
  font-weight: 500;
}
.sc-css-selectors .sc-no-text {
  color: grey;
  text-align: center;
}

.sc-css-selector-footer{
  text-align: right;
  margin-top: 20px;
}
.sc-css-selector-footer button{
  margin-left: 10px;
  height: 30px;
  font-size: 15px;
}

.sc-header3 {
  font-size: 15px;
  color: var(--main-text-color);
  margin-bottom: 0;
}

/*save rule modal window */
.sc-save-rule {
  color: #00000092;
}
.sc-save-rule h2 {
  margin-bottom: 20px;
}
.sc-save-rule-form {
  display: grid;
  grid-template-columns: 6fr 1fr;
  margin-bottom: 20px;
}
.sc-save-rule-form button {
  margin-left: 10px;
}

.sc-save-rule-form input {
  height: 32px;
}

/*rule list modal window */
.sc-rules-list {
  min-height: 100px;
  color: #00000092;
}
.sc-rules-list .sc-rules-list-content {
  margin-top: 10px;
}
.sc-rules-list .sc-content-spin {
  margin-left: 50%;
}
.sc-rules-list .list {
  list-style: none;
  margin-bottom: 20px;
}
.sc-rules-list .list li {
  padding: 10px 5px;
  border-bottom: 1px solid #eee;
  cursor: pointer;
  font-size: 15px;
}
.sc-rules-list .list li:hover {
  background-color: #f2f2f2;
}

/* modal window > list all images in the page */

.sc-image-gallery-item {
  position: relative;
  width: 100%;
  height: 220px;
  overflow: hidden;
  border: 1px solid #ccc;
  box-shadow: 2px 2px #ccc;
  cursor: pointer;
}

.sc-image-gallery-item:hover {
  filter: blur(50%);
}

.sc-image-gallery-item:hover .sc-right-bottom {
  opacity: 0;
}
.sc-image-gallery-item:hover .sc-img-overlay {
  opacity: 0.8;
}
.sc-image-gallery-item .sc-right-bottom {
  position: absolute;
  top: 145px;
  right: 10px;
  color: #f2f2f2;
  background: #1f1f1f;
  opacity: 0.8;
  padding: 5px;
  border-radius: 2px;
  font-size: 12px;
}
.sc-img-overlay {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100%;
  width: 100%;
  opacity: 0;
  transition: 0.5s ease;
  background-color: #fff;
  text-align: center;
  padding-top: 70px;
}
.sc-image-gallery-item img {
  display: block;
  width: 100%;
  height: auto;
}

.sc-image-gallery-item .sc-img-overlay .sc-image-select {
  margin-top: 7px;
}

.sc-image-gallery-item .sc-img-overlay .sc-image-select span {
  color: white;
  font-weight: bold;
  font-size: 16px;
  padding: 5px 10px;
  background-color: black;
}
.sc-image-gallery-item .sc-img-overlay .sc-image-select span:hover {
  color: #eee;
}

.sc-image-gallery-item .sc-image-dimension {
  border: none;
  border-bottom-left-radius: 0;
}

/* --------------------- */
#sc-extension-body .google-script-wrapper {
  margin-right: 20px;
  font-size: 14px;
}
#sc-extension-body .google-script-wrapper .sc-selector-cursor {
  margin-left: 5px;
}


.sc-modal-xml-viewer{
  padding: 10px;
  position: relative;
}

.sc-modal-xml-viewer .sc-button-group{
  margin: 6px 0 15px 0;
  display: flex;
}

.sc-modal-xml-viewer .sc-button-group button{
 height: 26px;
 font-size: 13px;
 margin-right: 6px;
 padding: 6px 10px;
 line-height: 13px;
}

.sc-modal-xml-viewer .sc-button-group button i{
  line-height: 13px;
  margin-right: 3px;
}

.sc-modal-xml-viewer .sc-button-group svg{
  color: #fff;
}

.sc-modal-xml-viewer pre {
  color: #0000009e;
  font-size: 12px;
}



.sc-widget-wrapper {
  margin: 0;
  background-color: #fff;
}

.sc-widget-wrapper .markup-overlay {
  position: absolute;
  z-index: 999999999923263;
  background-color: #f2f2f2;
  width: 100%;
  height: 100%;
}

.sc-widget-header {
  height: 40px;
  /* background-image: linear-gradient(to bottom right, var(--primary-color), var(--secondary-color)); */
  background-image: linear-gradient(to bottom right, #12394B, #0091D7);

  padding: 0px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  width: 100%;
  z-index: 99;
}
.sc-widget-header .sc-header-brand {
  flex: 0 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
}

.sc-widget-header .sc-header-brand span {
  flex: 0 0 auto;
  width: 20px;
  height: 20px;
  margin-right: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.sc-widget-header .sc-header-brand span img {
  width: 100%;
  height: 100%;
}

.sc-widget-header .sc-header-brand h2 {
  flex: 1 1 auto;
  color: #fff;
  margin: 0;
  font-size: 14px;
}
.sc-markup-generator {
  margin: 0;
  animation: fadein 1s;
}

.sc-markup-generator .sc-markup-generator__control .sc-controll-buttons {
  display: flex;
  height: 40px;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  background-color: #f2f2f2;
  border-bottom: solid #ccc solid;
  padding: 0 20px;
}

.sc-markup-generator .sc-markup-generator__control .sc-controll-buttons .sc-markup-generator__dropdown {
  margin: 0;
}

.sc-markup-generator .sc-markup-generator__body {
  display: flex;
  flex-direction: column;
  margin: 0;
  height: calc(100vh - 115px);
  overflow-x: hidden;
  overflow-y: auto;
  padding: 10px 12px;
}

.sc-markup-designer__inner {
  margin-top: 10px;
}

.sc-markup-designer .sc-designer-item {
  display: block;
  width: 100%;
  border-bottom: 1px solid rgba(14, 118, 168, 0.2);
  position: relative;
  transition: all 0.2s ease;
  padding: 8px 8px;
}

.sc-markup-designer .sc-designer-item:last-child {
  margin-bottom: 0;
  border-bottom: 0px;
}

.sc-markup-designer .sc-designer-item textarea {
  margin-top: 5px;
}

.sc-markup-designer .sc-designer-item__label {
  flex: 1 1 auto;
  display: flex;
  cursor: pointer;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  font-size: 13px !important;
}
.sc-markup-designer .sc-designer-item__label .sc-label-left {
  flex: 0 0 auto;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  font-size: 13px !important;
}
.sc-markup-designer .sc-designer-item__label .sc-label-left::before {
  content: '';
  width: 2px;
  height: 25px;
  background-color: #cdcccc;
  margin-right: 5px;
}
.sc-markup-designer .sc-designer-item__label .sc-label-left.required-field::before {
  background-color: var(--red-color);
}
.sc-markup-designer .sc-designer-item__label .sc-label-left.recommended-field::before {
  background-color: var(--orange-color);
}

.sc-markup-designer .sc-designer-item__label .sc-label-left.valid-field::before {
  background-color: green;
}

.sc-markup-designer .sc-designer-item__label .arrow {
  flex: 0 0 auto;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}
.sc-markup-designer .sc-designer-item__label .sc-iconset-right {
  /* flex: 0 0 auto; */
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
}

.sc-iconset-right .sc-options-dropdown{
  margin: 0 8px;
}
.sc-iconset-right .select-search__value{
  margin: 0 !important;
}
.sc-iconset-right>:last-child{
  margin-right: 0;
}

.sc-markup-designer .sc-designer-item__label .sc-iconset-right .arrow {
  flex: 0 0 auto;
  margin-right: 8px;
}
.sc-markup-designer .sc-designer-item__label .sc-iconset-right .arrow:last-child {
  flex: 0 0 auto;
  margin-right: 0;
}
.sc-markup-designer .sc-designer-item__label .sc-label-left > :last-child,
.sc-label-left > :nth-child(2) {
  margin-left: 5px;
}

.sc-markup-designer .sc-designer-item__label .sc-label-left > :nth-child(2) {
  opacity: 0.8;
}

.sc-markup-designer .sc-designer-item__label.hide-label .sc-iconset-right .sc-clear-icon {
  position: absolute;
  right: 0;
}

.sc-markup-designer .sc-designer-item__label.hide-label .sc-label-left {
  display: none;
}

.sc-markup-designer .sc-eitherOfFirst {
  border: 1px solid #343a40;
  border-bottom: 0;
}
.sc-markup-designer .sc-eitherOfMiddle {
  border: 1px solid #343a40;
  border-bottom: 0;
  border-top: 0;
}
.sc-markup-designer .sc-eitherOfLast {
  border: 1px solid #343a40;
  border-top: 0;
}
.sc-markup-designer .sc-eitherOfFirst::after, .sc-markup-designer .sc-eitherOfMiddle::after {
  content: 'OR';
  position: absolute;
  left: 48%;
  color: grey;
  font-size: 0.7rem;
}

.sc-widget-body .sc-coming-soon {
  margin-top: 5px;
  padding: 5px;
  width: 100%;
  border: 1px dashed #c3c3c3;
  margin-bottom: 12px;
  border-radius: 2px;
  height: 32px;
}

.sc-widget-body .sc-markup-designer__root {
  padding: 0 !important;
}

.sc-widget-body .sc-designer-item__content {
  font-size: 14px;
  margin-left: 2px;
  word-break: break-all;
}

.sc-field-warning {
  color: var(--red-color);
  font-size: 12px;
}
.sc-field-warning span {
  margin-right: 2px !important;
}
#sc-extension-body .collapse {
  margin: 0 !important;
}
#sc-extension-body .collapse .accordion,
#sc-extension-body .collapsing .accordion {
  margin: 0;
}
#sc-extension-body .collapse .accordion .sc-markup-designer,
#sc-extension-body .collapsing .accordion .sc-markup-designer {
  background-color: #f5fcff;
  border-radius: 4px;
  margin-top: 15px;
  margin-bottom: 15px;
  padding: 10px 15px !important;
}
#sc-extension-body .collapse .sc-designer-item,
#sc-extension-body .collapsing .sc-designer-item {
  width: 100%;
}

/*  IF ACTIVE TEXT SELECTOR :-> HOVERING ELEMENT STYLE  */
.sc-hovered-element {
  user-select: none;
  border: 2px solid orange !important;
}
.sc-sb-inspect-element {
  user-select: none;
  border: 2px dotted orange !important;
}

/*------------------*/

.sc-selector-cursor {
  cursor: pointer;
  color: var(--secondary-text-color);
  line-height: 25px;
}
.sc-selector-cursor:focus {
  outline: 0 !important;
}

.sc-widget-body .sc-active-icon svg {
  fill: #2196f3;
}



.sc-dropdown .select-search {
  position: relative;
  box-sizing: border-box;
}
.sc-dropdown .select-search .select-search__value {
  display: block;
  margin: 10px 0;
}
.sc-dropdown .select-search .select-search__value .select-search__input {
  flex: 1 1 auto;
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  border: solid 1px #dbdbdb;
  font-size: 13px;
  height: 28px;
  padding: 0px 10px;
  border-radius: 4px;
  cursor: pointer;
}
.sc-dropdown .select-search.is-disabled {
  opacity: 0.5;
}

.sc-dropdown .select-search__value {
  position: relative;
}

.sc-dropdown .select-search__value::after {
  content: "";
  display: inline-block;
  position: absolute;
  top: calc(50% - 5px);
  right: 8px;
  width: 8px;
  height: 8px;
  transform: rotate(45deg);
  border-right: 1px solid #00000092;
  border-bottom: 1px solid #00000092;
  pointer-events: none;
}

.sc-dropdown .select-search__input::-webkit-search-decoration,
.sc-dropdown .select-search__input::-webkit-search-cancel-button,
.sc-dropdown .select-search__input::-webkit-search-results-button,
.sc-dropdown .select-search__input::-webkit-search-results-decoration {
  -webkit-appearance: none;
}

.sc-dropdown .select-search--multiple .select-search__input {
  border-radius: 2px 2px 0 0;
}

.sc-dropdown .select-search__input:not([readonly]):focus {
  -webkit-box-shadow: 0px 0px 0px 0px rgba(255, 255, 255, 0);
  -moz-box-shadow: 0px 0px 0px 0px rgba(255, 255, 255, 0);
  box-shadow: 0px 0px 0px 0px rgba(255, 255, 255, 0);
  border: rgba(0, 0, 0, 0.3) solid 1px;
}
.sc-searchable-options-dropdown{
  margin-right: 12px;
}

.sc-searchable-options-dropdown .select-search__input{
  text-align: right;
  padding-right: 25px !important;
  color: #444242 !important;
  font-size: 14px !important;
  border: none !important;
  background-color: transparent;
}
.sc-searchable-options-dropdown .select-search__input:not([readonly]):focus{
  border: rgba(0, 0, 0, 0.3) solid 1px !important;
  text-align: left;
}
.sc-dropdown .select-search__select {
  background: #fff;
  font-size: 13px !important;
  box-shadow: 0 0.0625rem 0.125rem rgba(0, 0, 0, 0.15);
}

.sc-dropdown .select-search .select-search__select {
  position: absolute;
  z-index: 2;
  top: 30px;
  right: 0;
  left: 0;
  font-size: 13px !important;
  border-radius: 3px;
  overflow: auto;
  max-height: 240px;
}
.sc-options-dropdown .select-search .select-search__select {
  left: -220px;
  min-width: 150px;
}

.sc-options-dropdown .select-search__value::after{
  display: none;
}

.sc-options-dropdown .select-search__value{
  margin: 0;
}

.sc-dropdown .select-search__options {
  list-style: none;
  padding: 0 !important;
  margin: 0 !important;
}

.sc-dropdown .select-search__option {
  display: block;
  height: 30px;
  width: 100%;
  padding: 0 16px !important;
  background: #fff;
  border: none;
  outline: none;
  font-size: 13px;
  text-align: left;
  cursor: pointer;
  margin: 0 !important;
  color: #5f6368;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis
}

.sc-dropdown .select-search__option.is-selected {
  background: #f5fcff;
  color: #4c4e50;
}

.sc-dropdown .select-search__option.is-highlighted,
.sc-dropdown .select-search__option:not(.is-selected):hover {
  background: #f5fcff;
}

.sc-icon-disabed > i {
  cursor: not-allowed;
}

.sc-icon-disabed svg {
  color: rgba(128, 128, 128, 0.733);
}

.sc-custom-tooltip {
  box-shadow: 0 1px 15px rgba(0, 0, 0, 0.05);
}

.sc-custom-tooltip-header {
  padding: 0.2rem 30px 0.2rem 7px;
  margin-bottom: 0;
  font-size: 0.9375rem;
  color: #323d47;
  background-color: #f9fafb;
  border: 1px solid rgb(241, 242, 243);
  border-bottom: 1px solid #eaedf1;
  border-top-left-radius: calc(0.3rem - 1px);
  border-top-right-radius: calc(0.3rem - 1px);
  letter-spacing: 0.2px;
  font-size: 16px;
  font-weight: 500;
}
.sc-custom-tooltip-header h3{
  margin: 0;
}
.sc-custom-tooltip-header::after{
  content: "";
  min-width: 13px;
  min-height: 13px;
  background-color: var(--primary-color);
  position: absolute;
  transform: rotate(45deg);
}


.sc-custom-tooltip-body {
  max-width: 300px;
  padding: 0.5rem 0.75rem;
  color: #757575;
  font-style: normal;
  font-weight: 400;
  line-height: 1.9;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  word-spacing: normal;
  white-space: normal;
  line-break: auto;
  font-size: 14px;
  word-wrap: break-word;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #f1f2f3;
  border-radius: 0 0 3px 3px;
}

.sc-custom-tooltip-body * {
  list-style: none;
}

.sc-custom-tooltip-header > :last-child:hover {
  opacity: 0.9;
}

.sc-custom-tooltip-header > :last-child {
  opacity: 0.2;
  transition: opacity 0.15s linear;
  position: absolute;
  right: 10px;
}

.sc-source-links {
  display: flex;
  justify-content: space-between;
  background-color: #fff;
  font-size: 0.7rem;
  padding: 0.2rem 0.75rem 0;
}

.sc-custom-tooltip-header > a {
  margin-left: 5px;
}

.sc-custom-tooltip.blue {
  box-shadow: 0px 0px 5px var(--primary-color);
}

.sc-custom-tooltip.blue > .sc-custom-tooltip-header,
.sc-custom-tooltip-header svg {
  border: var(--primary-color);
  background: linear-gradient(to bottom right, var(--primary-color), var(--secondary-color));
  color: #fff;
}
.sc-custom-tooltip.blue > .sc-custom-tooltip-header,
.sc-custom-tooltip-header::after {
  border-color: var(--secondary-color);
  background-color: var(--secondary-color);
}

.sc-custom-tooltip.blue > .sc-custom-tooltip-body {
  border: none;
}

.sc-custom-tooltip.blue > .sc-custom-tooltip-header > :last-child {
  opacity: 0.5;
}

.sc-custom-tooltip.blue:hover > .sc-custom-tooltip-header > :last-child {
  opacity: 1;
}

.sc-custom-body-panel {
  position: absolute;
  top: 0;
  left: 0;
  min-height: 100%;
  min-width: 100%;
  padding-top: 40px;
  background-color: #fff;
  /* animation: fadein 1s; */
}

.sc-custom-panel-header {
  display: flex;
  align-items: center;
  padding:5px 0 2px 15px;
  background-color: #f2f2f2;
}
.sc-custom-panel-header h3 {
  font-size: 14px;
  margin: 0 0 0 10px;
  text-transform: capitalize;
  width: 100%;
}

.sc-custom-panel-content {
  padding: 10px;
}


.sc-array-generator-panel{
  margin: 0 10px;
}

.sc-array-generator-static-field{
  margin-bottom: 5px;
}

.sc-array-generator-static-field .sc-static-field-label{
  font-size: 16px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
}

.sc-array-generator-static-field .sc-static-field-label .sc-iconset-right .sc-clear-icon{
  margin-left: 10px;
}

.sc-array-generator-static-field .sc-static-field-value{
  margin-left: 15px;
  font-size: 12px;
  color: var(--secondary-text-color);
}

.sc-array-generator-btn{
  text-align: right;
  font-size: 14px;
}
.sc-array-generator-fields{
  margin-left: 17px;
}

.sc-array-generator-json{
  height: 250px;
  margin-top: 20px;
  width: 350px;
  border: 2px solid #eee;
  overflow: auto;
  font-size: 12px;
}

.sc-hovered-siblings-element{
  background-color: rgb(208, 237, 253) !important;
}
.sc-hovered-siblings-element * {
  background-color: rgb(208, 237, 253) !important;
}

.sc-disabled-element{
  pointer-events: none;
  opacity: .5;
}

.sc-matches-count{
  font-size: 14px;
  margin-top: 10px;
}

.sc-sibling-1{
  background-color: lightcyan;
}
.sc-sibling-1 *{
  background-color: lightcyan;
}

.sc-sibling-2{
  background-color: lightyellow;
}
.sc-sibling-2 *{
  background-color: lightyellow;
}

.sc-sibling-3{
  background-color: lightpink;
}
.sc-sibling-3 *{
  background-color: lightpink;
}

.sc-sibling-4{
  background-color: lightgrey;
}
.sc-sibling-4 *{
  background-color: lightgrey;
}

.sc-sibling-5{
  background-color: lightgoldenrodyellow;
}
.sc-sibling-5 *{
  background-color: lightgoldenrodyellow;
}

.sc-sibling-9{
  background-color: lightgreen;
}
.sc-sibling-9 *{
  background-color: lightgreen;
}

.sc-sibling-7{
  background-color: lightseagreen;
}
.sc-sibling-7 *{
  background-color: lightseagreen;
}
.sc-sibling-8{
  background-color: lightcoral;
}
.sc-sibling-8 *{
  background-color: lightcoral;
}

.sc-result-wrapper{
  padding: 10px;
  border: 1px solid #eee;
  margin-bottom: 15px;
  box-shadow: 0px 0px 5px var(--primary-color);
  max-width: 360px;
  position: relative;
  min-height: 30px;
}
.sc-result-wrapper .sc-remove-item,.sc-drag-item{
  position: absolute;
  right: 2px;
  top: 0;
  opacity: 0.2;
}
.sc-result-wrapper .sc-drag-item{
  left: 2px;
  right: auto;
  opacity: .1;
}
.sc-result-wrapper:hover .sc-remove-item,.sc-result-wrapper:hover .sc-drag-item{
  opacity: .9;
}
#sc-extension-body .dragged .sc-result-wrapper {
  opacity: 0.25;
  border-top: 1px solid #999999;
  border-bottom: 1px solid #ffffff;
}

.sc-floating .sc-result-wrapper {
  cursor: pointer;
  background-color: var(--primary-color);
  box-shadow: 0 4px 20px #666666;
  color: #fff;
}
.sc-result-field{
  display: flex;
  padding: 5px 0;
}

.sc-result-label{
  font-weight: 500;
  margin-right: 12px;
  font-size: 14px;
}

.sc-result-value{
  font-size: 14px;
  word-break: break-all;
}

.sc-result-field.nested{
  display: block;
}
.sc-result-wrapper.inner{
  margin-left: 15px;
  margin-bottom: 0;
  box-shadow:none;
}

.sc-result-wrapper.inner .sc-result-label,.sc-result-wrapper.inner .sc-result-value{
  font-size: 13px;
}


.sc-native-modal-wrapper{

  position: absolute;
  top: 80px;
  left: 0;
  height: calc(100% - 80px);
  width: 400px;
  background-color: #fff;
  animation: fadein .2s;
  overflow: auto;
}

.sc-native-modal-close{
  position: absolute;
  right: 5px;
}

.sc-native-modal-content{
  padding-top: 15px;
  padding: 10px;
}

@keyframes expand{
  0%{
    height:0px;
    opacity: 0;
  }
  25%{
    height:25%;
    opacity: .25;
  }
  50%{
    height:50%;
    opacity: .5;
  }
  75%{
    height:70%;
    opacity: .75;
  }
  100%{
    height:calc(100% - 80px);
    opacity: 1;
  }
}

.sc-errors-list{
  padding: 0 20px;
  list-style: none;
}
.sc-errors-list :first-child{
  background: none;
  margin-bottom: 0;
  padding: 0;
}

.sc-errors-list li{
  padding: 3px 10px;
  font-size: 12px;
  color: var(--red-color);
  background-color: rgba(238, 238, 238, 0.801);
  margin: 5px 0;
}
.sc-eitherof-errors{
  margin: 0 10px;
  color: var(--main-text-color);
}
.sc-eitherof-errors label{
  font-size: 14px;
  margin: 0;
}
.sc-eitherof-errors p{
  padding: 0;
  margin-bottom: 5px;
  font-size: 13px;
  margin-left: 4px;
}
@keyframes fadein {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
.sc-css-selector-modal{
  top: 40px !important;
  height: calc(100% - 40px);
  padding-top: 10px;
}

.sc-array-buttons{
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 5px;
  margin-top: 5px;
}

.sc-array-buttons button{
  font-size: 13px;
  line-height: 15px;
  height: auto !important;
}

.sc-array-generator-manual-panel .sc-array-item-wrapper{
  position: relative;
}
.sc-array-generator-manual-panel .sc-array-add-icon>span>i{
  color: green;
}
.sc-array-generator-manual-panel .sc-array-add-icon{
  text-align: right;
  margin-right: 12px;
}
.sc-array-generator-manual-panel .sc-minus-icon{
  position: absolute;
  top: 0;
  right: -10px;
  color: var(--red-color);
}

.sc-array-generator-manual-panel .sc-array-item{
  margin: 10px 7px;
  border: 1px solid #eee;
  border-radius: 3px;
  padding: 8px;
}

#sc-plugin-position-switcher{
  position: fixed;
  z-index: 214748364;
  right: calc(400px - 23px);
  top: calc(50% - 25px);
  opacity: .7;
  background-color: var(--primary-color);
  height: 50px;
  display: none;
  height: 42px;
  border-radius: 7px;
  transform: rotate(45deg) !important;
  width: 42px;
  animation: fadein 4s;
}

#sc-plugin-position-switcher.arrow-left{
  left: calc(400px - 23px);
  right: auto;
}

#sc-plugin-position-switcher:hover{
  opacity: 1;
}

#sc-plugin-position-switcher>div{
  width: 100%;
  height: 100%;
  cursor: pointer;
}
#sc-plugin-position-switcher svg{
  height: 20px;
  width: 20px;
  margin-top: 20px;
  margin-left: 2px;
  transform: rotate(-45deg);
  color: #f2f2f2;
}

#sc-plugin-position-switcher.arrow-left svg{
  margin-top: 2px;
  margin-left: 20px;
}
.sc-field-status-tab{
  display: flex;
  flex-direction: row;
  font-size: 15px;
  margin-top: 2px;
}
.sc-field-status-tab >  span {
  flex: 1 1;
  padding: 2px 10px;
  text-align: center;
  cursor: pointer;
}

.sc-field-status-tab > span:nth-child(1) {
  color: #0688c9;
}
.sc-field-status-tab > span:nth-child(2) {
  color: #dc3545;
}
.sc-field-status-tab > span:nth-child(3) {
  color: #ffc107;
}
.sc-field-status-tab > span:nth-child(4) {
  color: #6c757d;
}

.sc-field-status-tab > .active:nth-child(1) {
  background-color: #0688c9;
  color: #fff;
}
.sc-field-status-tab > .active:nth-child(2) {
  background-color: #dc3545;
  color: #fff;
}
.sc-field-status-tab > .active:nth-child(3) {
  background-color: #ffc107;
  color: #212529;
}
.sc-field-status-tab > .active:nth-child(4) {
  background-color: #6c757d;
  color: #fff;
}
.sc-field-status-tab > span:nth-child(1):not(.active):hover {
  background-color: #0688c9b3;
  color: #fff;
}
.sc-field-status-tab > span:nth-child(2):not(.active):hover {
  background-color: #dc3545b3;
  color: #fff;
}
.sc-field-status-tab > span:nth-child(3):not(.active):hover {
  background-color: #ffc107b3;
  color: #212529;
}
.sc-field-status-tab > span:nth-child(4):not(.active):hover {
  background-color: #6c757db3;
  color: #fff;
} 


.sc-left-poistion{
  right: auto !important;
  left: 0 !important;
}
.sc-right-poistion{
  right: 0;
  left: auto;
}

.sc-help-alert{
  background: #dc4145db;
  color: #fff;
  font-size: 12px;
  padding: 1px 13px;
  display: flex;
  justify-content: space-between;
  width: 100%;
}
.sc-help-alert a{
  margin-left: 3px;
  color: #fff;
}

.sc-help-alert a:active{
  color: #fff;
}

.sc-help-alert a:focus{
  color: #fff;
}
.sc-help-alert a:hover{
  color: #ccc;
}
.sc-help-alert .sc-help-close i{
  line-height: normal;
  color: #fff;
}

.sc-stick-bottom{
  width: 70%;
  position: absolute;
  bottom: 75px;
  border-radius: 3px;
}

.sc-auto-array-title{
  display: flex;
  justify-content: space-between;
}
.sc-auto-array-title a{
  font-size: 12px;
  margin-right: 5px;
  font-size: 12px;
  text-decoration: none !important;
  color: var(--secondary-text-color);
  background-color: #efff0036;
  padding: 2px 0;
}
.sc-auto-array-title a>:last-child{
  margin-left: 5px;
}
.sc-auto-array-title a:hover{
  color: var(--main-text-color);
}
.sc-auto-array-title a i{
  line-height: normal;
  color: var(--secondary-text-color);
}

.sc-js-conversions{
  margin-bottom: 10px;
}

.sc-text-transformers .sc-text-transformer{
  padding-bottom: 5px;
  padding-left: 5px;
}
.sc-text-transformers .sc-text-transformer:hover{
  background-color: #f0f0f09d;
}

.sc-text-transformer>:last-child{
  width: calc(100% - 35px);
}

.sc-text-transformer .sc-conversion-controls{
  display: flex;
  width: 100%;
  align-items: center;
}
.sc-conversion-controls>:first-child{
  min-width: calc(100% - 35px);
}
.sc-conversion-controls .sc-conversion-icons{
  width: 35px;
  text-align: center;
}
.sc-conversion-icons i{
  color: red;
}
.sc-image-modal-empty{
  height: 200px;
  text-align: center;
  padding-top: 75px;
  font-size: 20px;
  font-weight: bold;
  color: var(--secondary-text-color);
}
.sc-prompt-error{
  border: 1px solid red;
  margin-bottom: 5px;
}
.sc-red-dot{
  position: absolute;
  background-color: rgb(68, 199, 68);
  content: '';
  height: 5px;
  width: 5px;
  left: 8px;
  border-radius: 50%;
}
.sc-dropdown .select-search__group .select-search__group-header{
  padding-left: 16px !important;
}
.sc-dropdown .select-search__group ul{
  padding-left: 16px !important;
}
.sc-selector-value{
  color: #444242 !important;
}


#sc-schema-notify-contaner {
  position: fixed;
  top: 40px;
  z-index: 100;
  left: 50%;
  padding: 2px 10px;
  border-radius: 2px;
  color: white;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 0 14px rgba(0, 0, 0, 0.05);
  width: 100%;
  text-align: center;
}

#sc-schema-notify-contaner.msg-success {
  background-color: #28a745;
}

#sc-schema-notify-contaner.msg-warning {
  color: darken(#ffc107, 30%);
  background-color: #ffc107;
}

#sc-schema-notify-contaner.msg-danger {
  background-color: #dc3545;
}

#sc-schema-notify-contaner.msg-info {
  background-color: #17a2b8;
}

/*fade*/
#sc-schema-notify-contaner {
  /* opacity: 0; */
  visibility: hidden;
  transition: all 1s;
  -webkit-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
}

#sc-schema-notify-contaner.active {
  /* opacity: 1; */
  visibility: visible;
  transform: translateX(-50%) translateY(0%);
}

#sc-schema-notify-contaner {
  transform: translateY(-100%) translateX(-50%);  
}

.sc-multiple-text-item{
  background-color: #f9f9f9;
  padding: 2px;
  margin-bottom: 5px;
  min-height: 31px;
}
.sc-multiple-text-item .sc-iconset{
  display: flex;
  justify-content: flex-end;
}

.sc-multiple-text-item .sc-iconset-right{
  background-color: #fff;
}
.sc-multiple-text-item .sc-iconset-right .arrow{
  margin-left: 3px;
  font-size: 13px;
  margin-right: 7px;
}
.sc-multiple-text-handler{
  margin-left: 10px;
}

.sc-decision-section{
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  list-style: none;
  margin-bottom: 0;
  padding: 25vh 0 0 0;
  color: white;
  background-image: linear-gradient(to bottom, var(--primary-color), #0f76a8c9);
  min-height: calc(100vh - 40px);
}
.sc-decision-section li{
  padding: 20px 0;
  width: 80%;
  margin-bottom: 10px;
  box-shadow: 0px -1px 11px 3px rgba(0,0,0,.1);
  cursor: pointer;
  border-radius: 3px;
  text-transform: uppercase;
  color: var(--primary-color);
  background: #fff;
}
.sc-decision-section li:hover{
  box-shadow: 0px -1px 11px 3px rgba(0, 0, 0, 0.4);
}
.sc-decision-section__gtm{
  padding-top: 10vh;
  background-image: linear-gradient(to bottom, var(--primary-color), #0f76a8c9);
  min-height: calc(100vh - 40px);
}
.sc-decision-section__gtm label,.sc-decision-section__gtm h3{
  color: #fff !important;
}
.sc-gtm-data-collection{
  padding: 5px 10px 5px 20px;
  display: flex;
  flex-direction: column;
}
.sc-gtm-data-collection button{
  align-items: end;
  width: calc(100% - 17px);
  height: 32px;
  line-height: 11px;
  margin-top: 10px;
}
.sc-gtm-dropdown--item{
  display: grid;
  grid-template-columns: 3.5fr 5fr 17px;
  align-items: center;
}
.sc-gtm-data-collection .sc-custom-alert{
  margin: 0;
  font-size: 12px;
  margin-top: 10px;
  padding: 5px 10px;
  width: calc(100% - 17px);
}
.sc-gtm-data-collection h3{
  margin-bottom: 10px;
  font-size: 18px;
  color: var(--main-text-color) !important;
}
.sc-gtm-dropdown--item label{
  font-size: 14px;
  color: var(--secondary-text-color) !important;
  margin: 0;
}
.sc-gtm-dropdown--item .arrow,.sc-gtm-dropdown--item .sc-loader-wrapper{
  margin-left: 5px;
  color: #fff;
}
.sc-markup-control-btns{
  display: flex;
}
.sc-markup-control-btns button .sc-btn-label{
  margin-right: 5px;
}
.sc-markup-control-btns button{
  padding: 0 15px;
  margin-left: 10px;
}
.sc-publish-markup-modal h5{
  font-size: 14px;
  margin-bottom: 15px;
}
.sc-publish-markup-modal h3{
  margin-top: 15px;
  font-size: 17px;
}
.sc-publish-markup-modal .sc-custom-alert div{
  margin: 0 5px;
  font-size: 12px;
  display: grid;
  grid-template-columns: 5fr 6fr;
}
.sc-publish-markup-modal .sc-custom-alert div label{
    font-weight: 600;
}
.sc-publish-markup-modal .sc-gtm-buttons{
  display: flex;
  justify-content: center;
}
.sc-publish-markup-modal .sc-gtm-buttons button{
  padding: 3px 10px;
  margin-right: 10px;
  display: flex;
  align-items: center;
}
.sc-gtm-buttons button i,.sc-gtm-buttons button .sc-loader-wrapper{
  color: #fff;
  margin-right: 5px;
}
.sc-publish-markup-modal .sc-action-info{
  margin-top: 15px;
}
.sc-publish-markup-modal .sc-action-info .sc-custom-alert{
  padding: 2px 11px;
  font-size: 13px;
}
#sc-fullpage-loader{
  height: calc(100vh - 0px);
  position: absolute;
  background: #000;
  width: 100%;
  z-index: 99;
  opacity: 0.5;
  text-align: center;
  left: 0;
  top: 0;
  align-items: center;
  display: none;
  justify-content: center;
}
#sc-fullpage-loader .sc-text{
  font-size: 32px;
  color: #fff;
  display: flex;
  align-items: center;
}
#sc-fullpage-loader .sc-text svg{
  height: 2em;
  margin-right: 5px;
  color: #fff;
}

.sc-gtm-data-collection.light .sc-loader-wrapper{
  color: #000;
}

.sc-g2-logo{
  width: 1.5em;
  height: 1.5em;
  margin-right: 2px;
}
.sc-schema-control-btn{
  flex: 0 0 auto;
  order: 2;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  border: solid 1px #dbdbdb;
  font-size: 13px;
  height: 28px;
  padding: 0px 10px;
  border-radius: 4px;
  outline: 0px !important;
  transition: background-color 200ms;
}
.sc-schema-control-btn:hover {
  background-color: #dbdbdb;
  transition: background-color 200ms;
}
.sc-schema-control-btn:active {
  background-color: #c4c4c4;
  transition: background-color 200ms;
}
.sc-schema-control-btn :last-child{
  margin-left: 2px;
}
.sc-continue-g-button{
  min-height: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.sc-continue-g-button button{
  display: flex;
  align-items: center;
}
.sc-deploy-modal{
  width: 550px !important;
}
.sc-header-icons .select-search__value > button {
  background-color: transparent;
  outline: none;
  border: none;
  color: white;
  font-size: 14px;
}
.sc-header-icons{
  display: flex;
  align-items: center;
}
.sc-header-icons>:first-child{
  width: 50px;
  margin-right: 12px;
  text-align: right;
}
.sc-header-icons .select-search__value svg{
  height: 15px;
  width: 15px;
}
.sc-header-icons .select-search__value::after{
  display: none;
}
.sc-header-icons i{
  color: #fff;
}
.sc-detected-schemas{
  width: 80%;
  min-height: 100px;
}
.sc-detected-schemas > h6 {
  text-align: left;
}
.sc-detected-schemas ul{
  /* max-height: calc(33vh - 45px - 20px - 40px); */
  max-height: 114px;
  overflow: auto;
  min-height: 70px;
  padding: 0;
  margin: 0;
}
.sc-detected-schemas .sc-empty-msg{
  margin-top: 30px;
  font-style: italic;
}
.sc-detected-schemas li{
  padding: 5px 30px;
  cursor: pointer;
  display: flex;
  border-radius: 2px;
  align-items: center;
  justify-content: space-between;
  background-color: rgba(243, 243, 243, 0.445);
  font-size: 14px;
  margin: 3px 0;
  position: relative;
}
.sc-detected-schemas li label{
  color: var(--main-text-color);
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  margin: 0;
  cursor: pointer;
}
.sc-detected-schemas li>div{
  min-width: 48%;
}

.sc-detected-schemas li span,.sc-detected-schemas li span svg{
  color: var(--secondary-text-color);
  cursor: pointer;
  font-style: italic;
  margin-left: 4px;
  font-size: 12px;
}
.sc-detected-schemas li span:hover{
  text-decoration: underline;
}
.sc-rotate-icon svg{
  animation: spinner-border 1.8s linear infinite;
}

#sc-extension-body .custom-switch .custom-control-label{
  cursor: pointer;
}
#sc-extension-body .custom-control-input:checked~.custom-control-label::before{
  border-color: var(--primary-color) !important;
  background-color: var(--primary-color) !important;
}
#sc-extension-body .custom-switch .sc-custom-label{
  font-size: 13px;
  color: var(--main-text-color);
}
.sc-left-auto{
  margin-left: auto;
}
.sc-result-type-box {
  border: 1px solid var(--primary-color);
  border-radius: 0.25rem;
  padding: 0px 5px;
}

.sc-result-copy-box {
  background-color: #f2f2f282;
  padding: 10px;
  border-radius: 3px;
  margin-bottom: 10px;
  position: relative;
}

.sc-result-copy-box span {
  position: absolute;
  right: 10px;
  opacity: .6;
  height: 1.5rem;
  width: 1.5rem;
  background: #1c98d38c;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 3px;
  color: #fff;
}
.sc-result-copy-box span i {
  color: white !important;
}
.sc-result-copy-box span:hover{
  opacity: 1;
}

.sc-listjson-head {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 13px;
  margin: 0 20px 0 10px;
  font-size: 13px;
}

.sc-listjson-head .nav-item {
  color: var(--main-text-color);
  cursor: pointer;
  border: 1px solid transparent;
  border-bottom: 1px solid #ddd;
  padding: 2px 10px;
}

.sc-listjson-head .nav-item:hover {
  color: var(--secondary-color);
}

.sc-listjson-head .nav-item.active {
  color: var(--secondary-color);
  border: 1px solid #ddd;
  border-bottom-color: #fff;
  border-radius: 3px 3px 0 0;
}

.sc-multitypeItem > .sc-trashIcon {
  display: flex;
  justify-content: flex-end;
  margin: 0 4px -38px 0;
  
}