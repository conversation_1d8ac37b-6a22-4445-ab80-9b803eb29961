// Schema Speed Assistant - Content Capture System
// Visual element selection and content extraction with hover highlighting

class ContentCapture {
  constructor() {
    this.isActive = false;
    this.currentField = null;
    this.selectedElement = null;
    this.selectorCandidates = [];
    this.currentSelectorIndex = 0;
    this.captureSession = {};
    this.overlay = null;
    this.highlightedElement = null;
    this.transformations = new ContentTransformations();
  }

  initialize() {
    this.createOverlayElements();
    this.attachEventListeners();
    this.loadCaptureSession();
  }
  
  createOverlayElements() {
    // Create main overlay
    this.overlay = document.createElement('div');
    this.overlay.id = 'schema-speed-capture-overlay';
    this.overlay.innerHTML = `
      <div class="schema-speed-capture-toolbar">
        <div class="schema-speed-field-selector">
          <label>Field to Capture:</label>
          <select id="schema-speed-field-dropdown">
            <option value="">Select Field to Capture</option>
          </select>
        </div>
        <div class="schema-speed-capture-modes">
          <button id="schema-speed-auto-capture" class="schema-speed-mode-btn">Auto Detect</button>
          <button id="schema-speed-manual-capture" class="schema-speed-mode-btn active">Manual Select</button>
        </div>
        <div class="schema-speed-actions">
          <button id="schema-speed-clear-selection">Clear</button>
          <button id="schema-speed-save-profile">Save Profile</button>
          <button id="schema-speed-close-capture">Done</button>
        </div>
      </div>
      <div class="schema-speed-selection-info" id="schema-speed-selection-info" style="display: none;">
        <div class="schema-speed-selector-cycling">
          <button id="schema-speed-prev-selector">◀</button>
          <span id="schema-speed-selector-display"></span>
          <button id="schema-speed-next-selector">▶</button>
        </div>
        <div class="schema-speed-preview-content" id="schema-speed-preview-content"></div>
        <div class="schema-speed-transformation-options" id="schema-speed-transformation-options">
          <select id="schema-speed-transformation-select">
            <option value="">No Transformation</option>
            <option value="removeNumbers">Remove Numbers</option>
            <option value="removeSpecific">Remove Specific String</option>
            <option value="trimStart">Remove First N Characters</option>
            <option value="trimEnd">Remove Last N Characters</option>
            <option value="patternMatch">Pattern Matching</option>
            <option value="custom">Custom Transformation</option>
          </select>
        </div>
        <div class="schema-speed-capture-actions">
          <button id="schema-speed-confirm-capture">Capture This</button>
          <button id="schema-speed-cancel-capture">Cancel</button>
        </div>
      </div>
    `;
    
    // Apply styles
    this.overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.1);
      z-index: 10000;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      pointer-events: none;
      display: none;
    `;
    
    // Make toolbar interactive
    const toolbar = this.overlay.querySelector('.schema-speed-capture-toolbar');
    const selectionInfo = this.overlay.querySelector('.schema-speed-selection-info');
    toolbar.style.pointerEvents = 'auto';
    selectionInfo.style.pointerEvents = 'auto';
    
    document.body.appendChild(this.overlay);
    this.attachOverlayListeners();
  }
  
  attachEventListeners() {
    // Prevent page interactions while capturing
    document.addEventListener('click', this.handleClick.bind(this), true);
    document.addEventListener('mouseover', this.handleMouseOver.bind(this), true);
    document.addEventListener('mouseout', this.handleMouseOut.bind(this), true);
    document.addEventListener('keydown', this.handleKeyDown.bind(this), true);
    
    // Listen for messages from background script
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
    });
    
    // Also listen for window messages (legacy support)
    window.addEventListener('message', this.handleWindowMessage.bind(this));
  }
  
  attachOverlayListeners() {
    // Field dropdown change
    const fieldDropdown = document.getElementById('schema-speed-field-dropdown');
    if (fieldDropdown) {
      fieldDropdown.addEventListener('change', (e) => {
        this.currentField = e.target.value;
        if (this.currentField) {
          this.showFieldInstructions();
        }
      });
    }
    
    // Mode buttons
    const autoCaptureBtn = document.getElementById('schema-speed-auto-capture');
    if (autoCaptureBtn) {
      autoCaptureBtn.addEventListener('click', () => {
        this.handleAutoCapture();
      });
    }
    
    const manualCaptureBtn = document.getElementById('schema-speed-manual-capture');
    if (manualCaptureBtn) {
      manualCaptureBtn.addEventListener('click', () => {
        this.handleManualCapture();
      });
    }
    
    // Action buttons
    const clearSelectionBtn = document.getElementById('schema-speed-clear-selection');
    if (clearSelectionBtn) {
      clearSelectionBtn.addEventListener('click', () => {
        this.clearSelection();
      });
    }
    
    const saveProfileBtn = document.getElementById('schema-speed-save-profile');
    if (saveProfileBtn) {
      saveProfileBtn.addEventListener('click', () => {
        this.saveProfile();
      });
    }
    
    const closeCaptureBtn = document.getElementById('schema-speed-close-capture');
    if (closeCaptureBtn) {
      closeCaptureBtn.addEventListener('click', () => {
        this.closeCaptureMode();
      });
    }
    
    // Selector cycling
    const prevSelectorBtn = document.getElementById('schema-speed-prev-selector');
    if (prevSelectorBtn) {
      prevSelectorBtn.addEventListener('click', () => {
        this.cyclePreviousSelector();
      });
    }
    
    const nextSelectorBtn = document.getElementById('schema-speed-next-selector');
    if (nextSelectorBtn) {
      nextSelectorBtn.addEventListener('click', () => {
        this.cycleNextSelector();
      });
    }
    
    // Capture actions
    const confirmCaptureBtn = document.getElementById('schema-speed-confirm-capture');
    if (confirmCaptureBtn) {
      confirmCaptureBtn.addEventListener('click', () => {
        this.confirmCapture();
      });
    }
    
    const cancelCaptureBtn = document.getElementById('schema-speed-cancel-capture');
    if (cancelCaptureBtn) {
      cancelCaptureBtn.addEventListener('click', () => {
        this.cancelCapture();
      });
    }
  }
  
  handleMessage(message, sender, sendResponse) {
    if (message && message.action === 'startCapture') {
      this.startCaptureMode(message.fieldName);
      sendResponse({ success: true });
    }
  }
  
  handleWindowMessage(event) {
    if (event.data && event.data.type === 'SCHEMA_SPEED_START_CAPTURE') {
      this.startCaptureMode(event.data.fieldName);
    }
  }
  
  startCaptureMode(fieldName = null) {
    this.isActive = true;
    this.currentField = fieldName;
    
    // Show the overlay
    if (this.overlay) {
      this.overlay.style.display = 'block';
    }
    
    // Populate field dropdown with common schema fields
    this.populateFieldDropdown();
    
    if (fieldName && fieldName !== 'manual') {
      const dropdown = document.getElementById('schema-speed-field-dropdown');
      if (dropdown) {
        dropdown.value = fieldName;
        this.showFieldInstructions();
      }
    }
  }
  
  populateFieldDropdown() {
    const dropdown = document.getElementById('schema-speed-field-dropdown');
    if (!dropdown) return;
    
    const commonFields = [
      'headline', 'title', 'author', 'datepublished', 'description', 
      'image', 'url', 'publisher', 'keywords', 'category'
    ];
    
    dropdown.innerHTML = '<option value="">Select Field to Capture</option>';
    commonFields.forEach(field => {
      const option = document.createElement('option');
      option.value = field;
      option.textContent = field.charAt(0).toUpperCase() + field.slice(1);
      dropdown.appendChild(option);
    });
  }
  
  showFieldInstructions() {
    const instructions = {
      headline: 'Click on the main headline or title of the article',
      title: 'Click on the main title or headline',
      author: 'Click on the author name or byline',
      datepublished: 'Click on the publication date or timestamp',
      description: 'Click on the article description or summary',
      image: 'Click on the main image or featured image',
      url: 'Click on the URL or link element',
      publisher: 'Click on the publisher name or logo',
      keywords: 'Click on keywords or tags',
      category: 'Click on the category or section name'
    };
    
    const instruction = instructions[this.currentField] || 'Click on the element you want to capture';
    
    // Show instruction in a temporary overlay
    const instructionEl = document.createElement('div');
    instructionEl.textContent = instruction;
    instructionEl.style.cssText = `
      position: fixed;
      top: 80px;
      left: 50%;
      transform: translateX(-50%);
      background: var(--schema-speed-primary, #7c3aed);
      color: white;
      padding: 8px 16px;
      border-radius: 6px;
      font-size: 14px;
      z-index: 10001;
      pointer-events: none;
    `;
    
    document.body.appendChild(instructionEl);
    
    setTimeout(() => {
      if (instructionEl.parentNode) {
        instructionEl.parentNode.removeChild(instructionEl);
      }
    }, 3000);
  }
  
  handleClick(event) {
    if (!this.isActive) return;
    
    // Don't interfere with overlay interactions
    if (this.overlay.contains(event.target)) return;
    
    event.preventDefault();
    event.stopPropagation();
    
    if (this.currentField) {
      this.selectElement(event.target);
    }
  }
  
  handleMouseOver(event) {
    if (!this.isActive || !this.currentField) return;
    if (this.overlay.contains(event.target)) return;
    
    this.highlightElement(event.target);
  }
  
  handleMouseOut(event) {
    if (!this.isActive) return;
    if (this.overlay.contains(event.target)) return;
    
    this.removeHighlight();
  }
  
  handleKeyDown(event) {
    if (!this.isActive) return;
    
    if (event.key === 'Escape') {
      this.closeCaptureMode();
    }
  }
  
  highlightElement(element) {
    // Remove previous highlight
    this.removeHighlight();
    
    // Add highlight to current element
    element.style.outline = '2px solid var(--schema-speed-primary)';
    element.style.outlineOffset = '2px';
    element.style.backgroundColor = 'rgba(84, 19, 147, 0.1)';
    this.highlightedElement = element;
  }
  
  removeHighlight() {
    if (this.highlightedElement) {
      this.highlightedElement.style.outline = '';
      this.highlightedElement.style.outlineOffset = '';
      this.highlightedElement.style.backgroundColor = '';
      this.highlightedElement = null;
    }
  }
  
  selectElement(element) {
    this.generateSelectorCandidates(element);
    this.currentSelectorIndex = 0;
    this.showSelectionInterface(element);
  }
  
  generateSelectorCandidates(element) {
    const candidates = [];
    
    // ID selector (highest priority)
    if (element.id) {
      candidates.push({
        selector: `#${element.id}`,
        specificity: 100,
        type: 'id'
      });
    }
    
    // Class selectors
    if (element.className && typeof element.className === 'string') {
      const classes = element.className.split(' ').filter(c => c.length > 0);
      if (classes.length > 0) {
        candidates.push({
          selector: `.${classes.join('.')}`,
          specificity: 80,
          type: 'class'
        });
        
        // Single class selectors
        classes.forEach(cls => {
          candidates.push({
            selector: `.${cls}`,
            specificity: 60,
            type: 'single-class'
          });
        });
      }
    }
    
    // Attribute selectors
    const importantAttrs = ['data-testid', 'data-cy', 'name', 'data-field'];
    importantAttrs.forEach(attr => {
      if (element.hasAttribute(attr)) {
        candidates.push({
          selector: `[${attr}="${element.getAttribute(attr)}"]`,
          specificity: 70,
          type: 'attribute'
        });
      }
    });
    
    // Tag + class combinations
    if (element.className) {
      const classes = element.className.split(' ').filter(c => c.length > 0);
      if (classes.length > 0) {
        candidates.push({
          selector: `${element.tagName.toLowerCase()}.${classes[0]}`,
          specificity: 50,
          type: 'tag-class'
        });
      }
    }
    
    // Positional selectors
    const parent = element.parentElement;
    if (parent) {
      const siblings = Array.from(parent.children);
      const index = siblings.indexOf(element);
      
      candidates.push({
        selector: `${element.tagName.toLowerCase()}:nth-child(${index + 1})`,
        specificity: 30,
        type: 'position'
      });
      
      // Tag-specific position
      const tagSiblings = siblings.filter(el => el.tagName === element.tagName);
      if (tagSiblings.length > 1) {
        const tagIndex = tagSiblings.indexOf(element);
        candidates.push({
          selector: `${element.tagName.toLowerCase()}:nth-of-type(${tagIndex + 1})`,
          specificity: 25,
          type: 'tag-position'
        });
      }
    }
    
    // Tag selector (lowest priority)
    candidates.push({
      selector: element.tagName.toLowerCase(),
      specificity: 10,
      type: 'tag'
    });
    
    // Sort by specificity (highest first)
    this.selectorCandidates = candidates
      .sort((a, b) => b.specificity - a.specificity)
      .filter(candidate => {
        // Test if selector actually works and is unique enough
        try {
          const matchedElements = document.querySelectorAll(candidate.selector);
          return matchedElements.length > 0 && matchedElements.length < 10;
        } catch (e) {
          return false;
        }
      });
  }
  
  showSelectionInterface(element) {
    const selectionInfo = document.getElementById('schema-speed-selection-info');
    if (!selectionInfo) return;
    
    selectionInfo.style.display = 'block';
    
    this.updateSelectorDisplay();
    this.updatePreviewContent(element);
  }
  
  updateSelectorDisplay() {
    const selectorDisplay = document.getElementById('schema-speed-selector-display');
    if (!selectorDisplay) return;
    
    const current = this.selectorCandidates[this.currentSelectorIndex];
    
    if (current) {
      selectorDisplay.textContent = `${current.selector} (${current.type})`;
    }
  }
  
  updatePreviewContent(element) {
    const previewContent = document.getElementById('schema-speed-preview-content');
    if (!previewContent) return;
    
    const current = this.selectorCandidates[this.currentSelectorIndex];
    
    if (!current) return;
    
    // Get all elements that match current selector
    const matchedElements = document.querySelectorAll(current.selector);
    const content = this.extractContent(element);
    
    previewContent.innerHTML = `
      <div class="schema-speed-preview-stats">
        Matches: ${matchedElements.length} elements
      </div>
      <div class="schema-speed-preview-value">
        <strong>Content:</strong> "${content.text}"
      </div>
      ${content.url ? `<div class="schema-speed-preview-url"><strong>URL:</strong> ${content.url}</div>` : ''}
      ${content.alt ? `<div class="schema-speed-preview-alt"><strong>Alt:</strong> ${content.alt}</div>` : ''}
    `;
  }
  
  extractContent(element) {
    const result = {
      text: '',
      url: '',
      alt: ''
    };
    
    // Extract text content
    if (element.tagName === 'IMG') {
      result.text = element.src;
      result.url = element.src;
      result.alt = element.alt;
    } else if (element.tagName === 'A') {
      result.text = element.textContent.trim();
      result.url = element.href;
    } else if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
      result.text = element.value;
    } else {
      result.text = element.textContent.trim();
    }
    
    // Check for data attributes
    const dataAttrs = ['data-url', 'data-href', 'data-src'];
    dataAttrs.forEach(attr => {
      if (element.hasAttribute(attr) && !result.url) {
        result.url = element.getAttribute(attr);
      }
    });
    
    return result;
  }
  
  cyclePreviousSelector() {
    if (this.currentSelectorIndex > 0) {
      this.currentSelectorIndex--;
      this.updateSelectorDisplay();
      this.updatePreviewContent(this.highlightedElement);
    }
  }
  
  cycleNextSelector() {
    if (this.currentSelectorIndex < this.selectorCandidates.length - 1) {
      this.currentSelectorIndex++;
      this.updateSelectorDisplay();
      this.updatePreviewContent(this.highlightedElement);
    }
  }
  
  confirmCapture() {
    if (!this.currentField || !this.selectorCandidates[this.currentSelectorIndex]) {
      return;
    }
    
    const selector = this.selectorCandidates[this.currentSelectorIndex];
    const element = this.highlightedElement;
    if (!element) {
      return;
    }
    
    const content = this.extractContent(element);
    const transformationSelect = document.getElementById('schema-speed-transformation-select');
    const transformation = transformationSelect ? transformationSelect.value : '';
    
    // Apply transformation if selected
    let finalContent = content.text;
    if (transformation) {
      finalContent = this.transformations.applyTransformation(finalContent, transformation);
    }
    
    // Store captured data
    this.captureSession[this.currentField] = {
      value: finalContent,
      selector: selector.selector,
      element: element,
      transformation: transformation || null,
      timestamp: new Date().toISOString()
    };
    
    // Update session storage
    this.updateCaptureSession();
    
    // Show success feedback
    this.showCaptureSuccess();
    
    // Reset for next field
    this.resetSelection();
  }
  
  cancelCapture() {
    this.resetSelection();
  }
  
  resetSelection() {
    this.removeHighlight();
    this.currentField = null;
    
    const fieldDropdown = document.getElementById('schema-speed-field-dropdown');
    if (fieldDropdown) {
      fieldDropdown.value = '';
    }
    
    const selectionInfo = document.getElementById('schema-speed-selection-info');
    if (selectionInfo) {
      selectionInfo.style.display = 'none';
    }
    
    // Reset toolbar styling
    if (this.overlay) {
      const toolbar = this.overlay.querySelector('.schema-speed-capture-toolbar');
      if (toolbar) {
        toolbar.style.background = '';
        toolbar.style.color = '';
        toolbar.style.padding = '';
        
        const instructionText = toolbar.querySelector('.schema-speed-instruction-text');
        if (instructionText) {
          instructionText.remove();
        }
      }
    }
  }
  
  clearSelection() {
    this.resetSelection();
    this.captureSession = {};
    this.updateCaptureSession();
  }
  
  async updateCaptureSession() {
    try {
      await chrome.runtime.sendMessage({
        action: 'updateSession',
        sessionData: {
          capturedData: this.captureSession,
          domain: window.location.hostname,
          url: window.location.href
        }
      });
    } catch (error) {
      console.error('Error updating capture session:', error);
    }
  }
  
  showCaptureSuccess() {
    // Create temporary success indicator
    const success = document.createElement('div');
    success.textContent = `✓ Captured ${this.currentField}`;
    success.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: var(--schema-speed-chart-2);
      color: white;
      padding: 8px 12px;
      border-radius: 4px;
      font-size: 13px;
      z-index: 10001;
    `;
    
    document.body.appendChild(success);
    
    setTimeout(() => {
      if (success.parentNode) {
        success.parentNode.removeChild(success);
      }
    }, 2000);
  }
  
  async saveProfile() {
    if (Object.keys(this.captureSession).length === 0) {
      alert('No data captured yet. Capture some fields first.');
      return;
    }
    
    const profileName = prompt('Enter profile name:');
    if (!profileName) return;
    
    try {
      const profileData = {
        domain: window.location.hostname,
        profileType: profileName,
        profile: {
          templateType: 'custom',
          selectors: {},
          transformations: {},
          urlPatterns: [window.location.pathname]
        }
      };
      
      // Extract selectors and transformations from captured data
      Object.entries(this.captureSession).forEach(([fieldName, fieldData]) => {
        profileData.profile.selectors[fieldName] = fieldData.selector;
        if (fieldData.transformation) {
          profileData.profile.transformations[fieldName] = [fieldData.transformation];
        }
      });
      
      await chrome.runtime.sendMessage({
        action: 'saveProfile',
        profileData: profileData
      });
      
      alert('Profile saved successfully!');
    } catch (error) {
      console.error('Error saving profile:', error);
      alert('Error saving profile');
    }
  }
  
  closeCaptureMode() {
    this.isActive = false;
    this.removeHighlight();
    
    // Hide the overlay instead of removing it
    if (this.overlay) {
      this.overlay.style.display = 'none';
    }
    
    // Reset selection info
    const selectionInfo = document.getElementById('schema-speed-selection-info');
    if (selectionInfo) {
      selectionInfo.style.display = 'none';
    }
    
    // Reset current field
    this.currentField = null;
    this.selectorCandidates = [];
    this.currentSelectorIndex = 0;
  }
  
  handleAutoCapture() {
    // Auto-detection would be implemented here
    alert('Auto-detection feature coming soon!');
  }
  
  handleManualCapture() {
    // Already in manual mode - this is the default
    document.getElementById('schema-speed-manual-capture').classList.add('active');
    document.getElementById('schema-speed-auto-capture').classList.remove('active');
  }
  
  async loadCaptureSession() {
    try {
      const response = await chrome.runtime.sendMessage({
        action: 'getCurrentSession'
      });
      
      if (response.success && response.session) {
        this.captureSession = response.session.capturedData || {};
      }
    } catch (error) {
      console.error('Error loading capture session:', error);
    }
  }
}

// Content Transformation System
class ContentTransformations {
  applyTransformation(content, transformationType, options = {}) {
    switch (transformationType) {
      case 'removeNumbers':
        return content.replace(/\d+/g, '');
      
      case 'removeSpecific':
        const removeString = options.removeString || '';
        return content.replace(new RegExp(removeString, 'gi'), '');
      
      case 'trimStart':
        const startChars = parseInt(options.startChars) || 0;
        return content.substring(startChars);
      
      case 'trimEnd':
        const endChars = parseInt(options.endChars) || 0;
        return content.substring(0, content.length - endChars);
      
      case 'patternMatch':
        const pattern = options.pattern || '';
        const match = content.match(new RegExp(pattern, 'i'));
        return match ? match[0] : content;
      
      case 'sentenceCase':
        return content.charAt(0).toUpperCase() + content.slice(1).toLowerCase();
      
      case 'titleCase':
        return content.replace(/\w\S*/g, (txt) => 
          txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
        );
      
      case 'removeHtml':
        return content.replace(/<[^>]*>/g, '');
      
      case 'extractUrl':
        const urlMatch = content.match(/https?:\/\/[^\s]+/);
        return urlMatch ? urlMatch[0] : content;
      
      case 'formatDate':
        return this.formatDate(content, options.dateFormat || 'ISO8601');
      
      case 'custom':
        // Execute custom JavaScript transformation
        if (options.customFunction) {
          try {
            return new Function('content', options.customFunction)(content);
          } catch (error) {
            console.error('Custom transformation error:', error);
            return content;
          }
        }
        return content;
      
      default:
        return content;
    }
  }
  
  formatDate(dateString, format) {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return dateString;
    
    switch (format) {
      case 'ISO8601':
        return date.toISOString();
      case 'YYYY-MM-DD':
        return date.toISOString().split('T')[0];
      case 'MM/DD/YYYY':
        return date.toLocaleDateString('en-US');
      case 'DD/MM/YYYY':
        return date.toLocaleDateString('en-GB');
      default:
        return date.toISOString();
    }
  }
}

// Initialize content capture system
let contentCapture = null;

// Listen for initialization from background script
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    console.log('[Schema Speed DEBUG] Initializing content capture on DOMContentLoaded');
    contentCapture = new ContentCapture();
    contentCapture.initialize();
  });
} else {
  console.log('[Schema Speed DEBUG] Initializing content capture immediately');
  contentCapture = new ContentCapture();
  contentCapture.initialize();
}

// Add global message listener for debugging
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('[Schema Speed DEBUG] Global message received:', message);
  if (contentCapture && contentCapture.handleMessage) {
    contentCapture.handleMessage(message, sender, sendResponse);
  }
  return true; // Keep message channel open
});
