// Schema Speed Assistant - Sidebar Injector
// Injects the sidebar interface as a DOM element into the current webpage (NO IFRAME)

// At the top of the file, import SidebarController
// import { SidebarController } from '../sidebar/sidebar.js';

// --- BEGIN SidebarController and dependencies (copied from sidebar/sidebar.js) ---

// Schema Speed Assistant - Sidebar Controller
// Main interface controller for the sidebar when injected into content pages

class SidebarController {
  constructor() {
    this.currentTab = 'prepare';
    this.isDocked = false;
    this.dockPosition = 'right';
    this.profileManager = new ProfileManager();
    this.sessionManager = new SessionManager();
    this.initializeRetryCount = 0;
    this.maxInitializeRetries = 3;
  }

  async initialize() {
    try {
      await this.profileManager.initialize();
      await this.sessionManager.initialize();
      
      this.setupEventListeners();
      this.setupTabNavigation();
      this.setupDockingSystem();
      await this.loadDockState();
      await this.initializeTheme();
      
      await this.loadCurrentTabInfo();
      this.updateStatus('Ready');
    } catch (error) {
      console.error('Error during initialization:', error);
      
      if (error.message.includes('Extension context invalidated') && this.initializeRetryCount < this.maxInitializeRetries) {
        this.initializeRetryCount++;
        console.log(`Retrying initialization (attempt ${this.initializeRetryCount})`);
        await new Promise(resolve => setTimeout(resolve, 1000));
        return this.initialize();
      }
      
      this.showNotification('Failed to initialize. Please reload the extension.', 'error');
      this.updateStatus('Initialization failed');
    }
  }

  async loadCurrentTabInfo() {
    try {
      // Show the full URL of the current page
      this.currentDomain = window.location.href;
      
      const domainElement = document.getElementById('schema-speed-current-domain');
      if (domainElement) {
        domainElement.textContent = this.currentDomain;
      }
    } catch (error) {
      console.error('Error loading current tab info:', error);
      this.currentDomain = 'Unknown URL';
      
      const domainElement = document.getElementById('schema-speed-current-domain');
      if (domainElement) {
        domainElement.textContent = this.currentDomain;
      }
    }
  }

  setupEventListeners() {
    // Header controls
    const themeToggle = document.getElementById('theme-toggle-btn');
    if (themeToggle) {
      themeToggle.addEventListener('click', () => {
        this.handleThemeToggle();
      });
    }

    const closeSidebar = document.getElementById('schema-speed-close-sidebar');
    if (closeSidebar) {
      closeSidebar.addEventListener('click', () => {
        this.handleCloseSidebar();
      });
    }

    // Dock toggle
    const dockToggle = document.getElementById('schema-speed-dock-toggle');
    if (dockToggle) {
      dockToggle.addEventListener('click', () => {
        this.handleDockToggle();
      });
    }

    // Reload extension
    const reloadExtension = document.getElementById('reloadBtn');
    if (reloadExtension) {
      reloadExtension.addEventListener('click', () => {
        this.handleReloadExtension();
      });
    }

    // Prepare Template tab events
    const fillFields = document.getElementById('schema-speed-fill-fields');
    if (fillFields) {
      fillFields.addEventListener('click', () => {
        this.handleFillFieldsExtension();
      });
    }

    const clearFields = document.getElementById('schema-speed-clear-fields');
    if (clearFields) {
      clearFields.addEventListener('click', () => {
        this.handleClearFields();
      });
    }

    const extractTemplate = document.getElementById('schema-speed-extract-template');
    if (extractTemplate) {
      extractTemplate.addEventListener('click', () => {
        this.handleExtractTemplateTab();
      });
    }

    const previewTemplate = document.getElementById('schema-speed-preview-template');
    if (previewTemplate) {
      previewTemplate.addEventListener('click', () => {
        this.handlePreviewTemplate();
      });
    }

    // Scrape Page tab events
    const startManualCapture = document.getElementById('schema-speed-start-manual-capture');
    if (startManualCapture) {
      startManualCapture.addEventListener('click', () => {
        this.handleStartManualCapture();
      });
    }

    const autoDetectFields = document.getElementById('schema-speed-auto-detect-fields');
    if (autoDetectFields) {
      autoDetectFields.addEventListener('click', () => {
        this.handleAutoDetectFields();
      });
    }

    const saveCaptureSession = document.getElementById('schema-speed-save-capture-session');
    if (saveCaptureSession) {
      saveCaptureSession.addEventListener('click', () => {
        this.handleSaveCaptureSession();
      });
    }

    const createNewProfile = document.getElementById('schema-speed-create-new-profile');
    if (createNewProfile) {
      createNewProfile.addEventListener('click', () => {
        this.handleCreateNewProfile();
      });
    }

    // Fill out Template tab events
    const fillBuilderFields = document.getElementById('schema-speed-fill-builder-fields');
    if (fillBuilderFields) {
      fillBuilderFields.addEventListener('click', () => {
        this.handleFillBuilderFields();
      });
    }

    const generateSnippet = document.getElementById('schema-speed-generate-snippet');
    if (generateSnippet) {
      generateSnippet.addEventListener('click', () => {
        this.handleGenerateSnippet();
      });
    }

    const copyOutput = document.getElementById('schema-speed-copy-output');
    if (copyOutput) {
      copyOutput.addEventListener('click', () => {
        this.handleCopyOutput();
      });
    }

    // Output mode toggle
    document.querySelectorAll('input[name="output-mode"]').forEach(radio => {
      radio.addEventListener('change', (e) => {
        this.handleOutputModeChange(e.target.value);
      });
    });

    // Domain filter
    const domainFilter = document.getElementById('schema-speed-domain-filter');
    if (domainFilter) {
      domainFilter.addEventListener('change', (e) => {
        this.handleDomainFilterChange(e.target.value);
      });
    }

    // Profile select
    const profileSelect = document.getElementById('schema-speed-profile-select');
    if (profileSelect) {
      profileSelect.addEventListener('change', (e) => {
        this.handleProfileSelectChange(e.target.value);
      });
    }

    // Footer events
    const settingsBtn = document.getElementById('schema-speed-settings-btn');
    if (settingsBtn) {
      settingsBtn.addEventListener('click', () => {
        this.handleSettings();
      });
    }

    const helpBtn = document.getElementById('schema-speed-help-btn');
    if (helpBtn) {
      helpBtn.addEventListener('click', () => {
        this.handleHelp();
      });
    }

    // Save template button
    const saveTemplate = document.getElementById('schema-speed-save-template');
    if (saveTemplate) {
      saveTemplate.addEventListener('click', () => {
        console.debug('[DEBUG] Save Template button clicked');
        this.showNotification('[DEBUG] Save Template button clicked', 'info');
        this.handleSaveTemplate();
      });
    }

    // Saved templates dropdown
    const templatesDropdown = document.getElementById('schema-speed-templates-dropdown');
    if (templatesDropdown) {
      templatesDropdown.addEventListener('change', (e) => {
        const selected = e.target.value;
        if (selected) {
          this.useTemplate(selected);
        }
      });
    }
  }

  setupTabNavigation() {
    document.querySelectorAll('.schema-speed-nav-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const tabName = e.target.dataset.tab;
        this.switchTab(tabName);
      });
    });
  }

  switchTab(tabName) {
    // Update navigation
    document.querySelectorAll('.schema-speed-nav-btn').forEach(btn => {
      btn.classList.remove('active');
    });
    
    const activeNavBtn = document.querySelector(`[data-tab="${tabName}"]`);
    if (activeNavBtn) {
      activeNavBtn.classList.add('active');
    }

    // Update content
    document.querySelectorAll('.schema-speed-tab-content').forEach(content => {
      content.classList.remove('active');
    });
    
    const activeTabContent = document.getElementById(`schema-speed-${tabName}-tab`);
    if (activeTabContent) {
      activeTabContent.classList.add('active');
    }

    this.currentTab = tabName;

    // Load tab-specific data
    switch (tabName) {
      case 'prepare':
        // Prepare tab is always ready
        break;
      case 'scrape':
        this.refreshCaptureTab();
        break;
      case 'fill':
        this.refreshOutputTab();
        break;
    }
  }

  async refreshDashboard() {
    try {
      // Update stats
      const profiles = await this.profileManager.getDomainProfiles(this.currentDomain);
      const templates = await this.storage.getStoredData();
      const session = await this.sessionManager.getCurrentSession();

      const profilesCountEl = document.getElementById('schema-speed-profiles-count');
      if (profilesCountEl) {
        profilesCountEl.textContent = Object.keys(profiles).length;
      }

      const templatesCountEl = document.getElementById('schema-speed-templates-count');
      if (templatesCountEl) {
        templatesCountEl.textContent = Object.keys(templates?.templates || {}).length;
      }

      const capturedFieldsEl = document.getElementById('schema-speed-captured-fields');
      if (capturedFieldsEl) {
        capturedFieldsEl.textContent = session ? Object.keys(session.capturedData || {}).length : 0;
      }

      // Update recent activity
      this.updateRecentActivity();
    } catch (error) {
      console.error('Error refreshing dashboard:', error);
    }
  }

  updateRecentActivity() {
    const activityList = document.getElementById('schema-speed-activity-list');
    if (activityList) {
      activityList.innerHTML = `
        <div class="schema-speed-empty-state">
          <p>No recent activity</p>
        </div>
      `;
    }
  }

  async refreshCaptureTab() {
    try {
      // Load profiles for current domain
      const profiles = await this.profileManager.getDomainProfiles(this.currentDomain);
      const profileSelect = document.getElementById('schema-speed-profile-select');
      
      if (profileSelect) {
        profileSelect.innerHTML = '<option value="">Choose existing profile...</option>';
        Object.keys(profiles).forEach(profileType => {
          const option = document.createElement('option');
          option.value = profileType;
          option.textContent = profileType;
          profileSelect.appendChild(option);
        });
      }

      // Load current session if exists
      const session = await this.sessionManager.getCurrentSession();
      if (session) {
        this.displayCaptureProgress(session);
      }
    } catch (error) {
      console.error('Error refreshing capture tab:', error);
    }
  }

  async refreshProfilesTab() {
    try {
      const allProfiles = await this.profileManager.getAllProfiles();
      const domainFilter = document.getElementById('schema-speed-domain-filter');
      
      if (domainFilter) {
        // Update domain filter
        domainFilter.innerHTML = '<option value="">All Domains</option>';
        Object.keys(allProfiles).forEach(domain => {
          const option = document.createElement('option');
          option.value = domain;
          option.textContent = domain;
          if (domain === this.currentDomain) {
            option.selected = true;
          }
          domainFilter.appendChild(option);
        });
      }

      // Display profiles
      this.displayProfiles(allProfiles, this.currentDomain);
    } catch (error) {
      console.error('Error refreshing profiles tab:', error);
    }
  }

  displayProfiles(allProfiles, filterDomain = null) {
    const profilesList = document.getElementById('schema-speed-profiles-list');
    if (!profilesList) return;

    const profilesToShow = filterDomain ? 
      { [filterDomain]: allProfiles[filterDomain] || {} } : 
      allProfiles;

    if (Object.keys(profilesToShow).length === 0 || 
        (filterDomain && !allProfiles[filterDomain])) {
      profilesList.innerHTML = `
        <div class="schema-speed-empty-state">
          <p>No profiles found${filterDomain ? ` for ${filterDomain}` : ''}</p>
          <p>Create your first profile to get started!</p>
        </div>
      `;
      return;
    }

    let profilesHTML = '';
    Object.entries(profilesToShow).forEach(([domain, profiles]) => {
      Object.entries(profiles).forEach(([profileType, profile]) => {
        profilesHTML += this.createProfileCard(domain, profileType, profile);
      });
    });

    profilesList.innerHTML = profilesHTML;

    // Attach profile action listeners
    this.attachProfileActionListeners();
  }

  createProfileCard(domain, profileType, profile) {
    const selectorCount = Object.keys(profile.selectors || {}).length;
    const lastUsed = profile.lastUsed ? new Date(profile.lastUsed).toLocaleDateString() : 'Never';

    return `
      <div class="schema-speed-profile-card" data-domain="${domain}" data-profile="${profileType}">
        <div class="schema-speed-profile-card-header">
          <h5>${profileType} (${domain})</h5>
          <div class="schema-speed-profile-actions">
            <button class="schema-speed-link-btn edit-profile" data-domain="${domain}" data-profile="${profileType}">Edit</button>
            <button class="schema-speed-link-btn duplicate-profile" data-domain="${domain}" data-profile="${profileType}">Duplicate</button>
            <button class="schema-speed-link-btn delete-profile" data-domain="${domain}" data-profile="${profileType}">Delete</button>
          </div>
        </div>
        
        <div class="schema-speed-profile-card-body">
          <div class="schema-speed-profile-info">
            <span class="schema-speed-template-type">Template: ${profile.templateType || 'Custom'}</span>
            <span class="schema-speed-last-used">Last used: ${lastUsed}</span>
          </div>
          
          <div class="schema-speed-profile-fields">
            <strong>Mapped Fields (${selectorCount}):</strong>
            ${selectorCount > 0 ? `
              <div class="schema-speed-field-list">
                ${Object.entries(profile.selectors || {})
                  .slice(0, 3)
                  .map(([field, selector]) => `
                    <div class="schema-speed-field-item">
                      <span class="schema-speed-field-name">${field}:</span>
                      <span class="schema-speed-field-selector">${selector}</span>
                    </div>
                  `).join('')}
                ${selectorCount > 3 ? `<div class="schema-speed-field-more">+${selectorCount - 3} more</div>` : ''}
              </div>
            ` : '<p class="schema-speed-empty-state">No fields mapped</p>'}
          </div>
          
          ${profile.urlPatterns && profile.urlPatterns.length > 0 ? `
            <div class="schema-speed-url-patterns">
              <strong>URL Patterns:</strong>
              <div class="schema-speed-pattern-list">
                ${profile.urlPatterns.map(pattern => `<span class="schema-speed-pattern">${pattern}</span>`).join('')}
              </div>
            </div>
          ` : ''}
        </div>
      </div>
    `;
  }

  attachProfileActionListeners() {
    document.querySelectorAll('.edit-profile').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const domain = e.target.dataset.domain;
        const profile = e.target.dataset.profile;
        this.handleEditProfile(domain, profile);
      });
    });

    document.querySelectorAll('.duplicate-profile').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const domain = e.target.dataset.domain;
        const profile = e.target.dataset.profile;
        this.handleDuplicateProfile(domain, profile);
      });
    });

    document.querySelectorAll('.delete-profile').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const domain = e.target.dataset.domain;
        const profile = e.target.dataset.profile;
        this.handleDeleteProfile(domain, profile);
      });
    });
  }

  async refreshOutputTab() {
    try {
      const session = await this.sessionManager.getCurrentSession();
      const capturedDataPreview = document.getElementById('schema-speed-captured-data-preview');

      if (capturedDataPreview) {
        if (session && session.capturedData && Object.keys(session.capturedData).length > 0) {
          let previewHTML = '';
          Object.entries(session.capturedData).forEach(([field, data]) => {
            previewHTML += `
              <div class="schema-speed-captured-field">
                <strong>${field}:</strong> ${data.value || data}
              </div>
            `;
          });
          capturedDataPreview.innerHTML = previewHTML;
        } else {
          capturedDataPreview.innerHTML = '<p class="schema-speed-empty-state">No data captured yet</p>';
        }
      }
    } catch (error) {
      console.error('Error refreshing output tab:', error);
    }
  }

  displayCaptureProgress(session) {
    const progressDiv = document.getElementById('schema-speed-capture-progress');
    const completedFields = document.getElementById('schema-speed-completed-fields');
    const totalFields = document.getElementById('schema-speed-total-fields');
    const fieldsList = document.getElementById('schema-speed-fields-list');

    const capturedCount = Object.keys(session.capturedData || {}).length;
    const totalCount = session.totalFields || capturedCount;

    if (completedFields) completedFields.textContent = capturedCount;
    if (totalFields) totalFields.textContent = totalCount;

    if (fieldsList) {
      let fieldsHTML = '';
      Object.entries(session.capturedData || {}).forEach(([field, data]) => {
        fieldsHTML += `
          <div class="schema-speed-field-item captured">
            <span class="schema-speed-field-name">${field}</span>
            <span class="schema-speed-field-value">${data.value || data}</span>
            <span class="schema-speed-field-status">✓</span>
          </div>
        `;
      });
      fieldsList.innerHTML = fieldsHTML;
    }

    if (progressDiv) {
      progressDiv.style.display = 'block';
    }

    // Enable save session button if we have captured data
    const saveBtn = document.getElementById('schema-speed-save-capture-session');
    if (saveBtn) {
      saveBtn.disabled = capturedCount === 0;
    }
  }

  // Event Handlers
  async handleThemeToggle() {
    const sidebarContainer = document.getElementById('schema-speed-sidebar-container');
    const themeIcon = document.querySelector('#schema-speed-sidebar-container .schema-speed-theme-icon');
    
    let newTheme = 'light';
    if (sidebarContainer && sidebarContainer.classList.contains('dark')) {
      sidebarContainer.classList.remove('dark');
      if (themeIcon) themeIcon.textContent = '🌙';
    } else if (sidebarContainer) {
      sidebarContainer.classList.add('dark');
      if (themeIcon) themeIcon.textContent = '☀️';
      newTheme = 'dark';
    }
    
    // Save theme preference to local storage UI state
    try {
      const data = await chrome.storage.local.get(['schemaSpeedSession']);
      const storage = data.schemaSpeedSession || {};
      
      storage.uiState = {
        ...storage.uiState,
        theme: newTheme
      };
      
      await chrome.storage.local.set({ schemaSpeedSession: storage });
    } catch (error) {
      console.error('Error saving theme preference:', error);
    }
    
    this.showNotification('Theme toggled', 'info');
  }

  handleCloseSidebar() {
    // Use the same mechanism as the extension icon
    if (window.schemaSpeedSidebar) {
      window.schemaSpeedSidebar.hideSidebar();
    }
  }

  async handleExtractTemplate() {
    try {
      this.updateStatus('Extracting template...');
      
      const response = await chrome.runtime.sendMessage({
        action: 'extractTemplate'
      });

      if (response && response.success) {
        this.showNotification('Template extracted successfully!', 'success');
        await this.refreshDashboard();
      } else {
        this.showNotification('Failed to extract template: ' + (response?.error || 'Unknown error'), 'error');
      }
    } catch (error) {
      console.error('Error extracting template:', error);
      this.showNotification('Error extracting template', 'error');
    } finally {
      this.updateStatus('Ready');
    }
  }

  async handleFillFields() {
    try {
      const session = await this.sessionManager.getCurrentSession();
      if (!session || !session.capturedData) {
        this.showNotification('No captured data available', 'warning');
        return;
      }

      this.updateStatus('Filling fields...');

      const response = await chrome.runtime.sendMessage({
        action: 'populateFields',
        data: session.capturedData
      });

      if (response && response.success) {
        const results = response.results;
        this.showNotification(
          `Filled ${results.filled} fields, skipped ${results.skipped}`, 
          'success'
        );
      } else {
        this.showNotification('Failed to fill fields: ' + (response?.error || 'Unknown error'), 'error');
      }
    } catch (error) {
      console.error('Error filling fields:', error);
      this.showNotification('Error filling fields', 'error');
    } finally {
      this.updateStatus('Ready');
    }
  }

  async handleStartManualCapture() {
    try {
      // Start a new capture session
      await this.sessionManager.startSession(this.currentDomain, 'manual');
      
      const response = await chrome.runtime.sendMessage({
        action: 'startCapture',
        fieldName: 'manual'
      });

      if (response && response.success) {
        this.showNotification('Manual capture started', 'success');
      } else {
        this.showNotification('Failed to start capture: ' + (response?.error || 'Unknown error'), 'error');
      }
    } catch (error) {
      console.error('Error starting manual capture:', error);
      this.showNotification('Error starting capture', 'error');
    }
  }

  handleAutoDetectFields() {
    this.showNotification('Auto-detect feature coming soon!', 'info');
  }

  async handleSaveCaptureSession() {
    try {
      const session = await this.sessionManager.getCurrentSession();
      if (!session) {
        this.showNotification('No active session to save', 'warning');
        return;
      }

      this.showNotification('Session saved successfully!', 'success');
      await this.refreshDashboard();
    } catch (error) {
      console.error('Error saving capture session:', error);
      this.showNotification('Error saving session', 'error');
    }
  }

  handleCreateNewProfile() {
    this.showNotification('Profile creation interface coming soon!', 'info');
  }

  handleAddProfile() {
    this.showNotification('Profile creation interface coming soon!', 'info');
  }

  handleManageTemplates() {
    this.showNotification('Template management interface coming soon!', 'info');
  }

  handleImportProfiles() {
    this.showNotification('Profile import feature coming soon!', 'info');
  }

  handleExportProfiles() {
    this.showNotification('Profile export feature coming soon!', 'info');
  }

  handleEditProfile(domain, profileType) {
    this.showNotification(`Edit profile: ${profileType} (${domain}) - Coming soon!`, 'info');
  }

  handleDuplicateProfile(domain, profileType) {
    this.showNotification(`Duplicate profile: ${profileType} (${domain}) - Coming soon!`, 'info');
  }

  async handleDeleteProfile(domain, profileType) {
    if (confirm(`Are you sure you want to delete the profile "${profileType}" for ${domain}?`)) {
      try {
        await this.profileManager.deleteProfile(domain, profileType);
        this.showNotification('Profile deleted successfully!', 'success');
        await this.refreshProfilesTab();
      } catch (error) {
        console.error('Error deleting profile:', error);
        this.showNotification('Error deleting profile', 'error');
      }
    }
  }

  async handleFillFieldsExtension() {
    try {
      console.log('[DEBUG] Starting handleFillFieldsExtension');
      console.log('[DEBUG] Current window location:', window.location.href);
      console.log('[DEBUG] Sending fillFields message to background');
      
      const response = await chrome.runtime.sendMessage({
        action: 'fillFields'
      });

      console.log('[DEBUG] Raw response from background:', response);

      if (response && response.success) {
        const results = response.results || response;
        console.log('[DEBUG] Form filling results:', results);
        this.showNotification(`Form fields prepared for template extraction`, 'success');
      } else {
        console.error('[DEBUG] Form filling failed:', response);
        this.showNotification('Failed to fill fields: ' + (response?.error || 'Unknown error'), 'error');
      }
    } catch (error) {
      console.error('[DEBUG] Error in handleFillFieldsExtension:', error);
      this.showNotification('Error filling fields', 'error');
    }
  }

  async handleClearFields() {
    try {
      const response = await chrome.runtime.sendMessage({
        action: 'clearFields'
      });

      if (response && response.success) {
        const results = response.results || response;
        this.showNotification(`Cleared ${results.cleared} fields`, 'success');
        this.updateStatus(`Cleared ${results.cleared} fields`);
      } else {
        this.showNotification('Failed to clear fields: ' + (response?.error || 'Unknown error'), 'error');
      }
    } catch (error) {
      console.error('Error clearing fields:', error);
      this.showNotification('Error clearing fields', 'error');
    }
  }

  async handleExtractTemplateTab() {
    try {
      const templateName = document.getElementById('schema-speed-template-name')?.value || 'Custom Template';
      const templateType = document.getElementById('schema-speed-template-type')?.value || 'custom';

      const response = await chrome.runtime.sendMessage({
        action: 'extractTemplate',
        templateName: templateName,
        templateType: templateType
      });

      if (response && response.success) {
        this.showNotification('Template extracted successfully!', 'success');
        this.updateStatus('Template extracted');
        
        // Show extracted fields
        this.displayExtractedFields(response.template);
      } else {
        this.showNotification('Failed to extract template: ' + (response?.error || 'Unknown error'), 'error');
      }
    } catch (error) {
      console.error('Error extracting template:', error);
      this.showNotification('Error extracting template', 'error');
    }
  }

  async handlePreviewTemplate() {
    try {
      const response = await chrome.runtime.sendMessage({
        action: 'extractTemplate',
        preview: true
      });

      if (response && response.success) {
        this.displayExtractedFields(response.template, true);
        this.showNotification('Template preview generated', 'info');
      } else {
        this.showNotification('Failed to preview template: ' + (response?.error || 'Unknown error'), 'error');
      }
    } catch (error) {
      console.error('Error previewing template:', error);
      this.showNotification('Error previewing template', 'error');
    }
  }

  displayExtractedFields(template, isPreview = false) {
    const extractedFields = document.getElementById('schema-speed-extracted-fields');
    const fieldsPreview = document.getElementById('schema-speed-fields-preview');
    
    if (!extractedFields || !fieldsPreview) return;

    if (template && template.fields && Object.keys(template.fields).length > 0) {
      fieldsPreview.innerHTML = '';
      
      Object.entries(template.fields).forEach(([fieldName, fieldInfo]) => {
        const fieldElement = document.createElement('div');
        fieldElement.className = 'schema-speed-field-item';
        fieldElement.innerHTML = `
          <div class="schema-speed-field-header">
            <span class="schema-speed-field-name">${fieldName}</span>
            <span class="schema-speed-field-type">${fieldInfo.type}</span>
          </div>
          <div class="schema-speed-field-details">
            <span class="schema-speed-field-label">Label: ${fieldInfo.label}</span>
            <span class="schema-speed-field-selector">Selector: ${fieldInfo.selector}</span>
            ${fieldInfo.required ? '<span class="schema-speed-field-required">Required</span>' : ''}
          </div>
        `;
        fieldsPreview.appendChild(fieldElement);
      });
      
      extractedFields.style.display = 'block';
    } else {
      fieldsPreview.innerHTML = '<p class="schema-speed-empty-state">No fields found. Make sure to fill the form fields first.</p>';
      extractedFields.style.display = 'block';
    }
  }

  handleOutputModeChange(mode) {
    const fillBuilderOptions = document.getElementById('schema-speed-fill-builder-options');
    const jsonSnippetOptions = document.getElementById('schema-speed-json-snippet-options');

    if (fillBuilderOptions && jsonSnippetOptions) {
      if (mode === 'fill-builder') {
        fillBuilderOptions.style.display = 'block';
        jsonSnippetOptions.style.display = 'none';
      } else {
        fillBuilderOptions.style.display = 'none';
        jsonSnippetOptions.style.display = 'block';
      }
    }
  }

  handleSettings() {
    this.showNotification('Settings interface coming soon!', 'info');
  }

  handleHelp() {
    this.showNotification('Help documentation coming soon!', 'info');
  }

  handleDomainFilterChange(domain) {
    this.refreshProfilesTab();
  }

  handleProfileSelectChange(profileType) {
    // Handle profile selection - could be used to pre-load profile data
    if (profileType) {
      this.showNotification(`Selected profile: ${profileType}`, 'info');
    }
  }

  handleCaptureModeChange(mode) {
    // Handle capture mode change (manual vs auto-detect)
    this.showNotification(`Capture mode changed to: ${mode}`, 'info');
  }

  handleFillBuilderFields() {
    this.showNotification('Fill builder fields feature coming soon!', 'info');
  }

  handleGenerateSnippet() {
    this.showNotification('Generate snippet feature coming soon!', 'info');
  }

  handleCopyOutput() {
    this.showNotification('Copy output feature coming soon!', 'info');
  }

  // Utility Methods
  updateStatus(message) {
    const statusText = document.getElementById('schema-speed-status-text');
    const statusDot = document.querySelector('.schema-speed-status-dot');
    
    if (statusText) {
      statusText.textContent = message;
    }
    
    if (statusDot) {
      if (message === 'Ready') {
        statusDot.style.background = 'var(--schema-speed-success)';
      } else {
        statusDot.style.background = 'var(--schema-speed-warning)';
      }
    }
  }

  showNotification(message, type = 'info') {
    // Remove any existing notifications
    document.querySelectorAll('.schema-speed-notification').forEach(n => n.remove());

    // Create notification element
    const notification = document.createElement('div');
    notification.className = 'schema-speed-notification';

    // Set dot and border color based on type
    let dotColor = '#7C3AED'; // Purple for info
    let borderColor = 'rgba(124, 58, 237, 0.3)';
    if (type === 'success') {
      dotColor = '#22c55e'; // Green
      borderColor = 'rgba(34, 197, 94, 0.5)';
    } else if (type === 'error' || type === 'close' || type === 'danger') {
      dotColor = '#ef4444'; // Red
      borderColor = 'rgba(239, 68, 68, 0.5)';
    }

    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: rgba(0, 0, 0, 0.9);
      color: #fff;
      padding: 8px 12px;
      border-radius: 6px;
      z-index: 10000000;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 13px;
      font-weight: 500;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      backdrop-filter: blur(5px);
      border: 1px solid ${borderColor};
      opacity: 0;
      transform: translateX(20px);
      transition: all 0.3s ease-out;
      max-width: 300px;
      pointer-events: none;
      white-space: nowrap;
      display: flex;
      align-items: center;
      gap: 8px;
    `;

    notification.innerHTML = `<span style="color: ${dotColor}; font-size: 16px;">●</span> <span>${message}</span>`;

    document.body.appendChild(notification);

    // Animate in
    requestAnimationFrame(() => {
      notification.style.opacity = '1';
      notification.style.transform = 'translateX(0)';
    });

    // Remove after 3 seconds
    setTimeout(() => {
      notification.style.opacity = '0';
      notification.style.transform = 'translateX(20px)';
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    }, 3000);
  }

  setupDockingSystem() {
    // Load saved dock state
    this.loadDockState();
    
    // Apply initial dock state
    this.applyDockState();
  }

  async loadDockState() {
    try {
      const data = await chrome.storage.local.get(['schemaSpeedSession']);
      const uiState = data.schemaSpeedSession?.uiState || {};
      
      this.isDocked = uiState.sidebarDocked || false;
      this.dockPosition = uiState.sidebarPosition || 'right';
    } catch (error) {
      console.error('Error loading dock state:', error);
    }
  }

  async saveDockState() {
    try {
      const data = await chrome.storage.local.get(['schemaSpeedSession']);
      const storage = data.schemaSpeedSession || {};
      
      storage.uiState = {
        ...storage.uiState,
        sidebarDocked: this.isDocked,
        sidebarPosition: this.dockPosition
      };
      
      await chrome.storage.local.set({ schemaSpeedSession: storage });
    } catch (error) {
      console.error('Error saving dock state:', error);
    }
  }

  applyDockState() {
    const sidebar = document.getElementById('schema-speed-sidebar-container');
    const dockToggle = document.getElementById('schema-speed-dock-toggle');
    
    if (!sidebar || !dockToggle) return;

    if (this.isDocked) {
      sidebar.classList.add('docked', `docked-${this.dockPosition}`);
      dockToggle.innerHTML = '<i class="fas fa-arrows-alt-h"></i>';
      dockToggle.title = 'Undock Sidebar';
    } else {
      sidebar.classList.remove('docked', 'docked-left', 'docked-right');
      dockToggle.innerHTML = '<i class="fas fa-right-left"></i>';
      dockToggle.title = 'Dock Sidebar';
    }
  }

  handleDockToggle() {
    this.isDocked = !this.isDocked;
    // Toggle dock position if already docked
    this.dockPosition = this.dockPosition === 'right' ? 'left' : 'right';
    // Send a message to the parent window to dock the sidebar
    if (window.parent) {
      window.parent.postMessage({ type: 'dockSidebar', dockPosition: this.dockPosition }, '*');
    }
    this.applyDockState();
    this.saveDockState();
    this.showNotification(
      this.isDocked 
        ? `Sidebar docked to ${this.dockPosition}` 
        : 'Sidebar undocked', 
      'success'
    );
  }

  async handleReloadExtension() {
    try {
      console.log('Attempting to reload extension...');
      
      // Try direct reload first
      if (chrome.runtime.reload) {
        console.log('Using chrome.runtime.reload()');
        chrome.runtime.reload();
        return;
      }

      // Fallback: Send message to background script
      console.log('Sending reload message to background script');
      await chrome.runtime.sendMessage({ action: 'reloadExtension' });
      
      // If we get here, reload was successful
      console.log('Reload message sent successfully');
      this.showNotification('Extension reloading...', 'info');
      
      // Give time for the notification to show
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Force page reload as final fallback
      window.location.reload();
      
    } catch (error) {
      console.log('Reload error:', error);
      
      // If context invalidated, reload the page
      if (error.message.includes('Extension context invalidated')) {
        console.log('Context invalidated, reloading page...');
        this.showNotification('Extension context lost. Reloading page...', 'warning');
        await new Promise(resolve => setTimeout(resolve, 500));
        window.location.reload();
      } else {
        // For other errors, show error notification
        console.error('Error during extension reload:', error);
        this.showNotification('Please reload the extension manually', 'warning');
      }
    }
  }

  async initializeTheme() {
    try {
      const data = await chrome.storage.local.get(['schemaSpeedSession']);
      const savedTheme = data.schemaSpeedSession?.uiState?.theme || 'light';
      
      const sidebarContainer = document.getElementById('schema-speed-sidebar-container');
      const themeIcon = document.querySelector('#schema-speed-sidebar-container .schema-speed-theme-icon');
      
      if (savedTheme === 'dark' && sidebarContainer) {
        sidebarContainer.classList.add('dark');
        if (themeIcon) themeIcon.textContent = '☀️';
      } else if (sidebarContainer) {
        sidebarContainer.classList.remove('dark');
        if (themeIcon) themeIcon.textContent = '🌙';
      }
    } catch (error) {
      console.error('Error initializing theme:', error);
    }
  }

  async handleSaveTemplate() {
    try {
      console.debug('[DEBUG] handleSaveTemplate function called');
      const templateName = document.getElementById('schema-speed-template-name')?.value;
      const templateType = document.getElementById('schema-speed-template-type')?.value;
      const templateContent = document.getElementById('schema-speed-template-content')?.value;
      console.debug('[DEBUG] Template values:', { templateName, templateType, templateContent: templateContent?.substring(0, 100) + '...' });
      this.showNotification(`[DEBUG] handleSaveTemplate called with: ${templateName}, ${templateType}`);
      if (!templateName || !templateType || !templateContent) {
        this.showNotification('Please fill in all template fields', 'error');
        console.warn('[DEBUG] Missing template fields:', { templateName: !!templateName, templateType: !!templateType, templateContent: !!templateContent });
        return;
      }
      this.updateStatus('Saving template...');
      // Send to background for canonical save and SS- extraction
      console.debug('[DEBUG] Sending message to background script');
      const response = await chrome.runtime.sendMessage({
        action: 'saveTemplate',
        template: {
          name: templateName,
          type: templateType,
          content: templateContent
        }
      });
      console.debug('[DEBUG] Background script response:', response);
      if (response && response.success) {
        this.showNotification('Template saved successfully!', 'success');
        // Switch to scraping tab
        this.switchTab('scrape');
        // Update templates list
        await this.loadSavedTemplates();
      } else {
        this.showNotification('Failed to save template: ' + (response?.error || 'Unknown error'), 'error');
      }
    } catch (error) {
      console.error('[DEBUG] Error in handleSaveTemplate:', error);
      this.showNotification('Error saving template. Please try again.', 'error');
    } finally {
      this.updateStatus('Ready');
    }
  }

  async loadSavedTemplates() {
    try {
      // Get templates from sync storage
      const response = await chrome.runtime.sendMessage({ action: 'getTemplates' });
      
      if (response.success) {
        const templates = response.templates;
        const lastUsed = response.lastUsed;
        
        console.debug('[DEBUG] loadSavedTemplates found:', Object.keys(templates));
        
        // Update UI with templates
        const templatesList = document.getElementById('schema-speed-templates-list');
        const templatesDropdown = document.getElementById('schema-speed-templates-dropdown');
        
        if (templatesList) {
          templatesList.innerHTML = '';
          Object.entries(templates).forEach(([name, template]) => {
            const templateItem = document.createElement('div');
            templateItem.className = 'schema-speed-template-item';
            if (lastUsed && name === lastUsed) {
              templateItem.classList.add('last-used');
            }
            templateItem.innerHTML = `
              <div class="template-header">
                <span class="template-name">${name}</span>
                <span class="template-type">${template.type}</span>
              </div>
              <div class="template-actions">
                <button class="use-template" data-template="${name}">Use</button>
                <button class="delete-template" data-template="${name}">Delete</button>
              </div>
            `;
            templatesList.appendChild(templateItem);
          });
          
          // Add event listeners
          templatesList.querySelectorAll('.use-template').forEach(btn => {
            btn.addEventListener('click', (e) => {
              const templateName = e.target.dataset.template;
              this.useTemplate(templateName);
            });
          });
          
          templatesList.querySelectorAll('.delete-template').forEach(btn => {
            btn.addEventListener('click', (e) => {
              const templateName = e.target.dataset.template;
              this.deleteTemplate(templateName);
            });
          });
        }
        
        // Update dropdown
        if (templatesDropdown) {
          templatesDropdown.innerHTML = '<option value="">Select a saved template...</option>';
          Object.entries(templates).forEach(([name, template]) => {
            const option = document.createElement('option');
            option.value = name;
            option.textContent = name;
            if (lastUsed && name === lastUsed) {
              option.selected = true;
            }
            templatesDropdown.appendChild(option);
          });
        }
      } else {
        console.error('[DEBUG] Error loading templates:', response.error);
      }
    } catch (error) {
      console.error('[DEBUG] Error loading templates:', error);
    }
  }

  async useTemplate(templateName) {
    try {
      const response = await chrome.runtime.sendMessage({
        action: 'getTemplate',
        name: templateName
      });
      
      if (response.success) {
        const template = response.template;
        
        // Load template into form fields
        document.getElementById('schema-speed-template-name').value = templateName;
        document.getElementById('schema-speed-template-type').value = template.type;
        document.getElementById('schema-speed-template-content').value = JSON.stringify(template.content, null, 2);
        
        this.showNotification(`Loaded template: ${templateName}`, 'success');
        await chrome.storage.sync.set({ schemaSpeedLastUsedTemplate: templateName });
        console.debug('[DEBUG] Last used template set:', templateName);
        await this.loadSavedTemplates();
      }
    } catch (error) {
      console.error('[DEBUG] Error using template:', error);
      this.showNotification('Error loading template', 'error');
    }
  }

  async deleteTemplate(templateName) {
    try {
      const response = await chrome.runtime.sendMessage({
        action: 'deleteTemplate',
        name: templateName
      });
      
      if (response.success) {
        this.showNotification(`Template deleted: ${templateName}`, 'success');
        await this.loadSavedTemplates();
      } else {
        console.error('[DEBUG] Error deleting template:', response.error);
        this.showNotification('Error deleting template', 'error');
      }
    } catch (error) {
      console.error('[DEBUG] Error deleting template:', error);
      this.showNotification('Error deleting template', 'error');
    }
  }
}

// Storage Classes (same as popup but for sidebar context)
class SchemaStorage {
  constructor() {
    this.syncKey = 'schemaSpeedData';
    this.localKey = 'schemaSpeedSession';
    this.retryAttempts = 3;
    this.retryDelay = 1000; // 1 second
  }

  async initialize() {
    return true;
  }

  async getStoredData() {
    for (let attempt = 1; attempt <= this.retryAttempts; attempt++) {
      try {
        const [syncData, localData] = await Promise.all([
          chrome.storage.sync.get([this.syncKey]),
          chrome.storage.local.get([this.localKey])
        ]);
        
        return {
          ...syncData[this.syncKey],
          sessions: localData[this.localKey]?.sessions || {},
          uiState: localData[this.localKey]?.uiState || {}
        };
      } catch (error) {
        console.error(`Storage access attempt ${attempt} failed:`, error);
        
        if (error.message.includes('Extension context invalidated')) {
          // Show notification to user
          if (window.schemaSidebarController) {
            window.schemaSidebarController.showNotification(
              'Extension needs to be reloaded. Click the reload button in the sidebar.',
              'warning'
            );
          }
          
          // If this is the last attempt, throw the error
          if (attempt === this.retryAttempts) {
            throw error;
          }
          
          // Wait before retrying
          await new Promise(resolve => setTimeout(resolve, this.retryDelay));
          continue;
        }
        
        return null;
      }
    }
    return null;
  }

  async saveData(data) {
    // Split data between sync and local storage
    const { sessions, uiState, ...syncData } = data;
    
    await Promise.all([
      chrome.storage.sync.set({ [this.syncKey]: syncData }),
      chrome.storage.local.set({ 
        [this.localKey]: {
          sessions,
          uiState,
          version: data.version
        }
      })
    ]);
  }
}

class ProfileManager extends SchemaStorage {
  async getDomainProfiles(domain) {
    const data = await this.getStoredData();
    return data?.profiles?.[domain] || {};
  }

  async getAllProfiles() {
    const data = await this.getStoredData();
    return data?.profiles || {};
  }

  async deleteProfile(domain, profileType) {
    try {
      const data = await this.getStoredData();
      if (data?.profiles?.[domain]?.[profileType]) {
        delete data.profiles[domain][profileType];
        
        // Clean up empty domain entries
        if (Object.keys(data.profiles[domain]).length === 0) {
          delete data.profiles[domain];
        }
        
        // Only save to sync storage since profiles are in sync
        const { sessions, uiState, ...syncData } = data;
        await chrome.storage.sync.set({ [this.syncKey]: syncData });
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error deleting profile:', error);
      return false;
    }
  }
}

class SessionManager extends SchemaStorage {
  async getCurrentSession() {
    const data = await this.getStoredData();
    return data?.sessions?.current || null;
  }

  async startSession(domain, profileType) {
    try {
      const data = await this.getStoredData() || {};
      
      if (!data.sessions) {
        data.sessions = {};
      }
      
      data.sessions.current = {
        domain,
        profileType,
        capturedData: {},
        startedAt: new Date().toISOString()
      };
      
      // Only save to local storage since sessions are local
      await chrome.storage.local.set({ 
        [this.localKey]: {
          sessions: data.sessions,
          uiState: data.uiState || {},
          version: data.version
        }
      });
      return data.sessions.current;
    } catch (error) {
      console.error('Error starting session:', error);
      return null;
    }
  }
}

// --- END SidebarController and dependencies ---

class SidebarInjector {
  constructor() {
    this.isInjected = false;
    this.sidebarContainer = null;
    this.isVisible = false;
    this.currentDomain = window.location.hostname;
  }

  async initialize() {
    // Listen for messages from popup/background to show sidebar
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      if (message.action === 'toggleSidebar') {
        this.toggleSidebar();
        sendResponse({ success: true });
      } else if (message.action === 'showSidebar') {
        this.showSidebar();
        sendResponse({ success: true });
      } else if (message.action === 'hideSidebar') {
        this.hideSidebar();
        sendResponse({ success: true });
      }
    });
    // Prevent all forms from reloading the page on submit
    window.addEventListener('submit', function(e) {
      if (e.target && e.target.tagName === 'FORM') {
        e.preventDefault();
      }
    }, true);
  }

  async injectSidebar() {
    if (this.isInjected) return;
    try {
      // Inject sidebar CSS and wait for it to load
      const cssUrl = chrome.runtime.getURL('sidebar/sidebar.css');
      await new Promise((resolve) => {
        if (!document.getElementById('schema-speed-sidebar-css')) {
          const link = document.createElement('link');
          link.id = 'schema-speed-sidebar-css';
          link.rel = 'stylesheet';
          link.type = 'text/css';
          link.href = cssUrl;
          link.onload = resolve;
          document.head.appendChild(link);
        } else {
          resolve();
        }
      });

      // Fetch sidebar HTML
      const htmlUrl = chrome.runtime.getURL('sidebar/sidebar.html');
      const htmlText = await fetch(htmlUrl).then(r => r.text());
      // Parse the HTML and extract the sidebar root
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = htmlText;
      const sidebarRoot = tempDiv.querySelector('#schema-speed-sidebar-container');
      if (!sidebarRoot) throw new Error('Sidebar root not found in HTML');

      // Add dark mode class
      sidebarRoot.classList.add('dark');

      // Create sidebar container with complete isolation
      this.sidebarContainer = sidebarRoot;
      this.sidebarContainer.style.position = 'fixed';
      this.sidebarContainer.style.top = '0';
      this.sidebarContainer.style.right = '0';
      this.sidebarContainer.style.width = '600px';
      this.sidebarContainer.style.height = '100vh';
      this.sidebarContainer.style.zIndex = '2147483647';
      this.sidebarContainer.style.boxShadow = '-4px 0 20px rgba(0,0,0,0.15)';
      this.sidebarContainer.style.transition = 'transform 0.3s ease';
      this.sidebarContainer.style.transform = 'translateX(100%)';
      this.sidebarContainer.style.display = 'flex';
      this.sidebarContainer.style.flexDirection = 'column';
      this.sidebarContainer.style.borderLeft = '2px solid var(--schema-speed-primary, #6366f1)';
      this.sidebarContainer.style.background = '#18181b';
      // Ensure complete isolation from page
      this.sidebarContainer.style.pointerEvents = 'auto';
      this.sidebarContainer.style.isolation = 'isolate';
      this.sidebarContainer.style.contain = 'layout style paint';
      this.sidebarContainer.style.fontFamily = '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif';
      this.sidebarContainer.style.fontSize = '14px';
      this.sidebarContainer.style.lineHeight = '1.5';

      document.body.appendChild(this.sidebarContainer);

      // INSTEAD OF INJECTING A SCRIPT TAG, instantiate SidebarController directly
      this.sidebarController = new SidebarController();
      await this.sidebarController.initialize();
      window.schemaSidebarController = this.sidebarController;

      // --- Enhancement: Load and render last used template on sidebar open ---
      try {
        const lastUsedResponse = await chrome.runtime.sendMessage({ action: 'getTemplates' });
        if (lastUsedResponse.success) {
          const lastUsed = lastUsedResponse.lastUsed;
          const templates = lastUsedResponse.templates;
          console.debug('[DEBUG] SidebarInjector: Last used template:', lastUsed);
          if (lastUsed && templates && templates[lastUsed]) {
            await this.sidebarController.useTemplate(lastUsed);
            this.sidebarController.showNotification(`Loaded last used template: ${lastUsed}`, 'success');
          } else {
            console.warn('[DEBUG] SidebarInjector: No last used template found.');
            this.sidebarController.showNotification('No last used template found.', 'info');
          }
        } else {
          console.error('[DEBUG] SidebarInjector: Failed to get templates:', lastUsedResponse.error);
          this.sidebarController.showNotification('Failed to load templates.', 'error');
        }
      } catch (error) {
        console.error('[DEBUG] SidebarInjector: Error loading last used template:', error);
        this.sidebarController.showNotification('Error loading last used template.', 'error');
      }
      // Always refresh template list UI after init
      if (this.sidebarController.loadSavedTemplates) {
        await this.sidebarController.loadSavedTemplates();
      }
      // --- End enhancement ---

      this.isInjected = true;
    } catch (error) {
      console.error('Error injecting sidebar:', error);
    }
  }

  async showSidebar() {
    if (!this.isInjected) {
      await this.injectSidebar();
    }
    if (this.sidebarContainer) {
      this.sidebarContainer.style.transform = 'translateX(0)';
      this.isVisible = true;
    }
  }

  hideSidebar() {
    if (this.sidebarContainer) {
      this.sidebarContainer.style.transform = 'translateX(100%)';
      this.isVisible = false;
    }
  }

  toggleSidebar() {
    if (this.isVisible) {
      this.hideSidebar();
    } else {
      this.showSidebar();
    }
  }
}

// Initialize sidebar injector
const sidebarInjector = new SidebarInjector();
sidebarInjector.initialize();

// Make it globally available
window.schemaSpeedSidebar = sidebarInjector;
