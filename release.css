@import url(https://fonts.googleapis.com/css2?family=Kalam:wght@300;400;700&family=Ubuntu:ital,wght@0,300;0,400;0,500;0,700;1,300;1,400;1,500;1,700&display=swap);
html,
body,
div,
span,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
abbr,
address,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
samp,
small,
strong,
sub,
sup,
var,
b,
i,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section,
summary,
time,
mark,
audio,
video {
  border: 0;
  outline: 0;
  vertical-align: baseline;
}

body {
  line-height: 1;
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
  display: block;
}

nav,
ul,
li,
ol {
  list-style: none;
  text-decoration: none;
  padding: 0;
  margin: 0;
}

blockquote,
q {
  quotes: none;
}

blockquote:before,
blockquote:after,
q:before,
q:after {
  content: '';
  content: none;
}

a {
  margin: 0;
  padding: 0;
  font-size: 100%;
  vertical-align: baseline;
  background: transparent;
}

ins {
  background-color: #ff9;
  color: #000;
  text-decoration: none;
}

mark {
  background-color: #ff9;
  color: #000;
  font-style: italic;
  font-weight: bold;
}

del {
  text-decoration: line-through;
}

abbr[title],
dfn[title] {
  border-bottom: 1px dotted;
  cursor: help;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

hr {
  display: block;
  height: 1px;
  border: 0;
  border-top: 1px solid #cccccc;
  margin: 1em 0;
  padding: 0;
}

input,
select {
  vertical-align: middle;
}

.inline-flex {
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
}

.flex-column,
.main-header,
.banner-block,
.page-section,
.page-section .card .card-body .content-list,
.common-footer,
.common-footer .nav,
.loader,
.card {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-box-direction: normal;
  -webkit-box-orient: vertical;
  -webkit-flex-direction: column;
  -moz-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.flexbox,
.main-header .navbar,
.main-header .navbar .navbar-nav,
.banner-block .banner-theam .banner-button,
.card-header,
.card-footer,
.btn {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-box-direction: normal;
  -webkit-box-orient: horizontal;
  -webkit-flex-direction: row;
  -moz-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
}

.all-center,
.main-header .navbar,
.loader,
.btn {
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  -webkit-justify-content: center;
  -moz-justify-content: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  -webkit-align-items: center;
  -moz-align-items: center;
  align-items: center;
}

.justify-center {
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  -webkit-justify-content: center;
  -moz-justify-content: center;
  justify-content: center;
}

.justify-between {
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  -webkit-justify-content: space-between;
  -moz-justify-content: space-between;
  justify-content: space-between;
}

.justify-around {
  -ms-flex-pack: distribute;
  -webkit-justify-content: space-around;
  -moz-justify-content: space-around;
  justify-content: space-around;
}

.justify-start {
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  -moz-justify-content: flex-start;
  justify-content: flex-start;
}

.justify-end {
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  -webkit-justify-content: flex-end;
  -moz-justify-content: flex-end;
  justify-content: flex-end;
}

.item-center,
.main-header .navbar .navbar-nav,
.banner-block .banner-theam .banner-button,
.card-header {
  -webkit-box-align: center;
  -ms-flex-align: center;
  -webkit-align-items: center;
  -moz-align-items: center;
  align-items: center;
}

.item-start {
  -webkit-box-align: start;
  -ms-flex-align: start;
  -webkit-align-items: flex-start;
  -moz-align-items: flex-start;
  align-items: flex-start;
}

.item-end {
  -webkit-box-align: end;
  -ms-flex-align: end;
  -webkit-align-items: flex-end;
  -moz-align-items: flex-end;
  align-items: flex-end;
}

.item-stretch {
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  -webkit-align-items: stretch;
  -moz-align-items: stretch;
  align-items: stretch;
}

.item-nowrap {
  -ms-flex-wrap: none;
  -webkit-flex-wrap: nowrap;
  flex-wrap: nowrap;
}

.item-wrap {
  -ms-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  flex-wrap: wrap;
}

.dna-gradient {
  background-image: -webkit-linear-gradient(legacy-direction(to top), #262a37, rgba(38,42,55,0.2));
  background-image: -webkit-linear-gradient(to top, #262a37, rgba(38,42,55,0.2));
  background: -ms-linear-gradient(to top, #262a37, rgba(38,42,55,0.2));
  background-image: -ms-linear-gradient(to top, #262a37, rgba(38,42,55,0.2));
  background-image: linear-gradient(to top, #262a37, rgba(38,42,55,0.2));
}

.image-overlay {
  background-image: -webkit-linear-gradient(legacy-direction(to top), rgba(38,42,55,0.9), rgba(38,42,55,0.1));
  background-image: -webkit-linear-gradient(to top, rgba(38,42,55,0.9), rgba(38,42,55,0.1));
  background: -ms-linear-gradient(to top, rgba(38,42,55,0.9), rgba(38,42,55,0.1));
  background-image: -ms-linear-gradient(to top, rgba(38,42,55,0.9), rgba(38,42,55,0.1));
  background-image: linear-gradient(to top, rgba(38,42,55,0.9), rgba(38,42,55,0.1));
}

.break-text {
  word-break: break-all;
  line-break: strict;
  text-wrap: balance;
  -webkit-hyphens: "auto";
  -moz-hyphens: "auto";
  -o-hyphens: "auto";
  hyphens: "auto";
}

.main-header {
  background-color: transparent;
  width: 100%;
  position: absolute;
  top: 0;
  height: 60px;
  padding: 15px 0;
  z-index: 9999;
  -moz-transition: all 0.4s ease-in-out;
  -o-transition: all 0.4s ease-in-out;
  -webkit-transition: all 0.4s ease-in-out;
  transition: all 0.4s ease-in-out;
}

@media (max-width: 319px) {
  .main-header {
    padding: 20px 20px;
  }
}

@media (min-width: 320px) and (max-width: 767px) {
  .main-header {
    padding: 20px 30px;
  }
}

@media (min-width: 1024px) and (max-width: 1140px) {
  .main-header {
    padding: 20px 40px;
  }
}

.main-header .navbar {
  padding: 0;
}

.main-header .navbar .nav-logo {
  flex: 0 0 auto;
  margin-right: 30px;
  max-height: 50px;
}

.main-header .navbar .nav-logo .nav-brand {
  flex: 0 0 auto;
  max-height: 50px;
}

.main-header .navbar .nav-logo .nav-brand img {
  max-height: 50px;
}

.main-header .navbar .navbar-nav {
  flex: 1 1 auto;
}

.main-header .navbar .navbar-nav .nav-item {
  flex: 0 0 auto;
}

.main-header .navbar .navbar-nav .nav-item .nav-link {
  display: block;
  padding: 8px 30px;
  text-transform: uppercase;
  color: white;
}

.banner-block {
  padding-top: 7rem;
  background-color: #262a37;

  background-repeat: no-repeat;
  background-position: right center;
  background-size: 23%;
}

.banner-block .banner-theam {
  padding-bottom: 7rem;
}

.banner-block .banner-theam h1 {
  color: white;
  font-weight: 700;
  font-size: 48px;
  margin-bottom: 30px;
  line-height: 66px;
  font-family: "Kalam", cursive;
}

.banner-block .banner-theam p {
  color: white;
  font-weight: 400;
  font-size: 23 px;
}

.banner-block .banner-theam .banner-button {
  margin-top: 50px;
}

.page-section {
  padding: 5rem 0;
  border-bottom: solid 1px #f9fafa;
}

.page-section h2 {
  color: rgba(38,42,55,0.5);
  font-size: 40px;
  margin-bottom: 40px;
  text-align: center;
}
.page-section .row{
  justify-content: center;
}
.page-section .card {
  height: 100%;
}

.page-section .card .card-header {
  flex: 0 1 auto;
}

.page-section .card .card-header h2 {
  color: rgba(38,42,55,0.5);
  font-size: 16px;
  margin-bottom: 40px;
}

.page-section .card .card-body {
  flex: 1 1 auto;
}

.page-section .card .card-body .content-list li {
  flex: 1 1 auto;
  padding-left: 15px;
  position: relative;
}

.page-section .card .card-body .content-list li:not(:last-child) {
  margin-bottom: 15px;
}

.page-section .card .card-body .content-list li:before {
  content: "";
  background-color: rgba(38,42,55,0.5);
  width: 4px;
  height: 4px;
  position: absolute;
  left: 0;
  top: 8px;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
}

.common-footer {
  padding-top: 80px;
  background: #f9fafa;
}

.common-footer .img50 {
  max-width: 150px;
  height: auto;
}

.common-footer p {
  margin-top: 20px;
}

.common-footer h6 {
  font-family: "Kalam", cursive;
}

.common-footer .nav .nav-link {
  flex: 1 1 auto;
  color: #757575;
}

.main-wrapper,
.client-block,
.employee-block,
.project-block {
  -webkit-animation: fadeIn 0.8s;
  -moz-animation: fadeIn 0.8s;
  -o-animation: fadeIn 0.8s;
  animation: fadeIn 0.8s;
}

@-webkit-keyframes fadeIn {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

@-moz-keyframes fadeIn {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

@-o-keyframes fadeIn {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

.loader {
  position: fixed;
  top: 0;
  bottom: 0;
  width: 100%;
  background-color: #f7f7f7;
  z-index: 999999;
}

.loader .loader-inner {
  flex: 0 0 auto;
  border: 2px solid #f50136;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  border-top-color: transparent;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -webkit-animation: loader-inner 0.8s cubic-bezier(0.42, 0.61, 0.58, 0.41) infinite;
  -moz-animation: loader-inner 0.8s cubic-bezier(0.42, 0.61, 0.58, 0.41) infinite;
  -o-animation: loader-inner 0.8s cubic-bezier(0.42, 0.61, 0.58, 0.41) infinite;
  animation: loader-inner 0.8s cubic-bezier(0.42, 0.61, 0.58, 0.41) infinite;
}

@-webkit-keyframes loader-inner {
  0% {
    -moz-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  100% {
    -moz-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@-moz-keyframes loader-inner {
  0% {
    -moz-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  100% {
    -moz-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@-o-keyframes loader-inner {
  0% {
    -moz-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  100% {
    -moz-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes loader-inner {
  0% {
    -moz-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  100% {
    -moz-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

.responsive-button {
  display: none;
  margin-top: 30px;
}

@media (max-width: 319px) {
  .responsive-button {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -webkit-box-direction: normal;
    -webkit-box-orient: horizontal;
    -webkit-flex-direction: row;
    -moz-flex-direction: row;
    -ms-flex-direction: row;
    flex-direction: row;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    -webkit-justify-content: center;
    -moz-justify-content: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    -webkit-align-items: center;
    -moz-align-items: center;
    align-items: center;
  }
}

@media (min-width: 320px) and (max-width: 767px) {
  .responsive-button {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -webkit-box-direction: normal;
    -webkit-box-orient: horizontal;
    -webkit-flex-direction: row;
    -moz-flex-direction: row;
    -ms-flex-direction: row;
    flex-direction: row;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    -webkit-justify-content: center;
    -moz-justify-content: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    -webkit-align-items: center;
    -moz-align-items: center;
    align-items: center;
  }
}

.responsive-button .btn {
  margin: 0;
}

@media (max-width: 319px) {
  .responsive-button .btn {
    width: 100%;
  }
}

@media (min-width: 320px) and (max-width: 767px) {
  .responsive-button .btn {
    width: 100%;
  }
}

.container {
  -moz-transition: all 0.8s ease-in-out;
  -o-transition: all 0.8s ease-in-out;
  -webkit-transition: all 0.8s ease-in-out;
  transition: all 0.8s ease-in-out;
}

@media (max-width: 319px) {
  .container {
    max-width: 100%;
    min-width: 100%;
    padding: 0px 30px;
  }
}

@media (min-width: 320px) and (max-width: 767px) {
  .container {
    max-width: 100%;
    min-width: 100%;
    padding: 0px 30px;
  }
}

@media (min-width: 768px) and (max-width: 992px) {
  .container {
    max-width: 100%;
    min-width: 100%;
    padding: 0px 15px;
  }
}

@media (min-width: 992px) and (max-width: 1024px) {
  .container {
    max-width: 100%;
    min-width: 100%;
    padding: 0px 15px;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1140px;
    padding: 0px 15px;
  }
}

.container,
.container-fluid {
  -webkit-animation: fadeIn 0.8s;
  -moz-animation: fadeIn 0.8s;
  -o-animation: fadeIn 0.8s;
  animation: fadeIn 0.8s;
}

@-webkit-keyframes fadeIn {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

@-moz-keyframes fadeIn {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

@-o-keyframes fadeIn {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

.card {
  border: 0;
  margin-bottom: 0px;
  border-radius: 4px;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  -webkit-box-shadow: 0 1px 4px rgba(0,0,0,0.1);
  -moz-box-shadow: 0 1px 4px rgba(0,0,0,0.1);
  box-shadow: 0 1px 4px rgba(0,0,0,0.1);
}

.card-header {
  border: 0;
  flex: 0 1 auto;
  background-color: transparent;
}

.card-header span {
  flex: 0 0 auto;
  width: 20px;
  height: 20px;
  margin-right: 8px;
}

.card-header span svg {
  width: 100%;
  height: 100%;
}

.card-header span svg path {
  fill: #3578a6;
}

.card-header h3 {
  font-size: 18px;
  margin: 0;
  font-weight: 500;
  font-size: 16px;
  color: rgba(3,3,3,0.8);
}

.card-body {
  flex: 1 1 auto;
  margin: 0;
}

.card-body .card-title {
  font-size: 14px;
  margin-bottom: 20px;
  text-transform: uppercase;
  color: rgba(10,21,29,0.5);
}

.card-footer {
  border: 0;
  flex: 0 1 auto;
}

.form-control {
  border: 0;
  height: 54px;
  font-size: 16px;
  padding: 10px 17px;
  background: white;
  border-radius: 4px;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
}

.btn {
  padding: 10px 30px;
  font-size: 13px;
  font-weight: 400;
  text-transform: uppercase;
  border-radius: 50px;
  -webkit-border-radius: 50px;
  -moz-border-radius: 50px;
}

.btn.btn-default {
  border-color: #046dd6;
  color: #046dd6;
}

.btn.btn-primary {
  border: 0;
  color: white;
  background: #046dd6;
}

* {
  margin: 0;
  padding: 0;
}

ul,
ol,
li {
  list-style: none;
  margin: 0;
  padding: 0;
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  -webkit-box-shadow: 0 0 0 rgba(255,255,255,0) !important;
  -moz-box-shadow: 0 0 0 rgba(255,255,255,0) !important;
  box-shadow: 0 0 0 rgba(255,255,255,0) !important;
  background-color: transparent !important;
}

::selection {
  color: #000 !important;
  background-color: #accef7 !important;
}

button,
input,
textarea {
  -webkit-box-shadow: 0 0 0 rgba(255,255,255,0);
  -moz-box-shadow: 0 0 0 rgba(255,255,255,0);
  box-shadow: 0 0 0 rgba(255,255,255,0);
}

button:hover,
button:visited,
button:focus,
button:active,
input:hover,
input:visited,
input:focus,
input:active,
textarea:hover,
textarea:visited,
textarea:focus,
textarea:active {
  -webkit-box-shadow: 0 0 0 rgba(255,255,255,0) !important;
  -moz-box-shadow: 0 0 0 rgba(255,255,255,0) !important;
  box-shadow: 0 0 0 rgba(255,255,255,0) !important;
  outline: 0;
}

img {
  border: 0;
  outline: 0;
  max-width: 100%;
  display: inline-block;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  padding: 0;
  word-break: break-word;
  white-space: normal;
}

a {
  text-decoration: none;
}

a:hover,
a:focus {
  text-decoration: none;
}

p {
  font-size: 14px;
  line-height: 22px;
  margin: 0;
  word-break: break-word;
  white-space: normal;
}

html {
  scroll-behavior: smooth;
}

body {
  margin: 0;
  padding: 0;
  font-family: "Ubuntu", sans-serif;
  line-height: 1.428;
  font-weight: 400;
  font-size: 14px;
}

body.no-scrol {
  overflow: hidden;
}

.version-list{
  margin-top: 5px;
  text-align: center;
  padding: 0 20px;
}
.version-list label{
  margin: 5px;
  padding: 5px 20px;
  border-radius: 10px;
  background-color: #2b324bc9;
  color: #fff;
  height: 25px;
  line-height: 15px;
  cursor: pointer;
}
.version-list label:hover{
  background-color: #262a37;
}
.version-list label.active{
  background-color: #262a37;
}

#version-list {
  overflow-y: hidden;
  overflow-x: scroll;
  margin: 10px 2px;
}

html ::-webkit-scrollbar {
  width: 7px;
  height: 7px;
}

/* Track */
html ::-webkit-scrollbar-track {
  background: #f1f1f17a;
  margin: 5px 0;
}

/* Handle */
html ::-webkit-scrollbar-thumb {
  background: #ccc8c88d;
}

/* Handle on hover */
html ::-webkit-scrollbar-thumb:hover {
  background: rgb(194, 193, 193);
}