!function(e){var t={};function n(r){if(t[r])return t[r].exports;var o=t[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,n),o.l=!0,o.exports}n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)n.d(r,o,function(t){return e[t]}.bind(null,o));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=84)}({11:function(e,t,n){"use strict";n.r(t),n.d(t,"GET_GTM_ACCESSTOKEN",(function(){return r})),n.d(t,"DELETE_GTM_ACCESSTOKEN",(function(){return o})),n.d(t,"CHROMETOKENPERMISSIONERROR",(function(){return s})),n.d(t,"CLICKEDBROWSERACTION",(function(){return c})),n.d(t,"CHECKCONTENTSCRIPTEXIST",(function(){return i})),n.d(t,"SHOWMARKUPINNEWWINDOW",(function(){return u})),n.d(t,"CLOSEEXTENSION",(function(){return a})),n.d(t,"TESTTHEMARKUP",(function(){return E})),n.d(t,"CONTENT_SCRIPT_INVOKED",(function(){return T}));var r="GET_GTM_ACCESSTOKEN",o="DELETE_GTM_ACCESSTOKEN",s="CHROMETOKENPERMISSIONERROR",c="CLICKEDBROWSERACTION",i="CHECKCONTENTSCRIPTEXIST",u="SHOWMARKUPINNEWWINDOW",a="CLOSEEXTENSION",E="TESTTHEMARKUP",T="CONTENT_SCRIPT_INVOKED"},12:function(e,t,n){"use strict";n.r(t),n.d(t,"GOOGLE_RICH_RESULT_URL",(function(){return r})),n.d(t,"GET_RULE_BYID_URL",(function(){return o})),n.d(t,"SAVE_RULE_URL",(function(){return s})),n.d(t,"GET_RULES_URL",(function(){return c})),n.d(t,"GENERATE_SCRIPT",(function(){return i})),n.d(t,"LOGIN_URL",(function(){return u})),n.d(t,"TUTORIAL_VIDEO_URL",(function(){return a})),n.d(t,"AUTO_ARRAY_VIDEO_URL",(function(){return E})),n.d(t,"SCHEMA_DEV_SUPPORT_URL",(function(){return T})),n.d(t,"SCHEMA_DEV_URL",(function(){return d})),n.d(t,"GTM_BASE_URL",(function(){return _})),n.d(t,"GTM_DEPLOY_SCHEMA_URL",(function(){return f})),n.d(t,"GTM_RETRIEVE_SCHEMA_URL",(function(){return l})),n.d(t,"GTM_DELETE_Schema_URL",(function(){return S})),n.d(t,"GTMAUTHURL",(function(){return N})),n.d(t,"BOOTSTRAP",(function(){return g})),n.d(t,"POSTINSTALLATIONLINK",(function(){return I})),n.d(t,"POSTUNINSTALLURL",(function(){return L})),n.d(t,"TESTSCHEMAAPI",(function(){return M}));var r="https://search.google.com/test/rich-results",o="/get_rule_by_id",s="/save_rule",c="/get_rules",i="/generate_script",u="/native_login",a="https://storyxpress.co/video/kazj73n005plxgv61",E="https://support.schema.dev/portal/en/kb/articles/schema-builder-video-walkthrough#Advanced_Usage_-_Populating_Reviews_and_repeating_elements",T="https://support.schema.dev",d="https://schema.dev",_="",f="GTM_DEPLOY_SCHEMA_URL",l="GTM_RETRIEVE_SCHEMA_URL",S="GTM_DELETE_Schema_URL",O="",h=!0,m="token",R="state_parameter_passthrough_value",p="",C="838170823540-t9n6k3ejqto293armr5emoiljp7ouuc5.apps.googleusercontent.com",N="".concat("","?scope=").concat(O,"&include_granted_scopes=").concat(h,"&response_type=").concat(m,"&state=").concat(R,"&redirect_uri=").concat(p,"&client_id=").concat(C),g={rel:"stylesheet",href:"https://maxcdn.bootstrapcdn.com/bootstrap/4.4.1/css/bootstrap.min.css",integrity:"sha384-Vkoo8x4CGsO3+Hhxv8T/Q5PaXtkKtu6ug5TOeNV6gBiFeWPGFN9MuhOf23Q9Ifjh",crossorigin:"anonymous"},I="https://schema.dev/success-schema-builder-installed/",L="https://schema.dev/uninstall",M="https://d350l08hhk37nz.cloudfront.net/api/validate"},84:function(e,t,n){var r=n(12),o=(r.GTMAUTHURL,r.POSTINSTALLATIONLINK),s=r.POSTUNINSTALLURL,c=r.TESTSCHEMAAPI,i=n(11),u=i.CLICKEDBROWSERACTION,a=i.GET_GTM_ACCESSTOKEN,E=(i.CHROMETOKENPERMISSIONERROR,i.SHOWMARKUPINNEWWINDOW),T=i.DELETE_GTM_ACCESSTOKEN,d=i.CLOSEEXTENSION,_=i.CHECKCONTENTSCRIPTEXIST,f=i.TESTTHEMARKUP,l=i.CONTENT_SCRIPT_INVOKED;try{var S=function(e){chrome.scripting.insertCSS({target:{tabId:e.id},files:["extension.styles.css","content.styles.css"]},(function(){chrome.scripting.executeScript({target:{tabId:e.id},files:["contentScript.bundle.js"]},(function(){O({tab:e},!0)}))}))},O=function(e,t,n){var r=u;chrome.tabs.sendMessage(e.tab.id,{message:r,show:t,extraMsg:n})},h=function(e){chrome.tabs.sendMessage(e.id,{text:_},(function(t){chrome.runtime.lastError?S(e):t&&t.yes?O({tab:e}):S(e)}))},m=chrome.runtime.getURL("release.html"),R="New!",p=[];chrome.action.onClicked.addListener((function(e){var t;h(e),t=e.id,p.includes(t)?p.includes(t)&&p.splice(p.indexOf(t),1):p.push(t),chrome.storage.session.set({activeTabs:p}).catch((function(e){return console.error(e)})),setTimeout((function(){chrome.action.getBadgeText({},(function(e){e==R&&(chrome.action.setBadgeText({text:""}),chrome.tabs.create({url:m+"?v="+chrome.runtime.getManifest().version}))}))}),2e3)})),chrome.runtime.onMessage.addListener((function(e,t,n){return e.action===l?(r=t.tab,void chrome.storage.session.get(["activeTabs"]).then((function(e){e.activeTabs&&e.activeTabs.includes(r.id)&&h(r)}))):e.message===E?chrome.tabs.create({url:chrome.runtime.getURL("viewmarkup.html?sc="+e.schema)}):e.message===d?(function(e){var t=p.indexOf(e.id);-1!==t&&(p.splice(t,1),chrome.storage.session.set({activeTabs:p}))}(t.tab),O(t,!1,{message:e.message})):e.action===f?(function(e,t){var n=e.html,r=e.source;fetch(c,{method:"POST",body:JSON.stringify({html:n,source:r}),headers:{"Content-Type":"application/json"}}).then((function(e){return e.json()})).then(t).catch((function(e){t({error:!0,errMessage:e&&e.response&&e.response.data&&e.response.data.message,status:e&&e.response&&e.response.status})}))}(e,n),!0):(e.message===T&&chrome.identity.removeCachedAuthToken({token:e.token}),e.message===a||void 0);var r})),chrome.runtime.setUninstallURL(s),chrome.runtime.onInstalled.addListener((function(e){var t=chrome.runtime.getManifest().version;"update"===e.reason&&t!==e.previousVersion&&(chrome.action.setBadgeText({text:R}),chrome.action.setBadgeBackgroundColor({color:"#99ff11"})),"install"===e.reason&&chrome.tabs.create({url:o})}))}catch(e){console.log("error: ",e)}}});