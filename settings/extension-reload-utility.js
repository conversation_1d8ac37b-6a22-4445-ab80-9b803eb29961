// Extension Reload Utility for Schema Speed
// Instantly reloads the extension with no confirmation or popup

(function() {
    'use strict';
    
    function reloadExtension() {
        try {
            // Check if we're in an extension context
            if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.reload) {
                console.log('Schema Speed: Reloading extension...');
                chrome.runtime.reload();
            } else {
                console.log('Schema Speed: Not in extension context, skipping reload');
            }
        } catch (error) {
            // Don't log errors that are expected when extension context is invalidated
            if (error.message && error.message.includes('Extension context invalidated')) {
                console.log('Schema Speed: Extension context invalidated (expected during reload)');
            } else {
                console.error('Schema Speed: Error during reload:', error);
            }
        }
    }

    // Make reload function available globally
    if (typeof window !== 'undefined') {
        window.SchemaSpeedExtensionReload = {
            reload: reloadExtension
        };
    }

    console.log('Schema Speed Extension Reload Utility: Loaded');
})(); 