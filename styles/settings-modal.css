/* Schema Speed Assistant - Settings Modal Styles */

/* Modal Base Styles */
.schema-speed-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10000;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.schema-speed-modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.schema-speed-modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* Modal Header */
.schema-speed-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.schema-speed-modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #111827;
}

.schema-speed-close-btn {
  background: none;
  border: none;
  font-size: 24px;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.schema-speed-close-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

/* Modal Body */
.schema-speed-modal-body {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* Settings Tabs */
.schema-speed-settings-tabs {
  display: flex;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.schema-speed-settings-tab {
  flex: 1;
  padding: 12px 16px;
  background: none;
  border: none;
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 2px solid transparent;
}

.schema-speed-settings-tab:hover {
  color: #374151;
  background: #f3f4f6;
}

.schema-speed-settings-tab.active {
  color: #2563eb;
  border-bottom-color: #2563eb;
  background: white;
}

/* Settings Content */
.schema-speed-settings-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

.schema-speed-settings-panel {
  display: none;
  padding: 24px;
}

.schema-speed-settings-panel.active {
  display: block;
}

/* Setting Groups */
.schema-speed-setting-group {
  margin-bottom: 32px;
}

.schema-speed-setting-group:last-child {
  margin-bottom: 0;
}

.schema-speed-setting-group h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #111827;
}

.schema-speed-setting-group h5 {
  margin: 20px 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.schema-speed-setting-description {
  margin: 0 0 20px 0;
  font-size: 14px;
  color: #6b7280;
  line-height: 1.5;
}

/* Setting Items */
.schema-speed-setting-item {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.schema-speed-setting-item label {
  font-size: 14px;
  color: #374151;
  font-weight: 500;
}

.schema-speed-setting-item select,
.schema-speed-setting-item input[type="number"] {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  min-width: 120px;
}

.schema-speed-setting-item select:focus,
.schema-speed-setting-item input[type="number"]:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Checkbox Styles */
.schema-speed-checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
  color: #374151;
  font-weight: 500;
}

.schema-speed-checkbox-label input[type="checkbox"] {
  display: none;
}

.schema-speed-checkmark {
  width: 18px;
  height: 18px;
  border: 2px solid #d1d5db;
  border-radius: 4px;
  margin-right: 12px;
  position: relative;
  transition: all 0.2s ease;
}

.schema-speed-checkbox-label input[type="checkbox"]:checked + .schema-speed-checkmark {
  background: #2563eb;
  border-color: #2563eb;
}

.schema-speed-checkbox-label input[type="checkbox"]:checked + .schema-speed-checkmark::after {
  content: '';
  position: absolute;
  left: 5px;
  top: 2px;
  width: 4px;
  height: 8px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

/* Toggle Switch Styles */
.schema-speed-toggle-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
  color: #374151;
  font-weight: 600;
}

.schema-speed-toggle-label input[type="checkbox"] {
  display: none;
}

.schema-speed-toggle-slider {
  width: 44px;
  height: 24px;
  background: #d1d5db;
  border-radius: 12px;
  margin-right: 12px;
  position: relative;
  transition: all 0.3s ease;
}

.schema-speed-toggle-slider::before {
  content: '';
  position: absolute;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: white;
  top: 2px;
  left: 2px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.schema-speed-toggle-label input[type="checkbox"]:checked + .schema-speed-toggle-slider {
  background: #10b981;
}

.schema-speed-toggle-label input[type="checkbox"]:checked + .schema-speed-toggle-slider::before {
  transform: translateX(20px);
}

/* Debug-specific Styles */
.schema-speed-debug-toggle {
  flex-direction: column;
  align-items: flex-start;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.schema-speed-debug-status {
  margin-top: 8px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.schema-speed-status-enabled {
  color: #10b981;
  font-weight: 600;
}

.schema-speed-status-disabled {
  color: #6b7280;
  font-weight: 500;
}

.schema-speed-debug-config {
  margin-top: 20px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.schema-speed-debug-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  margin-top: 12px;
}

/* Debug Stats */
.schema-speed-debug-stats {
  margin-top: 20px;
  padding: 16px;
  background: #f0f9ff;
  border-radius: 8px;
  border: 1px solid #bae6fd;
}

.schema-speed-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 12px;
  margin-top: 12px;
}

.schema-speed-stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e0f2fe;
}

.schema-speed-stat-label {
  font-size: 13px;
  color: #0369a1;
  font-weight: 500;
}

.schema-speed-stat-item span:last-child {
  font-size: 14px;
  font-weight: 600;
  color: #0c4a6e;
}

/* Debug Actions */
.schema-speed-debug-actions {
  margin-top: 20px;
  padding: 16px;
  background: #fef3c7;
  border-radius: 8px;
  border: 1px solid #fcd34d;
}

.schema-speed-action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 12px;
}

/* Button Styles */
.schema-speed-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.schema-speed-btn-primary {
  background: #2563eb;
  color: white;
}

.schema-speed-btn-primary:hover {
  background: #1d4ed8;
}

.schema-speed-btn-secondary {
  background: #6b7280;
  color: white;
}

.schema-speed-btn-secondary:hover {
  background: #4b5563;
}

.schema-speed-btn-danger {
  background: #dc2626;
  color: white;
}

.schema-speed-btn-danger:hover {
  background: #b91c1c;
}

/* Modal Footer */
.schema-speed-modal-footer {
  padding: 20px 24px;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.schema-speed-modal-footer .schema-speed-btn {
  padding: 10px 20px;
  font-size: 14px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .schema-speed-modal-content {
    width: 95%;
    max-height: 95vh;
  }
  
  .schema-speed-modal-header,
  .schema-speed-settings-panel,
  .schema-speed-modal-footer {
    padding: 16px;
  }
  
  .schema-speed-debug-options {
    grid-template-columns: 1fr;
  }
  
  .schema-speed-stats-grid {
    grid-template-columns: 1fr;
  }
  
  .schema-speed-action-buttons {
    flex-direction: column;
  }
  
  .schema-speed-action-buttons .schema-speed-btn {
    justify-content: center;
  }
}

/* Animation */
.schema-speed-modal {
  animation: schema-speed-modal-fade-in 0.2s ease-out;
}

@keyframes schema-speed-modal-fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.schema-speed-modal-content {
  animation: schema-speed-modal-slide-in 0.3s ease-out;
}

@keyframes schema-speed-modal-slide-in {
  from {
    transform: translate(-50%, -60%);
    opacity: 0;
  }
  to {
    transform: translate(-50%, -50%);
    opacity: 1;
  }
}

/* Dark mode support (if needed) */
@media (prefers-color-scheme: dark) {
  .schema-speed-modal-content {
    background: #1f2937;
    color: #f9fafb;
  }
  
  .schema-speed-modal-header,
  .schema-speed-modal-footer,
  .schema-speed-settings-tabs {
    background: #111827;
    border-color: #374151;
  }
  
  .schema-speed-modal-header h3 {
    color: #f9fafb;
  }
  
  .schema-speed-settings-tab {
    color: #9ca3af;
  }
  
  .schema-speed-settings-tab:hover {
    color: #d1d5db;
    background: #374151;
  }
  
  .schema-speed-settings-tab.active {
    background: #1f2937;
    color: #60a5fa;
    border-bottom-color: #60a5fa;
  }
  
  .schema-speed-setting-group h4,
  .schema-speed-setting-group h5 {
    color: #f9fafb;
  }
  
  .schema-speed-setting-item label {
    color: #d1d5db;
  }
  
  .schema-speed-debug-toggle,
  .schema-speed-debug-config {
    background: #111827;
    border-color: #374151;
  }
  
  .schema-speed-debug-stats {
    background: #0f172a;
    border-color: #1e293b;
  }
  
  .schema-speed-stat-item {
    background: #1e293b;
    border-color: #334155;
  }
}
