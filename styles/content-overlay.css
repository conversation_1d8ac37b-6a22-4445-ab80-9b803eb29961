/* Schema Speed Assistant - Content Overlay Styles */
/* Styles for the content capture overlay and visual elements */

/* CSS Variables for consistency with popup design */
:root {
  --schema-speed-primary: #7c3aed;
  --schema-speed-primary-foreground: #ffffff;
  --schema-speed-secondary: #f7f7fd;
  --schema-speed-secondary-foreground: #1a1a2e;
  --schema-speed-background: #ffffff;
  --schema-speed-foreground: #1a1a2e;
  --schema-speed-border: #ececfb;
  --schema-speed-muted: #7a7ab3;
  --schema-speed-muted-foreground: #7a7ab3;
  --schema-speed-destructive: #e4572e;
  --schema-speed-chart-2: #3ecf8e;
  --schema-speed-chart-4: #f7b32b;
  --schema-speed-shadow-lg: 0 1px 1px 0 hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10);
  --schema-speed-radius: 0.5rem;
}

/* Main Capture Overlay */
#schema-speed-capture-overlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  background: rgba(0, 0, 0, 0.1) !important;
  z-index: 999999 !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  pointer-events: none !important;
  display: none !important;
}

/* Capture Toolbar */
.schema-speed-capture-toolbar {
  position: fixed !important;
  top: 20px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  background: var(--schema-speed-background) !important;
  border: 1px solid var(--schema-speed-border) !important;
  border-radius: var(--schema-speed-radius) !important;
  box-shadow: var(--schema-speed-shadow-lg) !important;
  padding: 16px !important;
  display: flex !important;
  align-items: center !important;
  gap: 16px !important;
  min-width: 600px !important;
  max-width: 90vw !important;
  pointer-events: auto !important;
  z-index: 1000000 !important;
}

.schema-speed-field-selector {
  display: flex !important;
  flex-direction: column !important;
  gap: 4px !important;
}

.schema-speed-field-selector label {
  font-size: 12px !important;
  font-weight: 500 !important;
  color: var(--schema-speed-muted-foreground) !important;
  margin: 0 !important;
}

#schema-speed-field-dropdown {
  padding: 8px 12px !important;
  border: 1px solid var(--schema-speed-border) !important;
  border-radius: 6px !important;
  background: var(--schema-speed-background) !important;
  color: var(--schema-speed-foreground) !important;
  font-size: 14px !important;
  min-width: 180px !important;
  cursor: pointer !important;
}

#schema-speed-field-dropdown:focus {
  outline: none !important;
  border-color: var(--schema-speed-primary) !important;
  box-shadow: 0 0 0 2px rgba(84, 19, 147, 0.2) !important;
}

.schema-speed-capture-modes {
  display: flex !important;
  gap: 8px !important;
}

.schema-speed-mode-btn {
  padding: 8px 12px !important;
  border: 1px solid var(--schema-speed-border) !important;
  border-radius: 6px !important;
  background: var(--schema-speed-secondary) !important;
  color: var(--schema-speed-secondary-foreground) !important;
  font-size: 13px !important;
  font-weight: 500 !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
}

.schema-speed-mode-btn:hover {
  background: var(--schema-speed-muted) !important;
}

.schema-speed-mode-btn.active {
  background: var(--schema-speed-primary) !important;
  color: var(--schema-speed-primary-foreground) !important;
  border-color: var(--schema-speed-primary) !important;
}

.schema-speed-actions {
  display: flex !important;
  gap: 8px !important;
}

.schema-speed-actions button {
  padding: 8px 12px !important;
  border: 1px solid var(--schema-speed-border) !important;
  border-radius: 6px !important;
  background: var(--schema-speed-background) !important;
  color: var(--schema-speed-foreground) !important;
  font-size: 13px !important;
  font-weight: 500 !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
}

.schema-speed-actions button:hover {
  background: var(--schema-speed-secondary) !important;
}

#schema-speed-close-capture {
  background: var(--schema-speed-destructive) !important;
  color: var(--schema-speed-primary-foreground) !important;
  border-color: var(--schema-speed-destructive) !important;
}

#schema-speed-save-profile {
  background: var(--schema-speed-chart-2) !important;
  color: var(--schema-speed-primary-foreground) !important;
  border-color: var(--schema-speed-chart-2) !important;
}

/* Selection Info Panel */
.schema-speed-selection-info {
  position: fixed !important;
  bottom: 20px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  background: var(--schema-speed-background) !important;
  border: 1px solid var(--schema-speed-border) !important;
  border-radius: var(--schema-speed-radius) !important;
  box-shadow: var(--schema-speed-shadow-lg) !important;
  padding: 20px !important;
  min-width: 500px !important;
  max-width: 90vw !important;
  pointer-events: auto !important;
  z-index: 1000000 !important;
}

.schema-speed-selector-cycling {
  display: flex !important;
  align-items: center !important;
  gap: 12px !important;
  margin-bottom: 16px !important;
  padding: 12px !important;
  background: var(--schema-speed-secondary) !important;
  border-radius: 6px !important;
}

#schema-speed-prev-selector,
#schema-speed-next-selector {
  padding: 6px 10px !important;
  border: 1px solid var(--schema-speed-border) !important;
  border-radius: 4px !important;
  background: var(--schema-speed-background) !important;
  color: var(--schema-speed-foreground) !important;
  font-size: 14px !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
}

#schema-speed-prev-selector:hover,
#schema-speed-next-selector:hover {
  background: var(--schema-speed-muted) !important;
}

#schema-speed-selector-display {
  flex: 1 !important;
  font-family: monospace !important;
  font-size: 13px !important;
  color: var(--schema-speed-foreground) !important;
  background: var(--schema-speed-background) !important;
  padding: 8px 12px !important;
  border-radius: 4px !important;
  border: 1px solid var(--schema-speed-border) !important;
}

.schema-speed-preview-content {
  margin-bottom: 16px !important;
  padding: 12px !important;
  background: var(--schema-speed-secondary) !important;
  border-radius: 6px !important;
  font-size: 13px !important;
}

.schema-speed-preview-stats {
  font-weight: 600 !important;
  color: var(--schema-speed-primary) !important;
  margin-bottom: 8px !important;
}

.schema-speed-preview-value,
.schema-speed-preview-url,
.schema-speed-preview-alt {
  margin-bottom: 4px !important;
  word-break: break-all !important;
}

.schema-speed-preview-value strong,
.schema-speed-preview-url strong,
.schema-speed-preview-alt strong {
  color: var(--schema-speed-foreground) !important;
}

.schema-speed-transformation-options {
  margin-bottom: 16px !important;
}

#schema-speed-transformation-select {
  width: 100% !important;
  padding: 8px 12px !important;
  border: 1px solid var(--schema-speed-border) !important;
  border-radius: 6px !important;
  background: var(--schema-speed-background) !important;
  color: var(--schema-speed-foreground) !important;
  font-size: 14px !important;
}

#schema-speed-transformation-select:focus {
  outline: none !important;
  border-color: var(--schema-speed-primary) !important;
  box-shadow: 0 0 0 2px rgba(84, 19, 147, 0.2) !important;
}

.schema-speed-capture-actions {
  display: flex !important;
  gap: 12px !important;
  justify-content: flex-end !important;
}

#schema-speed-confirm-capture {
  padding: 10px 20px !important;
  border: none !important;
  border-radius: 6px !important;
  background: var(--schema-speed-primary) !important;
  color: var(--schema-speed-primary-foreground) !important;
  font-size: 14px !important;
  font-weight: 600 !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
}

#schema-speed-confirm-capture:hover {
  opacity: 0.9 !important;
  transform: translateY(-1px) !important;
}

#schema-speed-cancel-capture {
  padding: 10px 20px !important;
  border: 1px solid var(--schema-speed-border) !important;
  border-radius: 6px !important;
  background: var(--schema-speed-background) !important;
  color: var(--schema-speed-foreground) !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
}

#schema-speed-cancel-capture:hover {
  background: var(--schema-speed-secondary) !important;
}

/* Instruction Text */
.schema-speed-instruction-text {
  margin-top: 8px !important;
  font-size: 13px !important;
  opacity: 0.9 !important;
  color: inherit !important;
}

/* Element Highlighting */
.schema-speed-highlighted {
  outline: 2px solid var(--schema-speed-primary) !important;
  outline-offset: 2px !important;
  background-color: rgba(84, 19, 147, 0.1) !important;
}

/* Success Notification */
.schema-speed-capture-success {
  position: fixed !important;
  top: 20px !important;
  right: 20px !important;
  background: var(--schema-speed-chart-2) !important;
  color: white !important;
  padding: 12px 16px !important;
  border-radius: 6px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  z-index: 1000001 !important;
  box-shadow: var(--schema-speed-shadow-lg) !important;
  animation: schema-speed-slide-in 0.3s ease-out !important;
}

@keyframes schema-speed-slide-in {
  from {
    transform: translateX(100%) !important;
    opacity: 0 !important;
  }
  to {
    transform: translateX(0) !important;
    opacity: 1 !important;
  }
}

/* Error States */
.schema-speed-error {
  background: var(--schema-speed-destructive) !important;
  color: var(--schema-speed-primary-foreground) !important;
}

.schema-speed-warning {
  background: var(--schema-speed-chart-4) !important;
  color: var(--schema-speed-primary-foreground) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .schema-speed-capture-toolbar {
    min-width: auto !important;
    max-width: 95vw !important;
    flex-direction: column !important;
    gap: 12px !important;
    padding: 12px !important;
  }
  
  .schema-speed-field-selector {
    width: 100% !important;
  }
  
  #schema-speed-field-dropdown {
    min-width: auto !important;
    width: 100% !important;
  }
  
  .schema-speed-capture-modes {
    width: 100% !important;
    justify-content: center !important;
  }
  
  .schema-speed-actions {
    width: 100% !important;
    justify-content: center !important;
  }
  
  .schema-speed-selection-info {
    min-width: auto !important;
    max-width: 95vw !important;
    padding: 16px !important;
  }
  
  .schema-speed-selector-cycling {
    flex-direction: column !important;
    gap: 8px !important;
  }
  
  #schema-speed-selector-display {
    width: 100% !important;
    text-align: center !important;
  }
  
  .schema-speed-capture-actions {
    flex-direction: column !important;
    gap: 8px !important;
  }
  
  #schema-speed-confirm-capture,
  #schema-speed-cancel-capture {
    width: 100% !important;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  :root {
    --schema-speed-background: #2a2a40;
    --schema-speed-foreground: #f7f7fd;
    --schema-speed-secondary: #3a3a55;
    --schema-speed-secondary-foreground: #ffffff;
    --schema-speed-border: #4a4a65;
    --schema-speed-muted-foreground: #a0a0c0;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .schema-speed-capture-toolbar,
  .schema-speed-selection-info {
    border-width: 2px !important;
  }
  
  .schema-speed-mode-btn,
  .schema-speed-actions button,
  #schema-speed-field-dropdown,
  #schema-speed-transformation-select {
    border-width: 2px !important;
  }
  
  .schema-speed-highlighted {
    outline-width: 3px !important;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  .schema-speed-capture-success {
    animation: none !important;
  }
}

/* Focus Management */
.schema-speed-capture-toolbar:focus-within,
.schema-speed-selection-info:focus-within {
  outline: 2px solid var(--schema-speed-primary) !important;
  outline-offset: 2px !important;
}

/* Ensure overlay doesn't interfere with page content when hidden */
#schema-speed-capture-overlay[style*="display: none"] {
  pointer-events: none !important;
  visibility: hidden !important;
}

/* Prevent text selection in overlay */
.schema-speed-capture-toolbar,
.schema-speed-selection-info {
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
}

/* Allow text selection in preview content */
.schema-speed-preview-content,
#schema-speed-selector-display {
  user-select: text !important;
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
}

/* Ensure proper stacking context */
#schema-speed-capture-overlay * {
  position: relative !important;
  z-index: auto !important;
}

.schema-speed-capture-toolbar,
.schema-speed-selection-info {
  z-index: 1000000 !important;
}

/* Print styles - hide overlay when printing */
@media print {
  #schema-speed-capture-overlay {
    display: none !important;
  }
}
