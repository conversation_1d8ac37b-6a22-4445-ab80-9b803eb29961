# Product Context - Schema Speed Assistant

## Current Project Status

### What Works
- **Basic Form Field Population**
  - Fields can be populated with their label names
  - Works in the main page context
  - Basic event triggering for form frameworks
  - Simple field detection strategies

### What Doesn't Work
- **Sidebar System**: Not functioning
  - Injection mechanism failing
  - Event listeners broken
  - CSS styling issues
  - Tab functionality non-operational

- **Template System**: Not implemented
  - Cannot extract field structures
  - No template storage
  - No management interface
  - No builder compatibility

- **Content Capture**: Not working
  - Visual selection broken
  - No hover highlighting
  - Selector generation missing
  - Preview system absent

- **Profile Management**: Not available
  - No domain-specific mappings
  - No CRUD operations
  - No profile switching
  - No data persistence

- **JSON-LD Generation**: Not implemented
  - No schema generation
  - No validation system
  - No copy functionality
  - No preview interface

## Project Vision (To Be Implemented)

### The Schema Markup Problem
Schema markup is essential for SEO success, but creating it manually is:
- **Time-intensive**: 10-15 minutes per piece of content
- **Repetitive**: Same field types across similar content
- **Error-prone**: Manual data entry leads to mistakes
- **Unscalable**: Becomes bottleneck for agencies and content teams

### Market Gap
Existing solutions are either:
- **Platform-specific**: Locked to WordPress, Shopify, etc.
- **Template-limited**: Only work with specific schema types
- **Manual-dependent**: Still require significant human input
- **Complex**: Require technical knowledge to implement

## Planned Features

### Phase 1: Core Infrastructure (In Progress)
1. **Sidebar System**
   - Proper injection mechanism
   - Working event handling
   - Correct CSS styling
   - Tab functionality

2. **Template System**
   - Field structure extraction
   - Template storage
   - Management interface
   - Builder compatibility

3. **Content Capture**
   - Visual element selection
   - Hover highlighting
   - CSS selector generation
   - Content preview

### Phase 2: Advanced Features
1. **Profile Management**
   - Domain-specific mappings
   - CRUD operations
   - Profile switching
   - Data persistence

2. **JSON-LD Generation**
   - Schema generation
   - Validation system
   - Copy functionality
   - Preview interface

## Current User Experience

### Working Functionality
- **Form Field Population**
  - Fields can be filled with their label names
  - Basic event triggering works
  - Simple field detection available
  - Framework-agnostic approach

### Limitations
- **No Template System**
  - Manual field identification required
  - No reusable templates
  - No builder compatibility

- **No Content Capture**
  - Cannot select elements visually
  - No automated content extraction
  - No selector generation

- **No Profile Management**
  - Cannot save domain mappings
  - No reusable configurations
  - Manual setup each time

## Next Development Focus

### Priority 1: Sidebar System
- Fix injection mechanism
- Implement event handling
- Resolve CSS issues
- Add tab functionality

### Priority 2: Template System
- Build extraction logic
- Create storage system
- Add management interface
- Test with builders

### Priority 3: Content Capture
- Fix element selection
- Add visual feedback
- Implement selectors
- Create preview system

## Success Indicators (Not Yet Achieved)

### Current Metrics
- **Time Reduction**: Not measured (system incomplete)
- **Automation Rate**: Limited to form fill only
- **Setup ROI**: Not achieved (core features missing)
- **Error Reduction**: Not measured

### Development Goals
- **Core Functionality**: Restore all basic features
- **User Interface**: Fix sidebar and interaction
- **Automation**: Implement capture and fill
- **Data Management**: Add profile and template systems

## Project Status Summary

The Schema Speed Assistant is currently in early development with only basic form filling functionality operational. Most core systems and features need to be implemented or repaired. The focus is on restoring critical functionality, starting with the sidebar system and template extraction capabilities.

## Why This Project Exists

### The Schema Markup Problem
Schema markup is essential for SEO success, but creating it manually is:
- **Time-intensive**: 10-15 minutes per piece of content
- **Repetitive**: Same field types across similar content
- **Error-prone**: Manual data entry leads to mistakes
- **Unscalable**: Becomes bottleneck for agencies and content teams

### Market Gap
Existing solutions are either:
- **Platform-specific**: Locked to WordPress, Shopify, etc.
- **Template-limited**: Only work with specific schema types
- **Manual-dependent**: Still require significant human input
- **Complex**: Require technical knowledge to implement

## Problems It Solves

### For SEO Specialists
- **Client Scalability**: Manage schema for dozens of client websites efficiently
- **Consistency**: Ensure uniform schema implementation across domains
- **Time Management**: Reduce schema creation from hours to minutes per client
- **Quality Control**: Eliminate manual entry errors

### For Web Developers
- **Implementation Speed**: Deploy structured data at scale
- **Framework Compatibility**: Works with React, Vue, Angular, vanilla JS
- **Maintenance Reduction**: Automated updates when content structure changes
- **Technical Debt**: Avoid platform-specific schema solutions

### For Content Teams
- **Publishing Workflow**: Integrate schema creation into content publishing
- **Template Efficiency**: Reuse schema patterns across similar content
- **Quality Assurance**: Consistent schema markup without technical knowledge
- **Speed to Market**: Faster content publication with complete SEO optimization

### For Agencies
- **Client Onboarding**: Rapid schema implementation for new clients
- **Service Scalability**: Handle more clients without proportional staff increase
- **Competitive Advantage**: Offer comprehensive schema services efficiently
- **Profit Margins**: Reduce time investment while maintaining service quality

## How It Should Work

### User Experience Philosophy
**"Set it once, use it everywhere"** - The tool should require minimal setup but provide maximum automation for ongoing use.

### Core User Journey

#### Phase 1: Template Setup (5 minutes, once per schema type)
1. Visit preferred schema builder (UpTools, SEOPress, etc.)
2. Click "Extract Template" in extension sidebar
3. System automatically identifies all form fields
4. Universal template saved for reuse across any compatible builder

#### Phase 2: Domain Mapping (15 minutes, once per domain/content type)
1. Visit target website (Forbes, CNN, client site)
2. Click "Start Capture" in extension sidebar
3. Select field types from dropdown and click corresponding page elements
4. System learns CSS selectors and creates reusable domain profile
5. Save profile for future use on similar content

#### Phase 3: Daily Usage (30 seconds per schema)
1. Visit any article on mapped domain
2. Click "Start Capture" to collect content using saved profile
3. Return to schema builder
4. Click "Fill Fields" to automatically populate form
5. Review and submit schema markup

### Alternative Workflow: JSON Generation
For users who prefer direct code output:
1. Complete capture process as above
2. Click "Generate JSON-LD" instead of returning to builder
3. Copy clean, production-ready schema code
4. Paste directly into website or CMS

## User Experience Goals

### Simplicity
- **One-click operations**: Primary actions should require single click
- **Visual feedback**: Clear indicators of system state and progress
- **Error prevention**: Validate inputs and provide helpful guidance
- **Graceful degradation**: Fallback options when automation fails

### Efficiency
- **Minimal setup time**: Initial configuration under 20 minutes per domain
- **Fast execution**: Capture and fill operations under 60 seconds
- **Batch operations**: Handle multiple similar pages efficiently
- **Smart defaults**: Intelligent field matching and content transformation

### Reliability
- **Consistent results**: Same input should produce same output
- **Error recovery**: Clear error messages with suggested solutions
- **Data persistence**: Profiles and templates saved reliably
- **Cross-session continuity**: Work resumes after browser restart

### Flexibility
- **Universal compatibility**: Works with any schema builder
- **Content adaptation**: Handles various website structures
- **Customization options**: Override automated selections when needed
- **Export capabilities**: Share profiles and templates between users

## Success Indicators

### Quantitative Metrics
- **Time Reduction**: 90%+ reduction in schema creation time
- **Automation Rate**: 90-100% automated field population
- **Setup ROI**: Positive return after 3-5 schemas per domain
- **Error Reduction**: Significant decrease in schema markup errors

### Qualitative Outcomes
- **User Satisfaction**: Positive feedback on workflow efficiency
- **Adoption Rate**: Regular use after initial setup
- **Scalability**: Successful deployment across multiple domains
- **Competitive Advantage**: Users report business benefits

## Target Content Types

### Primary Use Cases
- **News Articles**: Consistent structure across publications
- **Blog Posts**: Standardized author, date, category information
- **Product Pages**: Repeating product attributes and pricing
- **Recipe Content**: Structured ingredient and instruction data
- **Video Content**: Consistent metadata and description patterns

### Secondary Use Cases
- **FAQ Pages**: Question and answer pairs
- **Person Profiles**: Biographical and contact information
- **Event Listings**: Date, location, and description data
- **Review Content**: Rating and review text patterns

### Content Characteristics
- **Templated Structure**: Consistent DOM patterns across similar content
- **Repeating Elements**: Same field types appear regularly
- **Accessible Content**: Information visible in page HTML
- **Stable Selectors**: CSS selectors remain consistent over time
