# Project Brief - Schema Speed Assistant

## Project Overview
Schema Speed Assistant is a Chrome extension that automates schema markup creation for SEO specialists and web developers. It serves as a universal form replacement plugin that eliminates repetitive manual work by automatically mapping and transferring data between web pages and schema builders.

## Core Problem Statement
SEO specialists and web developers spend 10-15 minutes manually creating schema markup for each piece of content. This repetitive process becomes a significant time sink when working with multiple client websites or publishing templated content at scale.

## Solution Approach
A three-phase workflow that transforms schema creation from manual work into automated workflow:

1. **Template Extraction**: One-time setup to extract field structures from any schema builder
2. **Domain Mapping**: One-time setup to map content selectors for each website
3. **Automated Population**: Daily usage with 30-second capture and fill workflow

## Target Users
- **Primary**: SEO specialists managing multiple client websites
- **Secondary**: Web developers implementing structured data at scale
- **Tertiary**: Content teams publishing templated content (news, recipes, products)
- **Enterprise**: Agencies with repetitive schema markup workflows

## Success Metrics
- **Time Reduction**: From 10-15 minutes to 30 seconds per schema after setup
- **ROI Achievement**: After processing 3-5 articles per domain
- **Automation Rate**: 90-100% automated field population
- **Universal Compatibility**: Works with any form-based schema builder

## Technical Constraints
- Chrome Manifest V3 extension architecture
- Local-first data storage (no external dependencies)
- Universal compatibility across schema builders
- Framework support (React, Vue, Angular, vanilla JS)

## Project Scope
**In Scope:**
- Universal template extraction from schema builders
- Visual content capture with hover highlighting
- Domain-specific profile management
- Automated form field population
- JSON-LD snippet generation alternative

**Out of Scope:**
- Complex schema with extensive node referencing
- One-time website setup schemas (Organization, LocalBusiness)
- Dynamic content that loads after page render
- API-sourced data integration

## Business Value
- Eliminates 90-100% of manual schema field population
- Scales efficiently across multiple websites
- Works universally without vendor lock-in
- Handles repetitive content types effectively

## Project Status
**Current State**: Production-ready Chrome extension with complete core functionality
**Next Steps**: Icon generation, testing, and Chrome Web Store preparation
