# Technical Context - <PERSON><PERSON><PERSON> Speed Assistant

## Current Technical State

### Working Components
- **Form Field Population**: Basic form field detection and filling
- **Chrome Extension Base**: Manifest V3 configuration
- **Background Script**: Centralized, robust storage management
- **Content Scripts**: Limited form interaction, always delegate storage to background
- **Sidebar System**: Now loads and renders last used template on open, with robust error handling and debug logs
- **Template/Profile/Session Storage**: Fully working, persistent, and production-grade

### Non-Working Components
- **Content Capture**: Visual selection system non-functional
- **JSON-LD Generation**: Schema generation not implemented

## Technologies Used

### Core Extension Technologies
- **Chrome Extension Manifest V3**: Modern extension setup
- **Vanilla JavaScript**: No external dependencies
- **HTML5 & CSS3**: Sidebar and UI styling
- **Chrome APIs**: Full usage of storage and scripting APIs

### Storage Architecture (Finalized)
- All persistent data (templates, profiles, sessions, UI state) is managed by the background script using chrome.storage.local and chrome.storage.sync
- Sidebar, popup, and content scripts always delegate storage operations to the background via chrome.runtime.sendMessage
- The sidebar loads and renders the last used template on open, with robust error handling and debug logs
- No direct use of localStorage in any content script or UI component
- All storage logic is asynchronous and centralized for reliability and maintainability

### Example Storage Workflow
```javascript
// Save a template
chrome.runtime.sendMessage({ action: 'saveTemplate', template: { ... } });

// Load all templates
chrome.runtime.sendMessage({ action: 'getTemplates' }, (response) => { ... });

// Load last used template on sidebar open
chrome.runtime.sendMessage({ action: 'getTemplates' }, (response) => {
  if (response.lastUsed) {
    // Load and render template
  }
});
```

## Development Setup

### Current Project Structure
```
schema-speed-assistant/
├── manifest.json              # Basic extension configuration
├── background.js             # Limited message handling
├── sidebar/                  # Non-functional sidebar
│   ├── sidebar.html         # Basic HTML structure
│   ├── sidebar.css          # Broken styling
│   └── sidebar.js           # Non-working controller
├── content-scripts/          # Limited functionality
│   ├── content-capture.js   # Not working
│   ├── form-field-system.js # Basic form fill only
│   └── sidebar-injector.js  # Injection failing
└── styles/                   # CSS issues
    └── content-overlay.css  # Not applying correctly
```

### Development Workflow
1. **Load Extension**: Chrome → Extensions → Developer Mode → Load Unpacked
2. **Testing**: Only form fill functionality can be tested
3. **Debugging**: Use Chrome DevTools for basic debugging

## Working Technical Patterns

### Form Field Detection
```javascript
function detectFieldLabel(inputElement) {
  let labelText = '';
  
  // Working detection strategies
  if (inputElement.id) {
    const label = document.querySelector(`label[for="${inputElement.id}"]`);
    if (label) labelText = label.textContent.trim();
  }
  
  if (!labelText && inputElement.placeholder) {
    labelText = inputElement.placeholder;
  }
  
  if (!labelText && inputElement.name) {
    labelText = inputElement.name;
  }
  
  return labelText;
}
```

### Form Population
```javascript
function fillField(field, value) {
  // Basic field population
  field.value = value;
  
  // Event triggering
  field.dispatchEvent(new Event('input', { bubbles: true }));
  field.dispatchEvent(new Event('change', { bubbles: true }));
}
```

## Technical Constraints

### Current Limitations
- **Sidebar Injection**: Cannot properly inject sidebar into pages
- **Event Handling**: Limited to basic form events
- **Storage Usage**: Basic initialization only
- **Cross-Frame Access**: No proper frame communication
- **CSS Scoping**: Styling conflicts and application issues

### Browser Support
- **Current Target**: Chrome only
- **Testing Status**: Limited to form fill testing
- **Framework Support**: Basic event handling only

## Dependencies

### Current Dependencies
- **None**: Zero external dependencies
- **Browser APIs**: Limited to basic Chrome extension APIs

### Development Tools
- **Chrome DevTools**: Basic debugging
- **Manual Testing**: Form fill verification

## Data Storage

### Current Implementation
- All persistent data is managed by the background script using chrome.storage.local and chrome.storage.sync
- No direct use of localStorage in any content script or UI component
- All storage logic is asynchronous and centralized

## Security Considerations

### Current Permissions
```json
{
  "permissions": [
    "storage",
    "activeTab",
    "scripting"
  ],
  "host_permissions": [
    "<all_urls>"
  ]
}
```

### Security Status
- **Permission Usage**: Limited to form fill operations
- **Data Handling**: Basic form data only
- **Content Security**: Standard extension isolation

## Framework Support

### Current Support
- Basic event triggering for form frameworks
- Limited cross-framework compatibility
- No specific framework optimizations

### Planned Support (Not Implemented)
- React synthetic events
- Vue reactivity system
- Angular change detection
- Framework-specific event handling

## Next Technical Steps

### Priority 1: Expand Template/Profile Management
1. Add advanced CRUD features
2. Improve UI/UX for template/profile selection

### Priority 2: Restore Content Capture
1. Fix element selection
2. Implement visual feedback
3. Test selector generation

## Technical Status Summary

The Schema Speed Assistant is currently in early development with only basic form filling functionality operational. Most core systems need implementation or repair. The focus is on restoring critical functionality, starting with the sidebar system and template extraction.
