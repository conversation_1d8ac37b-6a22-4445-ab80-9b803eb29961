# System Patterns - <PERSON><PERSON><PERSON> Speed Assistant

## Current System Architecture

### Chrome Extension Architecture (Manifest V3)
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Sidebar UI    │    │  Background     │    │ Content Scripts │
│                 │◄──►│  Service Worker │◄──►│  (form fill)    │
│                 │    │  (background.js)│    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         ╳                       │                       │
         ╳                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Chrome Storage  │    │ Message Router  │    │   DOM Access    │
│ (robust, local) │    │ (full storage)  │    │  (form fill)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Storage Architecture (Finalized)
- **All persistent data** (templates, profiles, sessions, UI state) is managed by the background script.
- **chrome.storage.local** is used for large, persistent, local-first data (templates, sessions, UI state).
- **chrome.storage.sync** is used for user settings and optionally for profiles (to sync across devices).
- **Sidebar, popup, and content scripts** always delegate storage operations to the background script via messaging (chrome.runtime.sendMessage).
- **The sidebar loads and renders the last used template on open**, with robust error handling and debug logs for all storage operations.
- **No direct use of localStorage** in any content script or UI component.
- **All storage logic is asynchronous and centralized** for reliability and maintainability.

### Storage Workflow
```mermaid
graph LR
  A[Sidebar/Popup/UI] -- Request data --> B(Background Script)
  B -- Fetch from storage --> C[chrome.storage.local/sync]
  C -- Return data --> B
  B -- Send response --> A
  D[Content Script] -- Storage change --> E[chrome.storage.onChanged]
  E -- Update UI --> D
```

### Example Storage Operations
```javascript
// Save a template
chrome.runtime.sendMessage({ action: 'saveTemplate', template: { ... } });

// Load all templates
chrome.runtime.sendMessage({ action: 'getTemplates' }, (response) => { ... });

// Load last used template on sidebar open
chrome.runtime.sendMessage({ action: 'getTemplates' }, (response) => {
  if (response.lastUsed) {
    // Load and render template
  }
});
```

### Key Patterns
- **Centralized Storage Management**: All data flows through the background script
- **Async, Non-blocking**: All storage operations are asynchronous
- **Robust Error Handling**: All storage operations have debug logs and user notifications for errors
- **Single Source of Truth**: No duplicate or conflicting storage logic

### Current Component Status

#### Background Service Worker (`background.js`)
- **Role**: Basic message handling and form fill coordination
- **Current State**: Partially functional
- **Working Features**:
  - Form field population
  - Basic message routing
- **Non-working Features**:
  - Template extraction
  - Profile management
  - Cross-tab communication

#### Content Scripts
- **content-capture.js**: Not functioning
- **form-field-system.js**: Basic form field population working
- **sidebar-injector.js**: Injection failing
- **Current State**: Only form fill functionality operational

#### Sidebar Interface (`sidebar/`)
- **Current State**: Not functioning
- **Issues**:
  - Injection mechanism failing
  - Event listeners not working
  - CSS styling problems
  - Tab switching broken

## Working Technical Patterns

### Form Field Detection Pattern
```javascript
function detectFieldLabel(inputElement) {
  let labelText = '';
  
  // Strategy 1: Associated label by ID
  if (inputElement.id) {
    const associatedLabel = document.querySelector(`label[for="${inputElement.id}"]`);
    if (associatedLabel) {
      labelText = cleanLabelText(associatedLabel.textContent);
    }
  }
  
  // Strategy 2: Nearest label in DOM structure
  if (!labelText) {
    const nearbyLabel = inputElement.closest('div,fieldset,section')?.querySelector(
      'label, .form-label, .control-label, .field-label'
    );
    if (nearbyLabel) {
      labelText = cleanLabelText(nearbyLabel.textContent);
    }
  }
  
  // Strategy 3: Previous sibling element
  if (!labelText) {
    const prevElement = inputElement.previousElementSibling;
    if (prevElement && isLabelElement(prevElement)) {
      labelText = cleanLabelText(prevElement.textContent);
    }
  }
  
  // Strategy 4: Placeholder text fallback
  if (!labelText && inputElement.placeholder) {
    labelText = inputElement.placeholder.trim();
  }
  
  // Strategy 5: Name attribute fallback
  if (!labelText && inputElement.name) {
    labelText = capitalizeFirstLetter(inputElement.name);
  }
  
  return labelText;
}
```

### Form Population Pattern
```javascript
function fillField(field, value) {
  if (field.tagName.toLowerCase() === 'select') {
    const option = Array.from(field.options).find(opt => 
      opt.value === value || opt.textContent.trim() === value
    );
    if (option) {
      field.value = option.value;
    }
  } else {
    field.value = value;
  }
  
  // Trigger events to notify form frameworks
  field.dispatchEvent(new Event('input', { bubbles: true }));
  field.dispatchEvent(new Event('change', { bubbles: true }));
  field.dispatchEvent(new Event('blur', { bubbles: true }));
}
```

## Patterns To Be Implemented

### Template System (Not Working)
- Field structure extraction
- Template storage
- Template management
- Builder compatibility

### Content Capture System (Not Working)
- Visual element selection
- Hover highlighting
- CSS selector generation
- Content preview

### Profile Management (Not Working)
- Domain-specific storage
- CRUD operations
- Profile switching
- Data mapping

### JSON-LD Generation (Not Working)
- Schema generation
- Validation system
- Copy functionality
- Preview interface

## Current Component Relationships

### Limited Data Flow
```
User Action → Background Script → Content Script → Form Fill
                    │                    ↓
                    └──────── Basic Response
```

### Storage Patterns (Limited)
```javascript
// Basic storage operations
chrome.storage.local.set({
  pageToSchemaData: {
    version: "1.0.0",
    templates: {},
    profiles: {},
    sessions: {},
    settings: {
      defaultProfile: "news",
      autoDetectProfileType: true,
      urlBasedDefaults: {},
      transformationSettings: {
        dateFormat: "ISO8601",
        textCleaning: true
      }
    }
  }
});
```

## Framework Support

### Current Form Fill Support
- Basic event triggering
- Simple value setting
- Framework-agnostic approach

### Planned Framework Integration (Not Implemented)
- React synthetic events
- Vue reactivity system
- Angular change detection
- Framework-specific optimizations

## Next Development Focus

### Priority 1: Sidebar System
- Fix injection mechanism
- Restore event handling
- Repair tab functionality
- Resolve CSS issues

### Priority 2: Template System
- Implement field detection
- Create storage system
- Build management interface
- Test with builders

### Priority 3: Content Capture
- Fix element selection
- Add visual feedback
- Implement selectors
- Create preview system

## System Status Summary

The Schema Speed Assistant is currently in early development with only basic form filling functionality operational. Most core systems and patterns need to be implemented or repaired. The focus is on restoring critical functionality starting with the sidebar system.
