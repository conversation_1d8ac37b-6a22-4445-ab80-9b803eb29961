<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Schema Speed Assistant - Sidebar</title>
    <link rel="stylesheet" href="sidebar.css">
</head>
<body class="dark">
    <div id="schema-speed-sidebar-container">
        <header class="schema-speed-sidebar-header">
            <div class="schema-speed-logo-section">
                <h1 style="color: #fff; font-size: 24px;">⚡ Schema Speed</h1>
                <span class="schema-speed-version" id="sidebar-version"></span>
            </div>
            
            <div class="schema-speed-header-controls">
                <div class="schema-speed-status-indicator" id="schema-speed-status-indicator">
                    <span class="schema-speed-status-dot"></span>
                    <span class="schema-speed-status-text">Ready</span>
                </div>
                
                <div class="schema-speed-control-buttons">
                    <button id="schema-speed-dock-toggle" class="schema-speed-control-btn" title="Dock Sidebar">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M16 3.93a.75.75 0 0 1 1.177-.617l4.432 3.069a.75.75 0 0 1 0 1.233l-4.432 3.069A.75.75 0 0 1 16 10.067V8H4a1 1 0 0 1 0-2h12V3.93zm-9.177 9.383A.75.75 0 0 1 8 13.93V16h12a1 1 0 1 1 0 2H8v2.067a.75.75 0 0 1-1.177.617l-4.432-3.069a.75.75 0 0 1 0-1.233l4.432-3.069z" fill="currentColor"/>
                        </svg>
                    </button>
                    <button id="reloadBtn" class="schema-speed-control-btn" title="Reload Extension" aria-label="Reload Extension">
                        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 4V1L7 6l5 5V7c3.31 0 6 2.69 6 6 0 3.31-2.69 6-6 6s-6-2.69-6-6H4c0 4.42 3.58 8 8 8s8-3.58 8-8-3.58-8-8-8z" fill="currentColor"/>
                        </svg>
                    </button>
                    <button id="theme-toggle-btn" class="schema-speed-control-btn" title="Toggle Theme" aria-label="Toggle theme">
                        <span class="schema-speed-theme-icon">🌙</span>
                    </button>
                    <button id="schema-speed-close-sidebar" class="schema-speed-control-btn" title="Close Sidebar">
                        <span class="schema-speed-close-icon">✕</span>
                    </button>
                </div>
            </div>
        </header>

        <nav class="schema-speed-sidebar-nav">
            <button class="schema-speed-nav-btn active" data-tab="prepare">Prepare Template</button>
            <button class="schema-speed-nav-btn" data-tab="scrape">Scrape Page</button>
            <button class="schema-speed-nav-btn" data-tab="fill">Fill out Template</button>
        </nav>

        <main class="schema-speed-sidebar-content">
            <!-- Prepare Template Tab -->
            <div id="schema-speed-prepare-tab" class="schema-speed-tab-content active">
                <div class="schema-speed-tab-header">
                    <h2>Step 1: Prepare Template</h2>
                    <p class="schema-speed-tab-description">
                        Extract a reusable template from your schema builder by filling form fields with their label names. This creates a universal template that works across any website.
                    </p>
                </div>

                <div class="schema-speed-current-site-info">
                    <h4>Current Site: <span id="schema-speed-current-domain">Loading...</span></h4>
                    <div class="schema-speed-quick-stats">
                        <div class="schema-speed-stat-item">
                            <span class="schema-speed-stat-value" id="schema-speed-profiles-count">0</span>
                            <span class="schema-speed-stat-label">Profiles</span>
                        </div>
                        <div class="schema-speed-stat-item">
                            <span class="schema-speed-stat-value" id="schema-speed-templates-count">0</span>
                            <span class="schema-speed-stat-label">Templates</span>
                        </div>
                    </div>
                </div>

                <div class="schema-speed-prepare-actions">
                    <div class="schema-speed-action-group">
                        <h4>Form Field Preparation</h4>
                        <p>Fill form fields with their label names to identify the form structure:</p>
                        <div class="schema-speed-action-buttons">
                            <button id="schema-speed-fill-fields" class="schema-speed-action-btn primary">
                                Fill Form Fields
                            </button>
                            <button id="schema-speed-clear-fields" class="schema-speed-action-btn secondary">
                                Clear Fields
                            </button>
                        </div>
                    </div>

                    <div class="schema-speed-action-group">
                        <h4>Template Extraction</h4>
                        <p>Paste your schema template and save it for reuse:</p>
                        <div class="schema-speed-template-form">
                            <div class="schema-speed-form-group">
                                <label for="schema-speed-template-name">Template Name:</label>
                                <input type="text" id="schema-speed-template-name" placeholder="e.g., Article Schema">
                            </div>
                            <div class="schema-speed-form-group">
                                <label for="schema-speed-template-type">Template Type:</label>
                                <select id="schema-speed-template-type">
                                    <option value="article">Article</option>
                                    <option value="product">Product</option>
                                    <option value="organization">Organization</option>
                                    <option value="local-business">Local Business</option>
                                    <option value="custom">Custom</option>
                                </select>
                            </div>
                            <div class="schema-speed-form-group">
                                <label for="schema-speed-template-content">Schema Template:</label>
                                <textarea id="schema-speed-template-content" class="schema-speed-template-textarea" placeholder="Paste your schema template here..." rows="10"></textarea>
                            </div>
                            <div id="schema-speed-fields-list"></div>
                        </div>
                        <div class="schema-speed-action-buttons">
                            <button id="schema-speed-save-template" class="schema-speed-action-btn primary">Save Template</button>
                            <!-- Add dropdown for saved templates below the Save button -->
                            <div class="schema-speed-templates-dropdown-container" style="margin-top: 16px;">
                              <label for="schema-speed-templates-dropdown" style="display: block; margin-bottom: 4px;">Load Saved Template:</label>
                              <select id="schema-speed-templates-dropdown" class="schema-speed-templates-dropdown" style="width: 100%; padding: 6px;">
                                <option value="">Select a saved template...</option>
                              </select>
                            </div>
                            <!-- New: Saved Templates List with Delete Buttons -->
                            <div id="schema-speed-templates-list" class="schema-speed-templates-list" style="margin-top: 12px;"></div>
                        </div>
                    </div>

                    <div class="schema-speed-extracted-fields" id="schema-speed-extracted-fields" style="display: none;">
                        <h4>Extracted Fields</h4>
                        <div id="schema-speed-fields-preview">
                            <!-- Extracted fields will be shown here -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Scrape Page Tab -->
            <div id="schema-speed-scrape-tab" class="schema-speed-tab-content">
                <div class="schema-speed-tab-header">
                    <h2>Step 2: Scrape Page</h2>
                    <p class="schema-speed-tab-description">
                        Capture content from web pages using visual selection. Map page elements to your template fields for automatic data extraction.
                    </p>
                </div>

                <div class="schema-speed-profile-selection">
                    <h4>Profile Selection</h4>
                    <div class="schema-speed-form-group">
                        <label for="schema-speed-profile-select">Select Profile:</label>
                        <select id="schema-speed-profile-select">
                            <option value="">Choose existing profile...</option>
                        </select>
                        <button id="schema-speed-create-new-profile" class="schema-speed-link-btn">+ Create New</button>
                    </div>
                </div>

                <div class="schema-speed-capture-progress" id="schema-speed-capture-progress" style="display: none;">
                    <div class="schema-speed-progress-header">
                        <h4>Capture Progress</h4>
                        <span class="schema-speed-progress-counter">
                            <span id="schema-speed-completed-fields">0</span>/<span id="schema-speed-total-fields">0</span>
                        </span>
                    </div>
                    
                    <div class="schema-speed-fields-list" id="schema-speed-fields-list">
                        <!-- Fields will be populated here -->
                    </div>
                </div>

                <div class="schema-speed-capture-actions">
                    <h4>Content Capture</h4>
                    <div class="schema-speed-action-buttons">
                        <button id="schema-speed-start-manual-capture" class="schema-speed-action-btn primary">Start Manual Capture</button>
                        <button id="schema-speed-auto-detect-fields" class="schema-speed-action-btn secondary">Auto-Detect Fields</button>
                        <button id="schema-speed-save-capture-session" class="schema-speed-action-btn secondary" disabled>Save Session</button>
                    </div>
                </div>

                <div class="schema-speed-action-group">
                    <h4>Field Mapping</h4>
                    <p>Select elements on the page to map to your schema fields:</p>
                    
                    <div class="schema-speed-fields-list">
                        <!-- Fields will be populated here -->
                    </div>

                    <div class="schema-speed-action-buttons">
                        <button id="schema-speed-save-mapping" class="schema-speed-action-btn primary">Save Mapping</button>
                        <button id="schema-speed-test-mapping" class="schema-speed-action-btn secondary">Test Mapping</button>
                    </div>
                </div>
            </div>

            <!-- Fill out Template Tab -->
            <div id="schema-speed-fill-tab" class="schema-speed-tab-content">
                <div class="schema-speed-tab-header">
                    <h2>Step 3: Fill out Template</h2>
                    <p class="schema-speed-tab-description">
                        Use captured content to automatically fill your schema builder forms or generate clean JSON-LD code for your website.
                    </p>
                </div>

                <div class="schema-speed-session-data" id="schema-speed-session-data">
                    <h4>Captured Data</h4>
                    <div id="schema-speed-captured-data-preview">
                        <p class="schema-speed-empty-state">No data captured yet. Go to Step 2 to capture content.</p>
                    </div>
                </div>

                <div class="schema-speed-output-options">
                    <h4>Output Options</h4>
                    <div class="schema-speed-output-mode-toggle">
                        <label class="schema-speed-toggle-label">
                            <input type="radio" name="output-mode" value="fill-builder" checked>
                            Fill Builder
                        </label>
                        <label class="schema-speed-toggle-label">
                            <input type="radio" name="output-mode" value="json-snippet">
                            JSON Snippet
                        </label>
                    </div>

                    <div class="schema-speed-fill-builder-options" id="schema-speed-fill-builder-options">
                        <p>Switch to your schema builder tab and click "Fill Builder Fields" to populate your form.</p>
                        <div class="schema-speed-action-buttons">
                            <button id="schema-speed-fill-builder-fields" class="schema-speed-action-btn primary">Fill Builder Fields</button>
                        </div>
                    </div>

                    <div class="schema-speed-json-snippet-options" id="schema-speed-json-snippet-options" style="display: none;">
                        <div class="schema-speed-snippet-settings">
                            <div class="schema-speed-form-group">
                                <label for="schema-speed-schema-type">Schema Type:</label>
                                <select id="schema-speed-schema-type">
                                    <option value="NewsArticle">News Article</option>
                                    <option value="Recipe">Recipe</option>
                                    <option value="Person">Person</option>
                                    <option value="LocalBusiness">Local Business</option>
                                </select>
                            </div>
                            
                            <div class="schema-speed-form-group">
                                <label for="schema-speed-format-type">Format:</label>
                                <select id="schema-speed-format-type">
                                    <option value="json-ld">JSON-LD</option>
                                    <option value="microdata">Microdata</option>
                                </select>
                            </div>
                        </div>

                        <div class="schema-speed-action-buttons">
                            <button id="schema-speed-generate-snippet" class="schema-speed-action-btn primary">Generate Snippet</button>
                        </div>
                    </div>
                </div>

                <div class="schema-speed-output-result" id="schema-speed-output-result" style="display: none;">
                    <div class="schema-speed-result-header">
                        <h4>Generated Code</h4>
                        <button id="schema-speed-copy-output" class="schema-speed-link-btn">Copy</button>
                    </div>
                    <pre id="schema-speed-output-code" class="schema-speed-code-block"></pre>
                </div>
            </div>
        </main>

        <footer class="schema-speed-sidebar-footer">
            <div class="schema-speed-footer-links">
                <button id="schema-speed-settings-btn" class="schema-speed-footer-btn">Settings</button>
                <button id="schema-speed-help-btn" class="schema-speed-footer-btn">Help</button>
            </div>
        </footer>
    </div>

    <script src="../settings/extension-reload-utility.js"></script>
    <script src="sidebar.js"></script>
</body>
</html>

