// Schema Speed Assistant - Sidebar Controller
// Main interface controller for the sidebar when injected into content pages

class SidebarController {
  constructor() {
    this.currentTab = 'prepare';
    this.isDocked = false;
    this.dockPosition = 'right';
    this.currentDomain = '';
    this.isInitialized = false;
    this.profileManager = new ProfileManager();
    this.sessionManager = new SessionManager();
    this.templateManager = new TemplateManager();
    this.ssFieldValues = {}; // { pathString: value }
    this.ssFieldCaptureActive = null; // pathString of field being captured
  }

  async initialize() {
    try {
      // Initialize managers
      await this.profileManager.initialize();
      await this.sessionManager.initialize();
      await this.templateManager.initialize();

      // Get current domain
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tabs[0]?.url) {
        const url = new URL(tabs[0].url);
        this.currentDomain = url.hostname;
        document.getElementById('schema-speed-current-domain').textContent = this.currentDomain;
      }

      // Setup UI and systems
      this.setupEventListeners();
      this.setupTabNavigation();
      this.setupDockingSystem();
      await this.loadDockState();
      await this.initializeTheme();
      
      // Initial refresh
      await this.loadCurrentTabInfo();
      await this.refreshDashboard();
      await this.loadSavedTemplates();
      // After loading templates, load the last used template into the textarea, pretty-printed
      const lastUsed = await this.templateManager.getLastUsedTemplate();
      if (lastUsed) {
        await this.useTemplate(lastUsed);
      }
      this.updateStatus('Ready');
      this.isInitialized = true;

      // Set up template content paste handling
      const templateContent = document.getElementById('schema-speed-template-content');
      if (templateContent) {
        templateContent.addEventListener('paste', (e) => {
          e.preventDefault();
          const text = e.clipboardData.getData('text/plain');
          
          // Clean the pasted content
          let cleanContent = text;
          if (cleanContent.includes('<script')) {
            cleanContent = cleanContent.replace(/<script[^>]*>|<\/script>/gi, '');
          }
          
          // Insert at cursor position
          const start = templateContent.selectionStart;
          const end = templateContent.selectionEnd;
          templateContent.value = templateContent.value.substring(0, start) + 
                                cleanContent + 
                                templateContent.value.substring(end);
          
          // Move cursor after pasted content
          templateContent.selectionStart = start + cleanContent.length;
          templateContent.selectionEnd = start + cleanContent.length;
        });
        templateContent.addEventListener('input', () => this.handleTemplateContentChange());
      }
    } catch (error) {
      console.error('Error initializing sidebar:', error);
      this.showNotification('Error initializing sidebar', 'error');
    }
  }

  async loadCurrentTabInfo() {
    try {
      // Show the full URL of the current page
      this.currentDomain = window.location.href;
      
      const domainElement = document.getElementById('schema-speed-current-domain');
      if (domainElement) {
        domainElement.textContent = this.currentDomain;
      }
    } catch (error) {
      console.error('Error loading current tab info:', error);
      this.currentDomain = 'Unknown URL';
      
      const domainElement = document.getElementById('schema-speed-current-domain');
      if (domainElement) {
        domainElement.textContent = this.currentDomain;
      }
    }
  }

  setupEventListeners() {
    // Header controls
    const themeToggle = document.getElementById('theme-toggle-btn');
    if (themeToggle) {
      themeToggle.addEventListener('click', () => {
        this.handleThemeToggle();
      });
    }

    const closeSidebar = document.getElementById('schema-speed-close-sidebar');
    if (closeSidebar) {
      closeSidebar.addEventListener('click', () => {
        this.handleCloseSidebar();
      });
    }

    // Dock toggle
    const dockToggle = document.getElementById('schema-speed-dock-toggle');
    if (dockToggle) {
      dockToggle.addEventListener('click', () => {
        this.handleDockToggle();
      });
    }

    // Reload extension
    const reloadExtension = document.getElementById('reloadBtn');
    if (reloadExtension) {
      reloadExtension.addEventListener('click', () => {
        this.handleReloadExtension();
      });
    }

    // Save Template button
    const saveTemplate = document.getElementById('schema-speed-save-template');
    if (saveTemplate) {
      saveTemplate.addEventListener('click', () => {
        this.handleSaveTemplate();
      });
    }

    // Prepare Template tab events
    const fillFields = document.getElementById('schema-speed-fill-fields');
    if (fillFields) {
      fillFields.addEventListener('click', () => {
        this.handleFillFieldsExtension();
      });
    }

    const clearFields = document.getElementById('schema-speed-clear-fields');
    if (clearFields) {
      clearFields.addEventListener('click', () => {
        this.handleClearFields();
      });
    }

    const extractTemplate = document.getElementById('schema-speed-extract-template');
    if (extractTemplate) {
      extractTemplate.addEventListener('click', () => {
        this.handleExtractTemplateTab();
      });
    }

    const previewTemplate = document.getElementById('schema-speed-preview-template');
    if (previewTemplate) {
      previewTemplate.addEventListener('click', () => {
        this.handlePreviewTemplate();
      });
    }

    // Scrape Page tab events
    const startManualCapture = document.getElementById('schema-speed-start-manual-capture');
    if (startManualCapture) {
      startManualCapture.addEventListener('click', () => {
        this.handleStartManualCapture();
      });
    }

    const autoDetectFields = document.getElementById('schema-speed-auto-detect-fields');
    if (autoDetectFields) {
      autoDetectFields.addEventListener('click', () => {
        this.handleAutoDetectFields();
      });
    }

    const saveCaptureSession = document.getElementById('schema-speed-save-capture-session');
    if (saveCaptureSession) {
      saveCaptureSession.addEventListener('click', () => {
        this.handleSaveCaptureSession();
      });
    }

    const createNewProfile = document.getElementById('schema-speed-create-new-profile');
    if (createNewProfile) {
      createNewProfile.addEventListener('click', () => {
        this.handleCreateNewProfile();
      });
    }

    // Fill out Template tab events
    const fillBuilderFields = document.getElementById('schema-speed-fill-builder-fields');
    if (fillBuilderFields) {
      fillBuilderFields.addEventListener('click', () => {
        this.handleFillBuilderFields();
      });
    }

    const generateSnippet = document.getElementById('schema-speed-generate-snippet');
    if (generateSnippet) {
      generateSnippet.addEventListener('click', () => {
        this.handleGenerateSnippet();
      });
    }

    const copyOutput = document.getElementById('schema-speed-copy-output');
    if (copyOutput) {
      copyOutput.addEventListener('click', () => {
        this.handleCopyOutput();
      });
    }

    // Output mode toggle
    document.querySelectorAll('input[name="output-mode"]').forEach(radio => {
      radio.addEventListener('change', (e) => {
        this.handleOutputModeChange(e.target.value);
      });
    });

    // Domain filter
    const domainFilter = document.getElementById('schema-speed-domain-filter');
    if (domainFilter) {
      domainFilter.addEventListener('change', (e) => {
        this.handleDomainFilterChange(e.target.value);
      });
    }

    // Profile select
    const profileSelect = document.getElementById('schema-speed-profile-select');
    if (profileSelect) {
      profileSelect.addEventListener('change', (e) => {
        this.handleProfileSelectChange(e.target.value);
      });
    }

    // Footer events
    const settingsBtn = document.getElementById('schema-speed-settings-btn');
    if (settingsBtn) {
      settingsBtn.addEventListener('click', () => {
        this.handleSettings();
      });
    }

    const helpBtn = document.getElementById('schema-speed-help-btn');
    if (helpBtn) {
      helpBtn.addEventListener('click', () => {
        this.handleHelp();
      });
    }

    // Saved templates dropdown
    const templatesDropdown = document.getElementById('schema-speed-templates-dropdown');
    if (templatesDropdown) {
      templatesDropdown.addEventListener('change', (e) => {
        const selected = e.target.value;
        if (selected) {
          this.useTemplate(selected);
        }
      });
    }
  }

  setupTabNavigation() {
    document.querySelectorAll('.schema-speed-nav-btn').forEach(btn => {
      btn.addEventListener('click', async (e) => {
        const tabName = e.target.dataset.tab;
        this.switchTab(tabName);
        await this.loadSavedTemplates();
      });
    });
  }

  switchTab(tabName) {
    // Update navigation
    document.querySelectorAll('.schema-speed-nav-btn').forEach(btn => {
      btn.classList.remove('active');
    });
    
    const activeNavBtn = document.querySelector(`[data-tab="${tabName}"]`);
    if (activeNavBtn) {
      activeNavBtn.classList.add('active');
    }

    // Update content
    document.querySelectorAll('.schema-speed-tab-content').forEach(content => {
      content.classList.remove('active');
    });
    
    const activeTabContent = document.getElementById(`schema-speed-${tabName}-tab`);
    if (activeTabContent) {
      activeTabContent.classList.add('active');
    }

    this.currentTab = tabName;

    // Load tab-specific data
    switch (tabName) {
      case 'prepare':
        // Prepare tab is always ready
        break;
      case 'scrape':
        this.refreshCaptureTab();
        break;
      case 'fill':
        this.refreshOutputTab();
        break;
    }
  }

  async refreshDashboard() {
    try {
      // Update stats
      const profiles = await this.profileManager.getDomainProfiles(this.currentDomain);
      const templates = await this.templateManager.getAllTemplates();
      const session = await this.sessionManager.getCurrentSession();

      const profilesCountEl = document.getElementById('schema-speed-profiles-count');
      if (profilesCountEl) {
        profilesCountEl.textContent = Object.keys(profiles).length;
      }

      const templatesCountEl = document.getElementById('schema-speed-templates-count');
      if (templatesCountEl) {
        templatesCountEl.textContent = Object.keys(templates || {}).length;
      }

      const capturedFieldsEl = document.getElementById('schema-speed-captured-fields');
      if (capturedFieldsEl) {
        capturedFieldsEl.textContent = session ? Object.keys(session.capturedData || {}).length : 0;
      }

      // Update recent activity
      this.updateRecentActivity();
    } catch (error) {
      console.error('Error refreshing dashboard:', error);
    }
  }

  updateRecentActivity() {
    const activityList = document.getElementById('schema-speed-activity-list');
    if (activityList) {
      activityList.innerHTML = `
        <div class="schema-speed-empty-state">
          <p>No recent activity</p>
        </div>
      `;
    }
  }

  async refreshCaptureTab() {
    try {
      // Load profiles for current domain
      const profiles = await this.profileManager.getDomainProfiles(this.currentDomain);
      const profileSelect = document.getElementById('schema-speed-profile-select');
      if (profileSelect) {
        profileSelect.innerHTML = '<option value="">Choose existing profile...</option>';
        Object.keys(profiles).forEach(profileType => {
          const option = document.createElement('option');
          option.value = profileType;
          option.textContent = profileType;
          profileSelect.appendChild(option);
        });
      }
      // Load current session if exists
      const session = await this.sessionManager.getCurrentSession();
      if (session) {
        this.displayCaptureProgress(session);
      } else {
        // No session: show mappableFields for mapping
        const mappableFieldsData = await chrome.storage.local.get('schemaSpeedMappableFields');
        const mappableFields = mappableFieldsData.schemaSpeedMappableFields || {};
        const fieldsList = document.getElementById('schema-speed-fields-list');
        if (fieldsList) {
          let fieldsHTML = '';
          Object.entries(mappableFields).forEach(([field, info]) => {
            fieldsHTML += `
              <div class="schema-speed-field-item to-map">
                <span class="schema-speed-field-name">${field}</span>
                <span class="schema-speed-field-status">Map</span>
              </div>
            `;
          });
          fieldsList.innerHTML = fieldsHTML || '<p class="schema-speed-empty-state">No fields to map. Please save a template first.</p>';
        }
        // Set progress counts
        const completedFields = document.getElementById('schema-speed-completed-fields');
        const totalFields = document.getElementById('schema-speed-total-fields');
        if (completedFields) completedFields.textContent = '0';
        if (totalFields) totalFields.textContent = Object.keys(mappableFields).length;
        // Show progress div
        const progressDiv = document.getElementById('schema-speed-capture-progress');
        if (progressDiv) progressDiv.style.display = 'block';
      }
    } catch (error) {
      console.error('Error refreshing capture tab:', error);
    }
  }

  async refreshProfilesTab() {
    try {
      const allProfiles = await this.profileManager.getAllProfiles();
      const domainFilter = document.getElementById('schema-speed-domain-filter');
      
      if (domainFilter) {
        // Update domain filter
        domainFilter.innerHTML = '<option value="">All Domains</option>';
        Object.keys(allProfiles).forEach(domain => {
          const option = document.createElement('option');
          option.value = domain;
          option.textContent = domain;
          if (domain === this.currentDomain) {
            option.selected = true;
          }
          domainFilter.appendChild(option);
        });
      }

      // Display profiles
      this.displayProfiles(allProfiles, this.currentDomain);
    } catch (error) {
      console.error('Error refreshing profiles tab:', error);
    }
  }

  displayProfiles(allProfiles, filterDomain = null) {
    const profilesList = document.getElementById('schema-speed-profiles-list');
    if (!profilesList) return;

    const profilesToShow = filterDomain ? 
      { [filterDomain]: allProfiles[filterDomain] || {} } : 
      allProfiles;

    if (Object.keys(profilesToShow).length === 0 || 
        (filterDomain && !allProfiles[filterDomain])) {
      profilesList.innerHTML = `
        <div class="schema-speed-empty-state">
          <p>No profiles found${filterDomain ? ` for ${filterDomain}` : ''}</p>
          <p>Create your first profile to get started!</p>
        </div>
      `;
      return;
    }

    let profilesHTML = '';
    Object.entries(profilesToShow).forEach(([domain, profiles]) => {
      Object.entries(profiles).forEach(([profileType, profile]) => {
        profilesHTML += this.createProfileCard(domain, profileType, profile);
      });
    });

    profilesList.innerHTML = profilesHTML;

    // Attach profile action listeners
    this.attachProfileActionListeners();
  }

  createProfileCard(domain, profileType, profile) {
    const selectorCount = Object.keys(profile.selectors || {}).length;
    const lastUsed = profile.lastUsed ? new Date(profile.lastUsed).toLocaleDateString() : 'Never';

    return `
      <div class="schema-speed-profile-card" data-domain="${domain}" data-profile="${profileType}">
        <div class="schema-speed-profile-card-header">
          <h5>${profileType} (${domain})</h5>
          <div class="schema-speed-profile-actions">
            <button class="schema-speed-link-btn edit-profile" data-domain="${domain}" data-profile="${profileType}">Edit</button>
            <button class="schema-speed-link-btn duplicate-profile" data-domain="${domain}" data-profile="${profileType}">Duplicate</button>
            <button class="schema-speed-link-btn delete-profile" data-domain="${domain}" data-profile="${profileType}">Delete</button>
          </div>
        </div>
        
        <div class="schema-speed-profile-card-body">
          <div class="schema-speed-profile-info">
            <span class="schema-speed-template-type">Template: ${profile.templateType || 'Custom'}</span>
            <span class="schema-speed-last-used">Last used: ${lastUsed}</span>
          </div>
          
          <div class="schema-speed-profile-fields">
            <strong>Mapped Fields (${selectorCount}):</strong>
            ${selectorCount > 0 ? `
              <div class="schema-speed-field-list">
                ${Object.entries(profile.selectors || {})
                  .slice(0, 3)
                  .map(([field, selector]) => `
                    <div class="schema-speed-field-item">
                      <span class="schema-speed-field-name">${field}:</span>
                      <span class="schema-speed-field-selector">${selector}</span>
                    </div>
                  `).join('')}
                ${selectorCount > 3 ? `<div class="schema-speed-field-more">+${selectorCount - 3} more</div>` : ''}
              </div>
            ` : '<p class="schema-speed-empty-state">No fields mapped</p>'}
          </div>
          
          ${profile.urlPatterns && profile.urlPatterns.length > 0 ? `
            <div class="schema-speed-url-patterns">
              <strong>URL Patterns:</strong>
              <div class="schema-speed-pattern-list">
                ${profile.urlPatterns.map(pattern => `<span class="schema-speed-pattern">${pattern}</span>`).join('')}
              </div>
            </div>
          ` : ''}
        </div>
      </div>
    `;
  }

  attachProfileActionListeners() {
    document.querySelectorAll('.edit-profile').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const domain = e.target.dataset.domain;
        const profile = e.target.dataset.profile;
        this.handleEditProfile(domain, profile);
      });
    });

    document.querySelectorAll('.duplicate-profile').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const domain = e.target.dataset.domain;
        const profile = e.target.dataset.profile;
        this.handleDuplicateProfile(domain, profile);
      });
    });

    document.querySelectorAll('.delete-profile').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const domain = e.target.dataset.domain;
        const profile = e.target.dataset.profile;
        this.handleDeleteProfile(domain, profile);
      });
    });
  }

  async refreshOutputTab() {
    try {
      const session = await this.sessionManager.getCurrentSession();
      const capturedDataPreview = document.getElementById('schema-speed-captured-data-preview');

      if (capturedDataPreview) {
        if (session && session.capturedData && Object.keys(session.capturedData).length > 0) {
          let previewHTML = '';
          Object.entries(session.capturedData).forEach(([field, data]) => {
            previewHTML += `
              <div class="schema-speed-captured-field">
                <strong>${field}:</strong> ${data.value || data}
              </div>
            `;
          });
          capturedDataPreview.innerHTML = previewHTML;
        } else {
          capturedDataPreview.innerHTML = '<p class="schema-speed-empty-state">No data captured yet</p>';
        }
      }
    } catch (error) {
      console.error('Error refreshing output tab:', error);
    }
  }

  displayCaptureProgress(session) {
    const progressDiv = document.getElementById('schema-speed-capture-progress');
    const completedFields = document.getElementById('schema-speed-completed-fields');
    const totalFields = document.getElementById('schema-speed-total-fields');
    const fieldsList = document.getElementById('schema-speed-fields-list');

    const capturedCount = Object.keys(session.capturedData || {}).length;
    const totalCount = session.totalFields || capturedCount;

    if (completedFields) completedFields.textContent = capturedCount;
    if (totalFields) totalFields.textContent = totalCount;

    if (fieldsList) {
      let fieldsHTML = '';
      Object.entries(session.capturedData || {}).forEach(([field, data]) => {
        fieldsHTML += `
          <div class="schema-speed-field-item captured">
            <span class="schema-speed-field-name">${field}</span>
            <span class="schema-speed-field-value">${data.value || data}</span>
            <span class="schema-speed-field-status">✓</span>
          </div>
        `;
      });
      fieldsList.innerHTML = fieldsHTML;
    }

    if (progressDiv) {
      progressDiv.style.display = 'block';
    }

    // Enable save session button if we have captured data
    const saveBtn = document.getElementById('schema-speed-save-capture-session');
    if (saveBtn) {
      saveBtn.disabled = capturedCount === 0;
    }
  }

  // Event Handlers
  async handleThemeToggle() {
    const body = document.body;
    const themeIcon = document.querySelector('.schema-speed-theme-icon');
    
    let newTheme = 'light';
    if (body.classList.contains('dark')) {
      body.classList.remove('dark');
      if (themeIcon) themeIcon.textContent = '🌙';
    } else {
      body.classList.add('dark');
      if (themeIcon) themeIcon.textContent = '☀️';
      newTheme = 'dark';
    }
    
    // Save theme preference
    try {
      await chrome.storage.local.set({ schemaSpeedTheme: newTheme });
    } catch (error) {
      console.error('Error saving theme preference:', error);
    }
    
    this.showNotification('Theme toggled', 'info');
  }

  handleCloseSidebar() {
    // Send a message to the parent window to close the sidebar
    if (window.parent) {
      window.parent.postMessage({ type: 'closeSidebar' }, '*');
    }
  }

  async handleExtractTemplate() {
    try {
      this.updateStatus('Extracting template...');
      
      const response = await chrome.runtime.sendMessage({
        action: 'extractTemplate'
      });

      if (response && response.success) {
        this.showNotification('Template extracted successfully!', 'success');
        await this.refreshDashboard();
      } else {
        this.showNotification('Failed to extract template: ' + (response?.error || 'Unknown error'), 'error');
      }
    } catch (error) {
      console.error('Error extracting template:', error);
      this.showNotification('Error extracting template', 'error');
    } finally {
      this.updateStatus('Ready');
    }
  }

  async handleFillFields() {
    try {
      const session = await this.sessionManager.getCurrentSession();
      if (!session || !session.capturedData) {
        this.showNotification('No captured data available', 'warning');
        return;
      }

      this.updateStatus('Filling fields...');

      const response = await chrome.runtime.sendMessage({
        action: 'populateFields',
        data: session.capturedData
      });

      if (response && response.success) {
        const results = response.results;
        this.showNotification(
          `Filled ${results.filled} fields, skipped ${results.skipped}`, 
          'success'
        );
      } else {
        this.showNotification('Failed to fill fields: ' + (response?.error || 'Unknown error'), 'error');
      }
    } catch (error) {
      console.error('Error filling fields:', error);
      this.showNotification('Error filling fields', 'error');
    } finally {
      this.updateStatus('Ready');
    }
  }

  async handleStartManualCapture() {
    try {
      // Start a new capture session
      await this.sessionManager.startSession(this.currentDomain, 'manual');
      
      const response = await chrome.runtime.sendMessage({
        action: 'startCapture',
        fieldName: 'manual'
      });

      if (response && response.success) {
        this.showNotification('Manual capture started', 'success');
      } else {
        this.showNotification('Failed to start capture: ' + (response?.error || 'Unknown error'), 'error');
      }
    } catch (error) {
      console.error('Error starting manual capture:', error);
      this.showNotification('Error starting capture', 'error');
    }
  }

  handleAutoDetectFields() {
    this.showNotification('Auto-detect feature coming soon!', 'info');
  }

  async handleSaveCaptureSession() {
    try {
      const session = await this.sessionManager.getCurrentSession();
      if (!session) {
        this.showNotification('No active session to save', 'warning');
        return;
      }

      this.showNotification('Session saved successfully!', 'success');
      await this.refreshDashboard();
    } catch (error) {
      console.error('Error saving capture session:', error);
      this.showNotification('Error saving session', 'error');
    }
  }

  handleCreateNewProfile() {
    this.showNotification('Profile creation interface coming soon!', 'info');
  }

  handleAddProfile() {
    this.showNotification('Profile creation interface coming soon!', 'info');
  }

  handleManageTemplates() {
    this.showNotification('Template management interface coming soon!', 'info');
  }

  handleImportProfiles() {
    this.showNotification('Profile import feature coming soon!', 'info');
  }

  handleExportProfiles() {
    this.showNotification('Profile export feature coming soon!', 'info');
  }

  handleEditProfile(domain, profileType) {
    this.showNotification(`Edit profile: ${profileType} (${domain}) - Coming soon!`, 'info');
  }

  handleDuplicateProfile(domain, profileType) {
    this.showNotification(`Duplicate profile: ${profileType} (${domain}) - Coming soon!`, 'info');
  }

  async handleDeleteProfile(domain, profileType) {
    if (confirm(`Are you sure you want to delete the profile "${profileType}" for ${domain}?`)) {
      try {
        await this.profileManager.deleteProfile(domain, profileType);
        this.showNotification('Profile deleted successfully!', 'success');
        await this.refreshProfilesTab();
      } catch (error) {
        console.error('Error deleting profile:', error);
        this.showNotification('Error deleting profile', 'error');
      }
    }
  }

  async handleFillFieldsExtension() {
    try {
      // Get the current tab
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      
      // Execute the field prefixing
      await chrome.scripting.executeScript({
        target: { tabId: tab.id },
        function: () => {
          // The fieldPrefixer is already initialized in the content script
          if (typeof fieldPrefixer !== 'undefined') {
            fieldPrefixer.prefixAllFields();
            return fieldPrefixer.getFieldStats();
          }
          return { error: 'Field prefixer not initialized' };
        }
      });

      this.updateStatus('Fields prefixed successfully');
      this.showNotification('Fields have been prefixed with SS-', 'success');
    } catch (error) {
      console.error('Error filling fields:', error);
      this.updateStatus('Error filling fields');
      this.showNotification('Failed to prefix fields', 'error');
    }
  }

  async handleClearFields() {
    try {
      // Get the current tab
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      
      // Execute the field clearing
      await chrome.scripting.executeScript({
        target: { tabId: tab.id },
        function: () => {
          // The fieldPrefixer is already initialized in the content script
          if (typeof fieldPrefixer !== 'undefined') {
            fieldPrefixer.clearAllFields();
            return { success: true };
          }
          return { error: 'Field prefixer not initialized' };
        }
      });

      this.updateStatus('Fields cleared successfully');
      this.showNotification('Fields have been cleared', 'success');
    } catch (error) {
      console.error('Error clearing fields:', error);
      this.updateStatus('Error clearing fields');
      this.showNotification('Failed to clear fields', 'error');
    }
  }

  async handleExtractTemplateTab() {
    try {
      const templateName = document.getElementById('schema-speed-template-name')?.value || 'Custom Template';
      const templateType = document.getElementById('schema-speed-template-type')?.value || 'custom';

      const response = await chrome.runtime.sendMessage({
        action: 'extractTemplate',
        templateName: templateName,
        templateType: templateType
      });

      if (response && response.success) {
        this.showNotification('Template extracted successfully!', 'success');
        this.updateStatus('Template extracted');
        
        // Show extracted fields
        this.displayExtractedFields(response.template);
      } else {
        this.showNotification('Failed to extract template: ' + (response?.error || 'Unknown error'), 'error');
      }
    } catch (error) {
      console.error('Error extracting template:', error);
      this.showNotification('Error extracting template', 'error');
    }
  }

  async handlePreviewTemplate() {
    try {
      const response = await chrome.runtime.sendMessage({
        action: 'extractTemplate',
        preview: true
      });

      if (response && response.success) {
        this.displayExtractedFields(response.template, true);
        this.showNotification('Template preview generated', 'info');
      } else {
        this.showNotification('Failed to preview template: ' + (response?.error || 'Unknown error'), 'error');
      }
    } catch (error) {
      console.error('Error previewing template:', error);
      this.showNotification('Error previewing template', 'error');
    }
  }

  displayExtractedFields(template, isPreview = false) {
    const extractedFields = document.getElementById('schema-speed-extracted-fields');
    const fieldsPreview = document.getElementById('schema-speed-fields-preview');
    
    if (!extractedFields || !fieldsPreview) return;

    if (template && template.fields && Object.keys(template.fields).length > 0) {
      fieldsPreview.innerHTML = '';
      
      Object.entries(template.fields).forEach(([fieldName, fieldInfo]) => {
        const fieldElement = document.createElement('div');
        fieldElement.className = 'schema-speed-field-item';
        fieldElement.innerHTML = `
          <div class="schema-speed-field-header">
            <span class="schema-speed-field-name">${fieldName}</span>
            <span class="schema-speed-field-type">${fieldInfo.type}</span>
          </div>
          <div class="schema-speed-field-details">
            <span class="schema-speed-field-label">Label: ${fieldInfo.label}</span>
            <span class="schema-speed-field-selector">Selector: ${fieldInfo.selector}</span>
            ${fieldInfo.required ? '<span class="schema-speed-field-required">Required</span>' : ''}
          </div>
        `;
        fieldsPreview.appendChild(fieldElement);
      });
      
      extractedFields.style.display = 'block';
    } else {
      fieldsPreview.innerHTML = '<p class="schema-speed-empty-state">No fields found. Make sure to fill the form fields first.</p>';
      extractedFields.style.display = 'block';
    }
  }

  handleOutputModeChange(mode) {
    const fillBuilderOptions = document.getElementById('schema-speed-fill-builder-options');
    const jsonSnippetOptions = document.getElementById('schema-speed-json-snippet-options');

    if (fillBuilderOptions && jsonSnippetOptions) {
      if (mode === 'fill-builder') {
        fillBuilderOptions.style.display = 'block';
        jsonSnippetOptions.style.display = 'none';
      } else {
        fillBuilderOptions.style.display = 'none';
        jsonSnippetOptions.style.display = 'block';
      }
    }
  }

  handleSettings() {
    this.showNotification('Settings interface coming soon!', 'info');
  }

  handleHelp() {
    this.showNotification('Help documentation coming soon!', 'info');
  }

  handleDomainFilterChange(domain) {
    this.refreshProfilesTab();
  }

  handleProfileSelectChange(profileType) {
    // Handle profile selection - could be used to pre-load profile data
    if (profileType) {
      this.showNotification(`Selected profile: ${profileType}`, 'info');
    }
  }

  handleCaptureModeChange(mode) {
    // Handle capture mode change (manual vs auto-detect)
    this.showNotification(`Capture mode changed to: ${mode}`, 'info');
  }

  // Utility Methods
  updateStatus(message) {
    const statusText = document.getElementById('schema-speed-status-text');
    const statusDot = document.querySelector('.schema-speed-status-dot');
    
    if (statusText) {
      statusText.textContent = message;
    }
    
    if (statusDot) {
      if (message === 'Ready') {
        statusDot.style.background = 'var(--schema-speed-success)';
      } else {
        statusDot.style.background = 'var(--schema-speed-warning)';
      }
    }
  }

  showNotification(message, type = 'info') {
    // Remove any existing notifications
    document.querySelectorAll('.schema-speed-notification').forEach(n => n.remove());

    // Create notification element
    const notification = document.createElement('div');
    notification.className = 'schema-speed-notification';

    // Set dot and border color based on type
    let dotColor = '#7C3AED'; // Purple for info
    let borderColor = 'rgba(124, 58, 237, 0.3)';
    if (type === 'success') {
      dotColor = '#22c55e'; // Green
      borderColor = 'rgba(34, 197, 94, 0.5)';
    } else if (type === 'error' || type === 'close' || type === 'danger') {
      dotColor = '#ef4444'; // Red
      borderColor = 'rgba(239, 68, 68, 0.5)';
    }

    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: rgba(0, 0, 0, 0.9);
      color: #fff;
      padding: 8px 12px;
      border-radius: 6px;
      z-index: 10000000;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 13px;
      font-weight: 500;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      backdrop-filter: blur(5px);
      border: 1px solid ${borderColor};
      opacity: 0;
      transform: translateX(20px);
      transition: all 0.3s ease-out;
      max-width: 300px;
      pointer-events: none;
      white-space: nowrap;
      display: flex;
      align-items: center;
      gap: 8px;
    `;

    notification.innerHTML = `<span style="color: ${dotColor}; font-size: 16px;">●</span> <span>${message}</span>`;

    document.body.appendChild(notification);

    // Animate in
    requestAnimationFrame(() => {
      notification.style.opacity = '1';
      notification.style.transform = 'translateX(0)';
    });

    // Remove after 3 seconds
    setTimeout(() => {
      notification.style.opacity = '0';
      notification.style.transform = 'translateX(20px)';
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    }, 3000);
  }

  setupDockingSystem() {
    // Load saved dock state
    this.loadDockState();
    
    // Apply initial dock state
    this.applyDockState();
  }

  async loadDockState() {
    try {
      const result = await chrome.storage.local.get(['sidebarDocked', 'sidebarDockPosition']);
      this.isDocked = result.sidebarDocked || false;
      this.dockPosition = result.sidebarDockPosition || 'right';
    } catch (error) {
      console.error('Error loading dock state:', error);
    }
  }

  async saveDockState() {
    try {
      await chrome.storage.local.set({
        sidebarDocked: this.isDocked,
        sidebarDockPosition: this.dockPosition
      });
    } catch (error) {
      console.error('Error saving dock state:', error);
    }
  }

  applyDockState() {
    const sidebar = document.getElementById('schema-speed-sidebar');
    const dockToggle = document.getElementById('schema-speed-dock-toggle');
    
    if (!sidebar || !dockToggle) return;

    if (this.isDocked) {
      sidebar.classList.add('docked', `docked-${this.dockPosition}`);
      dockToggle.innerHTML = '<i class="fas fa-arrows-alt-h"></i>';
      dockToggle.title = 'Undock Sidebar';
    } else {
      sidebar.classList.remove('docked', 'docked-left', 'docked-right');
      dockToggle.innerHTML = '<i class="fas fa-right-left"></i>';
      dockToggle.title = 'Dock Sidebar';
    }
  }

  handleDockToggle() {
    this.isDocked = !this.isDocked;
    // Toggle dock position if already docked
    this.dockPosition = this.dockPosition === 'right' ? 'left' : 'right';
    // Send a message to the parent window to dock the sidebar
    if (window.parent) {
      window.parent.postMessage({ type: 'dockSidebar', dockPosition: this.dockPosition }, '*');
    }
    this.applyDockState();
    this.saveDockState();
    this.showNotification(
      this.isDocked 
        ? `Sidebar docked to ${this.dockPosition}` 
        : 'Sidebar undocked', 
      'success'
    );
  }

  async handleReloadExtension() {
    try {
      // First try to reload via background script
      await chrome.runtime.sendMessage({ action: 'reloadExtension' });
      
      // If that succeeds, show success message
      this.showNotification('Extension reloading...', 'success');
      
      // Wait a moment for the notification to show
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Reload the page
      window.location.reload();
    } catch (error) {
      console.error('Error reloading extension:', error);
      
      // If context is invalid, force page reload
      if (error.message.includes('Extension context invalidated')) {
        this.showNotification('Reloading page...', 'info');
        setTimeout(() => window.location.reload(), 500);
      } else {
        this.showNotification('Please reload the extension manually', 'error');
      }
    }
  }

  async initializeTheme() {
    try {
      const result = await chrome.storage.local.get(['schemaSpeedTheme']);
      const savedTheme = result.schemaSpeedTheme || 'light';
      
      const body = document.body;
      const themeIcon = document.querySelector('.schema-speed-theme-icon');
      
      if (savedTheme === 'dark') {
        body.classList.add('dark');
        if (themeIcon) themeIcon.textContent = '☀️';
      } else {
        body.classList.remove('dark');
        if (themeIcon) themeIcon.textContent = '🌙';
      }
    } catch (error) {
      console.error('Error initializing theme:', error);
    }
  }

  async handleSaveTemplate() {
    try {
      console.debug('[DEBUG] handleSaveTemplate function called');
      const templateName = document.getElementById('schema-speed-template-name')?.value;
      const templateType = document.getElementById('schema-speed-template-type')?.value;
      let templateContent = document.getElementById('schema-speed-template-content')?.value;
      console.debug('[DEBUG] Template values:', { templateName, templateType, templateContent: templateContent?.substring(0, 100) + '...' });
      
      if (!templateName || !templateType || !templateContent) {
        this.showNotification('Please fill in all template fields', 'error');
        console.warn('[DEBUG] Missing template fields:', { templateName: !!templateName, templateType: !!templateType, templateContent: !!templateContent });
        return;
      }

      // Pretty-print JSON if possible
      let prettyContent = templateContent;
      // If it looks like a <script> block, extract and pretty-print the JSON
      if (prettyContent.trim().startsWith('<script')) {
        const match = prettyContent.match(/<script[^>]*>([\s\S]*?)<\/script>/i);
        if (match && match[1]) {
          try {
            const json = JSON.parse(match[1]);
            prettyContent = `<script type="application/ld+json">\n${JSON.stringify(json, null, 4)}\n<\/script>`;
          } catch (e) {
            // If not valid JSON, leave as is
          }
        }
      } else {
        // Try to pretty-print as JSON
        try {
          const json = JSON.parse(prettyContent);
          prettyContent = JSON.stringify(json, null, 4);
        } catch (e) {
          // Not JSON, leave as is
        }
      }
      
      this.updateStatus('Saving template...');
      
      // Save template using template manager
      const saved = await this.templateManager.saveTemplate({
        name: templateName,
        type: templateType,
        content: prettyContent,
        createdAt: new Date().toISOString()
      });

      if (saved) {
        this.showNotification('Template saved successfully!', 'success');
        this.switchTab('scrape');
        await this.loadSavedTemplates();
      } else {
        this.showNotification('Failed to save template', 'error');
      }
    } catch (error) {
      console.error('[DEBUG] Error in handleSaveTemplate:', error);
      this.showNotification('Error saving template. Please try again.', 'error');
    } finally {
      this.updateStatus('Ready');
    }
  }

  async loadSavedTemplates() {
    try {
      const templates = await this.templateManager.getAllTemplates();
      const lastUsed = await this.templateManager.getLastUsedTemplate();
      
      console.debug('[DEBUG] loadSavedTemplates found:', Object.keys(templates));
      
      // Update UI with templates
      const templatesList = document.getElementById('schema-speed-templates-list');
      const templatesDropdown = document.getElementById('schema-speed-templates-dropdown');
      
      if (templatesList) {
        templatesList.innerHTML = '';
        Object.entries(templates).forEach(([name, template]) => {
          const templateItem = document.createElement('div');
          templateItem.className = 'schema-speed-template-item';
          if (lastUsed && name === lastUsed) {
            templateItem.classList.add('last-used');
          }
          templateItem.innerHTML = `
            <div class="template-header">
              <span class="template-name">${name}</span>
              <span class="template-type">${template.type}</span>
            </div>
            <div class="template-actions">
              <button class="use-template" data-template="${name}">Use</button>
              <button class="delete-template" data-template="${name}">Delete</button>
            </div>
          `;
          templatesList.appendChild(templateItem);
        });
        
        templatesList.querySelectorAll('.use-template').forEach(btn => {
          btn.addEventListener('click', (e) => {
            const templateName = e.target.dataset.template;
            this.useTemplate(templateName);
          });
        });
        
        templatesList.querySelectorAll('.delete-template').forEach(btn => {
          btn.addEventListener('click', (e) => {
            const templateName = e.target.dataset.template;
            this.deleteTemplate(templateName);
          });
        });
      }
      
      // Update dropdown
      if (templatesDropdown) {
        templatesDropdown.innerHTML = '<option value="">Select a saved template...</option>';
        Object.entries(templates).forEach(([name, template]) => {
          const option = document.createElement('option');
          option.value = name;
          option.textContent = name;
          if (lastUsed && name === lastUsed) {
            option.selected = true;
          }
          templatesDropdown.appendChild(option);
        });
      }
      
      this.showNotification('[DEBUG] Templates loaded and UI updated', 'info');
    } catch (error) {
      console.error('[DEBUG] Error loading templates:', error);
      this.showNotification('Error loading templates', 'error');
    }
  }

  async useTemplate(templateName) {
    try {
      const template = await this.templateManager.getTemplate(templateName);
      if (template) {
        document.getElementById('schema-speed-template-name').value = templateName;
        document.getElementById('schema-speed-template-type').value = template.type;
        let content = template.content;
        // Unescape if double-encoded (JSON-stringified string)
        if (
          typeof content === 'string' &&
          content.startsWith('"') && content.endsWith('"') &&
          (content.includes('\\"') || content.includes('\\n') || content.includes('\"') || content.includes('\n'))
        ) {
          try {
            content = JSON.parse(content);
          } catch (e) {}
        }
        // Pretty-print for textarea
        if (content.trim().startsWith('<script')) {
          const match = content.match(/<script[^>]*>([\s\S]*?)<\/script>/i);
          if (match && match[1]) {
            try {
              const json = JSON.parse(match[1]);
              content = `<script type="application/ld+json">\n${JSON.stringify(json, null, 4)}\n<\/script>`;
            } catch (e) {}
          }
        } else {
          try {
            const json = JSON.parse(content);
            content = JSON.stringify(json, null, 4);
          } catch (e) {}
        }
        document.getElementById('schema-speed-template-content').value = content;
        await this.templateManager.setLastUsedTemplate(templateName);
        await this.loadSavedTemplates();
        this.showNotification(`Template "${templateName}" loaded`, 'success');
        this.handleTemplateContentChange();
      }
    } catch (error) {
      console.error('[DEBUG] Error using template:', error);
      this.showNotification('Error loading template', 'error');
    }
  }

  async deleteTemplate(templateName) {
    try {
      const deleted = await this.templateManager.deleteTemplate(templateName);
      if (deleted) {
        this.showNotification(`Template deleted: ${templateName}`, 'success');
        await this.loadSavedTemplates();
      }
    } catch (error) {
      console.error('Error deleting template:', error);
      this.showNotification('Error deleting template', 'error');
    }
  }

  // Utility: Extract SS- fields from template
  extractSSFieldsFromTemplate(templateString) {
    // Remove <script> tags if present
    let jsonString = templateString;
    if (jsonString.trim().startsWith('<script')) {
      const match = jsonString.match(/<script[^>]*>([\s\S]*?)<\/script>/i);
      if (match && match[1]) {
        jsonString = match[1];
      }
    }
    let obj;
    try {
      obj = JSON.parse(jsonString);
    } catch (e) {
      return [];
    }
    const fields = [];
    function recurse(o, path = []) {
      if (typeof o === 'string' && o.startsWith('SS-')) {
        fields.push({ path, label: o });
      } else if (Array.isArray(o)) {
        o.forEach((item, idx) => recurse(item, path.concat([idx])));
      } else if (typeof o === 'object' && o !== null) {
        Object.entries(o).forEach(([k, v]) => recurse(v, path.concat([k])));
      }
    }
    recurse(obj);
    return fields;
  }

  // Add to SidebarController
  renderSSFieldsList() {
    const templateContent = document.getElementById('schema-speed-template-content')?.value;
    const fieldsListDiv = document.getElementById('schema-speed-fields-list');
    if (!fieldsListDiv) return;
    fieldsListDiv.innerHTML = '';
    if (!templateContent) return;
    const fields = this.extractSSFieldsFromTemplate(templateContent);
    if (!fields.length) return;
    fields.forEach(({ path, label }) => {
      const pathString = path.join('.');
      const value = this.ssFieldValues[pathString] || '';
      const isCapturing = this.ssFieldCaptureActive === pathString;
      const li = document.createElement('div');
      li.className = 'ss-field-list-item' + (isCapturing ? ' ss-capturing' : '');
      li.innerHTML = `
        <span class="ss-status-dot ${value ? 'green' : 'red'}"></span>
        <label class="ss-field-label">${label.replace('SS-', '')}</label>
        <input type="text" class="ss-field-input" value="${value}" data-path="${pathString}" style="margin:0 8px;" ${isCapturing ? 'disabled' : ''} />
        <button class="ss-capture-btn" data-path="${pathString}" ${isCapturing ? 'disabled' : ''}>${isCapturing ? 'Capturing...' : 'Capture'}</button>
      `;
      fieldsListDiv.appendChild(li);
    });
    // Wire up input events
    fieldsListDiv.querySelectorAll('.ss-field-input').forEach(input => {
      input.addEventListener('input', (e) => {
        const path = e.target.dataset.path;
        this.ssFieldValues[path] = e.target.value;
        this.renderSSFieldsList(); // update status dot
      });
    });
    // Wire up capture buttons
    fieldsListDiv.querySelectorAll('.ss-capture-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const path = e.target.dataset.path;
        this.startDummyCapture(path);
      });
    });
  }

  // Simulate capture system for now
  startDummyCapture(path) {
    this.ssFieldCaptureActive = path;
    this.renderSSFieldsList();
    setTimeout(() => {
      this.ssFieldValues[path] = 'Captured Value for ' + path;
      this.ssFieldCaptureActive = null;
      this.renderSSFieldsList();
    }, 1200); // Simulate 1.2s capture delay
  }

  // Call this after template loads/changes
  handleTemplateContentChange() {
    this.renderSSFieldsList();
  }
}

// Storage Classes
class SchemaStorage {
  constructor() {
    this.storageKey = 'pageToSchemaData';
  }

  async initialize() {
    return true;
  }

  async getStoredData() {
    try {
      const result = await chrome.storage.local.get([this.storageKey]);
      return result[this.storageKey] || null;
    } catch (error) {
      console.error('Error accessing storage:', error);
      return null;
    }
  }
}

// New TemplateManager class for universal templates
class TemplateManager {
  constructor() {
    this.templatesKey = 'schemaSpeedTemplates';
    this.lastUsedKey = 'schemaSpeedLastUsedTemplate';
  }

  async initialize() {
    try {
      // Request initial templates load from background script
      const response = await chrome.runtime.sendMessage({ action: 'getTemplates' });
      console.debug('[DEBUG] Template manager initialized:', response?.success ? 'success' : 'failed');
      return response?.success || false;
    } catch (error) {
      console.error('[DEBUG] Error initializing template manager:', error);
      return false;
    }
  }

  async getAllTemplates() {
    try {
      const response = await chrome.runtime.sendMessage({ action: 'getTemplates' });
      return response?.templates || {};
    } catch (error) {
      console.error('Error getting templates:', error);
      return {};
    }
  }

  async getTemplate(templateName) {
    try {
      const response = await chrome.runtime.sendMessage({ action: 'getTemplate', name: templateName });
      return response?.template || null;
    } catch (error) {
      console.error('Error getting template:', error);
      return null;
    }
  }

  async saveTemplate(template) {
    try {
      const response = await chrome.runtime.sendMessage({ action: 'saveTemplate', template });
      return response?.success || false;
    } catch (error) {
      console.error('Error saving template:', error);
      return false;
    }
  }

  async deleteTemplate(templateName) {
    try {
      const response = await chrome.runtime.sendMessage({ action: 'deleteTemplate', name: templateName });
      return response?.success || false;
    } catch (error) {
      console.error('Error deleting template:', error);
      return false;
    }
  }

  async getLastUsedTemplate() {
    try {
      const response = await chrome.runtime.sendMessage({ action: 'getTemplates' });
      return response?.lastUsed || null;
    } catch (error) {
      console.error('Error getting last used template:', error);
      return null;
    }
  }

  async setLastUsedTemplate(templateName) {
    try {
      const response = await chrome.runtime.sendMessage({ action: 'setLastUsedTemplate', name: templateName });
      return response?.success || false;
    } catch (error) {
      console.error('Error setting last used template:', error);
      return false;
    }
  }
}

class ProfileManager extends SchemaStorage {
  async getDomainProfiles(domain) {
    const data = await this.getStoredData();
    return data?.profiles?.[domain] || {};
  }

  async getAllProfiles() {
    const data = await this.getStoredData();
    return data?.profiles || {};
  }

  async deleteProfile(domain, profileType) {
    try {
      const data = await this.getStoredData();
      if (data?.profiles?.[domain]?.[profileType]) {
        delete data.profiles[domain][profileType];
        
        // Clean up empty domain entries
        if (Object.keys(data.profiles[domain]).length === 0) {
          delete data.profiles[domain];
        }
        
        await chrome.storage.local.set({ [this.storageKey]: data });
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error deleting profile:', error);
      return false;
    }
  }
}

class SessionManager extends SchemaStorage {
  async getCurrentSession() {
    const data = await this.getStoredData();
    return data?.sessions?.current || null;
  }

  async startSession(domain, profileType) {
    try {
      const data = await this.getStoredData() || {};
      
      if (!data.sessions) {
        data.sessions = {};
      }
      
      data.sessions.current = {
        domain,
        profileType,
        capturedData: {},
        startedAt: new Date().toISOString()
      };
      
      await chrome.storage.local.set({ [this.storageKey]: data });
      return data.sessions.current;
    } catch (error) {
      console.error('Error starting session:', error);
      return null;
    }
  }
}

// Export SidebarController as a named export
export { SidebarController };
