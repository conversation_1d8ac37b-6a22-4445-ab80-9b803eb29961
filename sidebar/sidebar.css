/* Schema Speed Assistant - Sidebar Styles */
/* TARGETED STYLE ISOLATION - Prevent inheritance while preserving our styles */

/* Scope all sidebar styles to prevent conflicts */
#schema-speed-sidebar-container {
  /* Force consistent typography */
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
  color: #f1f5f9 !important;
  
  /* Reset problematic inherited styles */
  text-shadow: none !important;
  text-transform: none !important;
  letter-spacing: normal !important;
  word-spacing: normal !important;
  box-sizing: border-box !important;
  
  /* Ensure proper stacking and isolation */
  position: fixed !important;
  top: 0 !important;
  right: 0 !important;
  width: 600px !important;
  height: 100vh !important;
  z-index: 2147483647 !important; /* Maximum possible z-index */
  isolation: isolate !important;
  contain: layout style paint !important;
  background: var(--schema-speed-background) !important;
  border-left: 2px solid var(--schema-speed-primary) !important;
  padding: 24px !important; /* Increased overall padding */
}

/* Ensure all child elements inherit our font settings */
#schema-speed-sidebar-container * {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
  text-shadow: none !important;
  box-sizing: border-box !important;
}

/* Root container isolation */
#schema-speed-sidebar-container {
  position: fixed !important;
  top: 0 !important;
  right: 0 !important;
  width: 600px !important;
  height: 100vh !important;
  z-index: 2147483647 !important; /* Maximum possible z-index */
  isolation: isolate !important;
  contain: layout style !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
  color: #f1f5f9 !important;
}

/* CSS Variables */
:root {
  --schema-speed-primary: #7C3AED; /* Brand Purple */
  --schema-speed-primary-hover: #6a19a3; /* Slightly darker purple for hover */
  --schema-speed-secondary: #2c2c2c; /* Dark grey for secondary backgrounds */
  --schema-speed-background: #ffffff;
  --schema-speed-surface: #f8fafc;
  --schema-speed-border: #e2e8f0;
  --schema-speed-text: #0f172a;
  --schema-speed-text-muted: #64748b;
  --schema-speed-success: #10b981;
  --schema-speed-warning: #f59e0b;
  --schema-speed-error: #ef4444;
  --schema-speed-chart-1: #7C3AED;
  --schema-speed-chart-2: #10b981;
  --schema-speed-chart-3: #f59e0b;
  --schema-speed-chart-4: #ef4444;
  --schema-speed-chart-5: #8b5cf6;
  --schema-speed-destructive: #dc2626;
  --schema-speed-destructive-hover: #b91c1c;
  --schema-speed-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --schema-speed-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

/* Dark theme */
.dark {
  --schema-speed-primary: #7C3AED !important;
  --schema-speed-primary-hover: #6a19a3 !important;
  --schema-speed-secondary: #1e1e1e !important;
  --schema-speed-background: #18181b !important;
  --schema-speed-surface: #1e1e1e !important;
  --schema-speed-border: #333333 !important;
  --schema-speed-text: #ffffff !important;
  --schema-speed-text-muted: #a1a1aa !important;
}

/* Sidebar container */
#schema-speed-sidebar-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 600px;
  background: var(--schema-speed-background);
  border-left: 2px solid var(--schema-speed-primary);
}

/* Header */
#schema-speed-sidebar-container .schema-speed-sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: var(--schema-speed-surface);
  border-bottom: 1px solid var(--schema-speed-border);
  min-height: 60px;
}

#schema-speed-sidebar-container .schema-speed-logo-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

#schema-speed-sidebar-container .schema-speed-logo-section h1 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--schema-speed-primary);
}

#schema-speed-sidebar-container .schema-speed-version {
  background: var(--schema-speed-primary);
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
}

#schema-speed-sidebar-container .schema-speed-header-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  background: transparent;
}

#schema-speed-sidebar-container .schema-speed-status-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: var(--schema-speed-text-muted);
}

#schema-speed-sidebar-container .schema-speed-status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--schema-speed-success);
}

#schema-speed-sidebar-container .schema-speed-control-buttons {
  display: flex;
  gap: 4px;
}

#schema-speed-sidebar-container .schema-speed-control-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  border-radius: 6px;
  cursor: pointer;
  color: var(--schema-speed-text-muted);
  transition: all 0.2s ease;
}

#schema-speed-sidebar-container .schema-speed-control-btn:hover {
  background: var(--schema-speed-border);
  color: var(--schema-speed-text);
}

/* Navigation */
#schema-speed-sidebar-container .schema-speed-sidebar-nav {
  display: flex;
  background: var(--schema-speed-surface);
  border-bottom: 1px solid var(--schema-speed-border);
  padding: 0 4px;
}

#schema-speed-sidebar-container .schema-speed-nav-btn {
  flex: 1;
  padding: 12px 16px;
  border: none;
  background: transparent;
  color: var(--schema-speed-text-muted);
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  border-radius: 6px;
  margin: 4px 2px;
  transition: all 0.2s ease;
}

#schema-speed-sidebar-container .schema-speed-nav-btn:hover {
  background: var(--schema-speed-border);
  color: var(--schema-speed-text);
}

#schema-speed-sidebar-container .schema-speed-nav-btn.active {
  background: var(--schema-speed-primary);
  color: white;
}

/* Content area */
#schema-speed-sidebar-container .schema-speed-sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
  background: var(--schema-speed-background);
}

#schema-speed-sidebar-container .schema-speed-tab-content {
  display: none;
  padding: 24px !important; /* Match design system cardPadding */
  height: 100%;
  background: var(--schema-speed-background);
}

#schema-speed-sidebar-container .schema-speed-tab-content.active {
  display: block;
  background: var(--schema-speed-background);
}

/* Current site info */
#schema-speed-sidebar-container .schema-speed-current-site-info {
  background: #1e1e1e !important;
  border: 1px solid #333333 !important;
  border-radius: 8px !important;
  padding: 24px !important; /* Match design system cardPadding */
}

#schema-speed-sidebar-container .schema-speed-current-site-info h3 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--schema-speed-text);
}

#schema-speed-sidebar-container .schema-speed-quick-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px !important; /* Match design system sectionSpacing */
  margin-top: 16px !important; /* Match design system fieldSpacing */
}

#schema-speed-sidebar-container .schema-speed-stat-item {
  text-align: center;
}

#schema-speed-sidebar-container .schema-speed-stat-value {
  display: block;
  font-size: 24px;
  font-weight: 700;
  color: var(--schema-speed-primary);
  margin-bottom: 4px;
}

#schema-speed-sidebar-container .schema-speed-stat-label {
  font-size: 11px;
  color: var(--schema-speed-text-muted);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

#schema-speed-sidebar-container .schema-speed-quick-actions {
  margin-bottom: 20px;
}

#schema-speed-sidebar-container .schema-speed-quick-actions h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--schema-speed-text);
}

#schema-speed-sidebar-container .schema-speed-action-buttons {
  margin-top: 24px !important; /* Match design system sectionSpacing */
  display: flex;
  flex-direction: column;
  gap: 12px !important; /* Match design system spacing.scale[2] */
}

/* Quick actions */
#schema-speed-sidebar-container .schema-speed-action-btn {
  display: flex !important;
  align-items: center !important;
  gap: 10px !important;
  padding: 12px 20px !important;
  border: 1px solid var(--schema-speed-primary) !important;
  border-radius: 6px !important;
  font-size: 13px !important;
  font-weight: 500 !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  text-align: left !important;
  line-height: 1.4 !important;
  background: #1e1e1e !important;
  color: #ffffff !important;
}

#schema-speed-sidebar-container .schema-speed-action-btn:hover {
  background: #2a2a2a !important;
  border-color: var(--schema-speed-primary-hover) !important;
}

#schema-speed-sidebar-container .schema-speed-action-btn.primary {
  background: #1e1e1e !important;
  border-color: var(--schema-speed-primary) !important;
  color: #ffffff !important;
}

#schema-speed-sidebar-container .schema-speed-action-btn.primary:hover {
  background: #2a2a2a !important;
  border-color: var(--schema-speed-primary-hover) !important;
}

#schema-speed-sidebar-container .schema-speed-action-btn.secondary {
  background: transparent !important;
  border: 1px solid #333333 !important;
  color: #ffffff !important;
}

#schema-speed-sidebar-container .schema-speed-action-btn.secondary:hover {
  background: #2a2a2a !important;
  border-color: var(--schema-speed-primary) !important;
}

#schema-speed-sidebar-container .schema-speed-btn-icon {
  font-size: 16px;
  color: inherit;
}

/* Recent activity */
#schema-speed-sidebar-container .schema-speed-recent-activity h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--schema-speed-text);
}

#schema-speed-sidebar-container .schema-speed-activity-list {
  background: var(--schema-speed-surface);
  border-radius: 6px;
  border: 1px solid var(--schema-speed-border);
  min-height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

#schema-speed-sidebar-container .schema-speed-empty-state {
  text-align: center;
  color: var(--schema-speed-text-muted);
  font-size: 12px;
}

/* Capture tab */
#schema-speed-sidebar-container .schema-speed-capture-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

#schema-speed-sidebar-container .schema-speed-capture-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--schema-speed-text);
}

#schema-speed-sidebar-container .schema-speed-capture-mode-toggle {
  display: flex;
  gap: 12px;
  background: transparent;
}

#schema-speed-sidebar-container .schema-speed-toggle-label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: var(--schema-speed-text-muted);
  cursor: pointer;
}

#schema-speed-sidebar-container .schema-speed-toggle-label input {
  margin: 0;
}

/* Form elements */
#schema-speed-sidebar-container .schema-speed-profile-selection,
#schema-speed-sidebar-container .schema-speed-template-selection,
#schema-speed-sidebar-container .schema-speed-domain-selector {
  margin-bottom: 20px;
}

#schema-speed-sidebar-container .schema-speed-profile-selection label,
#schema-speed-sidebar-container .schema-speed-template-selection label,
#schema-speed-sidebar-container .schema-speed-domain-selector label {
  display: block;
  margin-bottom: 6px;
  font-size: 13px;
  font-weight: 500;
  color: #ffffff !important;
}

#schema-speed-sidebar-container .schema-speed-profile-selection select,
#schema-speed-sidebar-container .schema-speed-template-selection select,
#schema-speed-sidebar-container .schema-speed-domain-selector select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--schema-speed-border);
  border-radius: 6px;
  background: var(--schema-speed-background);
  color: var(--schema-speed-text);
  font-size: 13px;
}

#schema-speed-sidebar-container .schema-speed-link-btn {
  background: none;
  border: none;
  color: var(--schema-speed-primary);
  font-size: 12px;
  cursor: pointer;
  text-decoration: underline;
  margin-top: 8px;
}

#schema-speed-sidebar-container .schema-speed-link-btn:hover {
  color: var(--schema-speed-primary-hover);
}

/* Capture progress */
#schema-speed-sidebar-container .schema-speed-capture-progress {
  background: var(--schema-speed-surface);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
  border: 1px solid var(--schema-speed-border);
}

#schema-speed-sidebar-container .schema-speed-progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

#schema-speed-sidebar-container .schema-speed-progress-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

#schema-speed-sidebar-container .schema-speed-progress-counter {
  font-size: 12px;
  color: var(--schema-speed-text-muted);
}

#schema-speed-sidebar-container .schema-speed-fields-list {
  margin: 16px 0;
  padding: 0;
}

#schema-speed-sidebar-container .schema-speed-field-item {
  background: #1e1e1e;
  border: 1px solid #333;
  border-radius: 6px;
  margin-bottom: 16px;
  padding: 16px;
}

#schema-speed-sidebar-container .schema-speed-field-item .field-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

#schema-speed-sidebar-container .schema-speed-field-item .field-name {
  font-weight: 600;
  color: #f1f5f9;
}

#schema-speed-sidebar-container .schema-speed-field-item .required-badge {
  background: #7C3AED;
  color: white;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
}

#schema-speed-sidebar-container .schema-speed-field-item .field-selector {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
}

#schema-speed-sidebar-container .schema-speed-field-item .selector-input {
  flex: 1;
  background: #2d2d2d !important;
  border: 1px solid #444 !important;
  border-radius: 4px !important;
  padding: 8px 12px !important;
  color: #f1f5f9 !important;
  font-family: monospace !important;
}

#schema-speed-sidebar-container .schema-speed-field-item .select-element-btn {
  background: #7C3AED;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  font-weight: 500;
}

#schema-speed-sidebar-container .schema-speed-field-item .select-element-btn:hover {
  background: #6D28D9;
}

#schema-speed-sidebar-container .schema-speed-field-item .field-preview {
  font-size: 13px;
  color: #94a3b8;
  margin-top: 8px;
  padding: 8px;
  background: #2d2d2d;
  border-radius: 4px;
  min-height: 24px;
}

/* Active selection state */
#schema-speed-sidebar-container .schema-speed-selecting .schema-speed-field-item[data-active="true"] {
  border-color: #7C3AED;
  box-shadow: 0 0 0 2px rgba(124, 58, 237, 0.2);
}

/* Capture actions */
#schema-speed-sidebar-container .schema-speed-capture-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* Profiles tab */
#schema-speed-sidebar-container .schema-speed-profiles-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

#schema-speed-sidebar-container .schema-speed-profiles-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--schema-speed-text);
}

#schema-speed-sidebar-container .schema-speed-profile-header-actions {
  display: flex;
  gap: 12px;
}

#schema-speed-sidebar-container .schema-speed-profiles-list {
  margin-bottom: 20px;
  min-height: 200px;
}

#schema-speed-sidebar-container .schema-speed-profile-card {
  background: var(--schema-speed-surface);
  border: 1px solid var(--schema-speed-border);
  border-radius: 8px;
  margin-bottom: 12px;
  overflow: hidden;
}

#schema-speed-sidebar-container .schema-speed-profile-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: var(--schema-speed-background);
  border-bottom: 1px solid var(--schema-speed-border);
}

#schema-speed-sidebar-container .schema-speed-profile-card-header h5 {
  margin: 0;
  font-size: 13px;
  font-weight: 600;
  color: var(--schema-speed-text);
}

#schema-speed-sidebar-container .schema-speed-profile-actions {
  display: flex;
  gap: 10px;
}

#schema-speed-sidebar-container .schema-speed-profile-card-body {
  padding: 12px 16px;
}

#schema-speed-sidebar-container .schema-speed-profile-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  font-size: 11px;
  color: var(--schema-speed-text-muted);
}

#schema-speed-sidebar-container .schema-speed-profile-fields strong {
  font-size: 12px;
  color: var(--schema-speed-text);
}

#schema-speed-sidebar-container .schema-speed-field-list {
  margin-top: 8px;
}

#schema-speed-sidebar-container .schema-speed-field-item {
  display: flex;
  justify-content: space-between;
  font-size: 11px;
  margin-bottom: 4px;
}

#schema-speed-sidebar-container .schema-speed-field-selector {
  color: var(--schema-speed-text-muted);
  font-family: monospace;
}

#schema-speed-sidebar-container .schema-speed-field-more {
  font-size: 11px;
  color: var(--schema-speed-text-muted);
  font-style: italic;
  margin-top: 4px;
}

#schema-speed-sidebar-container .schema-speed-url-patterns {
  margin-top: 12px;
  background: transparent;
}

#schema-speed-sidebar-container .schema-speed-pattern-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-top: 4px;
}

#schema-speed-sidebar-container .schema-speed-pattern {
  background: var(--schema-speed-primary);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-family: monospace;
}

/* Profile actions */
#schema-speed-sidebar-container .schema-speed-profile-actions {
  display: flex;
  gap: 10px;
}

/* Output tab */
#schema-speed-sidebar-container .schema-speed-output-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

#schema-speed-sidebar-container .schema-speed-output-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--schema-speed-text);
}

#schema-speed-sidebar-container .schema-speed-output-mode-toggle {
  display: flex;
  gap: 12px;
  background: transparent;
}

#schema-speed-sidebar-container .schema-speed-session-data {
  background: var(--schema-speed-surface);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
  border: 1px solid var(--schema-speed-border);
}

#schema-speed-sidebar-container .schema-speed-session-data h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--schema-speed-text);
}

#schema-speed-sidebar-container .schema-speed-captured-field {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid var(--schema-speed-border);
  font-size: 12px;
  color: var(--schema-speed-text);
}

#schema-speed-sidebar-container .schema-speed-captured-field:last-child {
  border-bottom: none;
}

#schema-speed-sidebar-container .schema-speed-captured-field strong {
  color: var(--schema-speed-text);
}

#schema-speed-sidebar-container .schema-speed-output-options {
  margin-bottom: 20px;
}

#schema-speed-sidebar-container .schema-speed-snippet-settings {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 12px;
}

#schema-speed-sidebar-container .schema-speed-output-format {
  margin-bottom: 12px;
}

#schema-speed-sidebar-container .schema-speed-snippet-settings label,
#schema-speed-sidebar-container .schema-speed-output-format label {
  display: block;
  margin-bottom: 4px;
  font-size: 12px;
  font-weight: 500;
}

#schema-speed-sidebar-container .schema-speed-snippet-settings select,
#schema-speed-sidebar-container .schema-speed-output-format select {
  width: 100%;
  padding: 6px 8px;
  border: 1px solid var(--schema-speed-border);
  border-radius: 4px;
  background: var(--schema-speed-background);
  color: var(--schema-speed-text);
  font-size: 12px;
}

#schema-speed-sidebar-container .schema-speed-output-result {
  background: var(--schema-speed-surface);
  border-radius: 8px;
  padding: 16px;
  border: 1px solid var(--schema-speed-border);
}

#schema-speed-sidebar-container .schema-speed-result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

#schema-speed-sidebar-container .schema-speed-result-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--schema-speed-text);
}

#schema-speed-sidebar-container .schema-speed-code-block {
  background: var(--schema-speed-background);
  border: 1px solid var(--schema-speed-border);
  border-radius: 4px;
  padding: 12px;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 11px;
  line-height: 1.4;
  color: var(--schema-speed-text);
  overflow-x: auto;
  white-space: pre;
}

/* Footer */
#schema-speed-sidebar-container .schema-speed-sidebar-footer {
  padding: 12px 20px;
  background: var(--schema-speed-surface);
  border-top: 1px solid var(--schema-speed-border);
}

#schema-speed-sidebar-container .schema-speed-footer-links {
  display: flex;
  justify-content: center;
  gap: 20px;
}

#schema-speed-sidebar-container .schema-speed-footer-btn {
  background: none;
  border: none;
  color: var(--schema-speed-text-muted);
  font-size: 12px;
  cursor: pointer;
  text-decoration: underline;
}

#schema-speed-sidebar-container .schema-speed-footer-btn:hover {
  color: var(--schema-speed-text);
}

/* Scrollbar styling */
#schema-speed-sidebar-container .schema-speed-sidebar-content::-webkit-scrollbar {
  width: 6px;
}

#schema-speed-sidebar-container .schema-speed-sidebar-content::-webkit-scrollbar-track {
  background: var(--schema-speed-surface);
}

#schema-speed-sidebar-container .schema-speed-sidebar-content::-webkit-scrollbar-thumb {
  background: var(--schema-speed-border);
  border-radius: 3px;
}

#schema-speed-sidebar-container .schema-speed-sidebar-content::-webkit-scrollbar-thumb:hover {
  background: var(--schema-speed-text-muted);
}

/* Responsive adjustments */
@media (max-width: 400px) {
  #schema-speed-sidebar-container .schema-speed-sidebar-header {
    padding: 12px 16px;
  }
  
  #schema-speed-sidebar-container .schema-speed-tab-content {
    padding: 16px;
  }
  
  #schema-speed-sidebar-container .schema-speed-quick-stats {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  #schema-speed-sidebar-container .schema-speed-action-buttons {
    gap: 6px;
  }
  
  #schema-speed-sidebar-container .schema-speed-snippet-settings {
    grid-template-columns: 1fr;
  }
}

/* Animation and transitions */
#schema-speed-sidebar-container .schema-speed-tab-content {
  animation: fadeIn 0.2s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Notification styles */
#schema-speed-sidebar-container .schema-speed-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 12px 16px;
  border-radius: 6px;
  color: white;
  font-size: 13px;
  z-index: 10000;
  max-width: 300px;
  word-wrap: break-word;
  box-shadow: var(--schema-speed-shadow-lg);
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

#schema-speed-sidebar-container .schema-speed-notification.success {
  background: var(--schema-speed-success);
}

#schema-speed-sidebar-container .schema-speed-notification.error {
  background: var(--schema-speed-error);
}

#schema-speed-sidebar-container .schema-speed-notification.warning {
  background: var(--schema-speed-warning);
}

#schema-speed-sidebar-container .schema-speed-notification.info {
  background: var(--schema-speed-primary);
}

/* Docked sidebar positions */
#schema-speed-sidebar-container.docked-left {
  left: 0;
  right: auto;
  border-left: none;
  border-right: 2px solid var(--schema-speed-primary);
}
#schema-speed-sidebar-container.docked-right {
  right: 0;
  left: auto;
  border-right: none;
  border-left: 2px solid var(--schema-speed-primary);
}

/* Reload button styles (matching GMB Extractor) */
#reloadBtn {
  background: transparent;
  border: 1px solid var(--schema-speed-border);
  color: var(--schema-speed-text-muted);
  font-size: 11px;
  opacity: 0.7;
  width: 24px;
  height: 24px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  border-radius: 4px;
}

#reloadBtn svg {
  width: 12px;
  height: 12px;
  transition: transform 0.2s ease;
}

#reloadBtn:hover {
  background: var(--schema-speed-secondary) !important;
  border-color: var(--schema-speed-primary) !important;
  color: var(--schema-speed-text) !important;
  opacity: 1 !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
}

#reloadBtn:hover svg {
  transform: rotate(180deg);
}

#reloadBtn:active {
  transform: translateY(0);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

/* Dark theme specific reload button styles */
.dark #reloadBtn {
  border-color: #333333;
  color: #6b7280;
}

.dark #reloadBtn:hover {
  background: #262626;
  border-color: #404040;
  color: #d1d5db;
}

/* Light theme specific reload button styles */
:not(.dark) #reloadBtn {
  border-color: #d1d5db;
  color: #6b7280;
}

:not(.dark) #reloadBtn:hover {
  background: #f8fafc;
  border-color: #7C3AED;
  color: #374151;
}

/* Workflow Steps */
#schema-speed-sidebar-container .schema-speed-workflow-steps {
  margin-top: 24px;
  padding: 16px;
  background: var(--schema-speed-card-bg);
  border-radius: 8px;
  border: 1px solid var(--schema-speed-border);
}

#schema-speed-sidebar-container .schema-speed-workflow-steps h4 {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--schema-speed-text-primary);
}

#schema-speed-sidebar-container .schema-speed-steps-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

#schema-speed-sidebar-container .schema-speed-step {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  background: var(--schema-speed-bg-secondary);
  border-radius: 6px;
  border-left: 3px solid var(--schema-speed-primary);
}

#schema-speed-sidebar-container .schema-speed-step-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: var(--schema-speed-primary);
  color: white;
  border-radius: 50%;
  font-size: 12px;
  font-weight: 600;
  flex-shrink: 0;
}

#schema-speed-sidebar-container .schema-speed-step-text {
  font-size: 13px;
  color: var(--schema-speed-text-secondary);
  line-height: 1.4;
}

/* Tab Description */
#schema-speed-sidebar-container .schema-speed-tab-description {
  margin: 0 0 20px 0;
  font-size: 14px;
  color: var(--schema-speed-text-secondary);
  line-height: 1.5;
}

/* Action Groups */
#schema-speed-sidebar-container .schema-speed-action-group {
  margin-bottom: 32px !important; /* Match design system spacing.scale[6] */
  padding: 24px !important; /* Match design system cardPadding */
  background: var(--schema-speed-card-bg);
  border: 1px solid var(--schema-speed-border);
  border-radius: 12px;
}

#schema-speed-sidebar-container .schema-speed-action-group:last-child {
  margin-bottom: 0;
}

#schema-speed-sidebar-container .schema-speed-action-group h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--schema-speed-text-primary);
}

#schema-speed-sidebar-container .schema-speed-action-group p {
  margin: 0 0 16px 0;
  font-size: 14px;
  color: var(--schema-speed-text-secondary);
  line-height: 1.5;
}

/* Tab Description */
#schema-speed-sidebar-container .schema-speed-tab-description {
  font-size: 16px !important;
  line-height: 1.6 !important;
  color: #a1a1aa !important;
  margin: 0 0 32px 0 !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
}

/* Form Fields - Targeted Reset */
#schema-speed-sidebar-container input,
#schema-speed-sidebar-container select,
#schema-speed-sidebar-container textarea {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
  color: #ffffff !important;
  background: #1e1e1e !important;
  border: 1px solid #333333 !important;
  border-radius: 6px !important;
  padding: 8px 12px !important;
  margin: 0 !important;
  box-shadow: none !important;
  outline: none !important;
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;
}

/* Template Textarea Specific Styles */
#schema-speed-sidebar-container .schema-speed-template-textarea {
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace !important;
  width: 100% !important;
  min-height: 200px !important;
  resize: vertical !important;
  white-space: pre !important;
  tab-size: 2 !important;
}

#schema-speed-sidebar-container .schema-speed-template-textarea:focus {
  border-color: var(--schema-speed-primary) !important;
  box-shadow: 0 0 0 2px rgba(124, 58, 237, 0.1) !important;
}

/* Buttons - Match CSS Tricks style */
#schema-speed-sidebar-container .schema-speed-action-btn {
  background: var(--schema-speed-primary) !important;
  color: white !important;
  border: none !important;
  border-radius: 6px !important;
  padding: 12px 24px !important;
  font-weight: 600 !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
  text-transform: none !important;
  letter-spacing: normal !important;
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;
}

#schema-speed-sidebar-container .schema-speed-action-btn:hover {
  background: var(--schema-speed-primary-hover) !important;
}

/* Ensure proper stacking for all interactive elements */
#schema-speed-sidebar-container button,
#schema-speed-sidebar-container input,
#schema-speed-sidebar-container select,
#schema-speed-sidebar-container a {
  position: relative !important;
  z-index: 2147483647 !important;
}

/* Big Step Headings - Match CSS Tricks style */
#schema-speed-sidebar-container .schema-speed-tab-header h2 {
  font-size: 42px !important;
  font-weight: 800 !important;
  color: #f1f5f9 !important;
  margin: 0 0 24px 0 !important;
  line-height: 1.1 !important;
  letter-spacing: -0.03em !important;
  text-transform: none !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
  padding: 0 !important;
  border: none !important;
  background: none !important;
  text-shadow: none !important;
}

/* Step Description - Match CSS Tricks style */
#schema-speed-sidebar-container .schema-speed-tab-description {
  font-size: 18px !important;
  line-height: 1.6 !important;
  color: #94a3b8 !important;
  margin: 0 0 40px 0 !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
  max-width: 600px !important;
}

/* Update heading colors */
#schema-speed-sidebar-container h1,
#schema-speed-sidebar-container h2,
#schema-speed-sidebar-container h3,
#schema-speed-sidebar-container h4,
#schema-speed-sidebar-container h5,
#schema-speed-sidebar-container h6 {
  color: #ffffff !important;
  margin-bottom: 12px !important;
}

/* Update tab description text */
#schema-speed-sidebar-container .schema-speed-tab-description {
  color: #a1a1aa !important;
  font-size: 14px !important;
  line-height: 1.6 !important;
  margin-bottom: 24px !important;
}

/* Update current site info section */
#schema-speed-sidebar-container .schema-speed-current-site-info {
  background: #1e1e1e !important;
  border: 1px solid #333333 !important;
  border-radius: 8px !important;
  padding: 24px !important; /* Match design system cardPadding */
}

/* Update form fields */
#schema-speed-sidebar-container input,
#schema-speed-sidebar-container select,
#schema-speed-sidebar-container textarea {
  background: #1e1e1e !important;
  border: 1px solid #333333 !important;
  color: #ffffff !important;
}

#schema-speed-sidebar-container .schema-speed-template-textarea {
  width: 100% !important;
  min-height: 200px !important;
  padding: 12px !important;
  border: 1px solid var(--schema-speed-border) !important;
  border-radius: 6px !important;
  background: var(--schema-speed-surface) !important;
  color: var(--schema-speed-text) !important;
  font-family: monospace !important;
  font-size: 13px !important;
  line-height: 1.5 !important;
  resize: vertical !important;
  margin-bottom: 16px !important;
}

#schema-speed-sidebar-container .schema-speed-template-textarea:focus {
  outline: none !important;
  border-color: var(--schema-speed-primary) !important;
  box-shadow: 0 0 0 2px rgba(124, 58, 237, 0.2) !important;
}

/* Update spacing for form groups and sections */
#schema-speed-sidebar-container .schema-speed-form-group {
  margin-bottom: 24px !important; /* Match design system sectionSpacing */
}

/* Update action buttons spacing */
#schema-speed-sidebar-container .schema-speed-action-buttons {
  margin-top: 24px !important; /* Match design system sectionSpacing */
  display: flex;
  flex-direction: column;
  gap: 12px !important; /* Match design system spacing.scale[2] */
}

/* Update form field spacing */
#schema-speed-sidebar-container .schema-speed-template-form {
  margin-top: 16px !important; /* Match design system fieldSpacing */
}

#schema-speed-sidebar-container label {
  margin-bottom: 8px !important; /* Match design system elementSpacing */
  display: block;
}

#schema-speed-sidebar-container input,
#schema-speed-sidebar-container select,
#schema-speed-sidebar-container textarea {
  margin-bottom: 16px !important; /* Match design system fieldSpacing */
}

/* Quick stats spacing */
#schema-speed-sidebar-container .schema-speed-quick-stats {
  margin-top: 16px !important; /* Match design system fieldSpacing */
  gap: 24px !important; /* Match design system sectionSpacing */
}

#schema-speed-dock-toggle {
  background: none;
  border: none;
  padding: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: background-color 0.2s ease;
}

#schema-speed-dock-toggle:hover {
  background: rgba(124, 58, 237, 0.1);
}

#schema-speed-dock-toggle svg {
  width: 20px;
  height: 20px;
  transition: transform 0.2s ease;
}

#schema-speed-dock-toggle svg path {
  fill: #f1f5f9;
}

#schema-speed-dock-toggle:hover svg {
  transform: scale(1.1);
}

/* Dark mode adjustments */
.dark #schema-speed-dock-toggle svg path {
  fill: #f1f5f9;
}

/* When docked, rotate the icon */
.docked #schema-speed-dock-toggle svg {
  transform: rotate(90deg);
}

.docked #schema-speed-dock-toggle:hover svg {
  transform: rotate(90deg) scale(1.1);
}

/* Saved Templates List Styles */
.schema-speed-templates-list {
  margin-top: 12px;
  background: #232228;
  border-radius: 6px;
  padding: 8px 0;
  box-shadow: 0 1px 4px rgba(0,0,0,0.08);
}
.schema-speed-template-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 16px;
  border-bottom: 1px solid #29283a;
  font-size: 15px;
  color: #f3f3fa;
}
.schema-speed-template-item:last-child {
  border-bottom: none;
}
.schema-speed-template-item.last-used {
  background: #28274a;
}
.schema-speed-template-item .template-header {
  display: flex;
  flex-direction: column;
}
.schema-speed-template-item .template-name {
  font-weight: 600;
  color: #fff;
}
.schema-speed-template-item .template-type {
  font-size: 12px;
  color: #b3b3c6;
}
.schema-speed-template-item .template-actions {
  display: flex;
  gap: 8px;
}
.schema-speed-template-item .use-template,
.schema-speed-template-item .delete-template {
  background: none;
  border: 1.5px solid transparent;
  border-radius: 4px;
  padding: 2px 10px;
  font-size: 13px;
  cursor: pointer;
  transition: border-color 0.2s, color 0.2s;
}
.schema-speed-template-item .use-template {
  color: #7C3AED;
  border-color: #7C3AED;
  background: #18171f;
}
.schema-speed-template-item .use-template:hover {
  background: #221c3a;
}
.schema-speed-template-item .delete-template {
  color: #f44336;
  border-color: #7C3AED;
  background: #18171f;
}
.schema-speed-template-item .delete-template:hover {
  background: #2d1a2d;
  color: #fff;
  border-color: #7C3AED;
}

/* SS Fields List Styles */
#schema-speed-fields-list {
  margin-top: 18px;
  background: #232228;
  border-radius: 6px;
  padding: 8px 0;
  box-shadow: 0 1px 4px rgba(0,0,0,0.08);
}
.ss-field-list-item {
  display: flex;
  align-items: center;
  padding: 6px 16px;
  border-bottom: 1px solid #29283a;
  font-size: 15px;
  color: #f3f3fa;
}
.ss-field-list-item:last-child {
  border-bottom: none;
}
.ss-status-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  display: inline-block;
  margin-right: 8px;
  background: #f44336;
}
.ss-status-dot.green {
  background: #4caf50;
}
.ss-status-dot.red {
  background: #f44336;
}
.ss-field-label {
  min-width: 120px;
  font-weight: 500;
  color: #fff;
  margin-right: 8px;
}
.ss-field-input {
  flex: 1;
  background: #18171f;
  border: 1px solid #29283a;
  border-radius: 4px;
  color: #fff;
  padding: 2px 8px;
  font-size: 14px;
}
.ss-capture-btn {
  background: none;
  border: 1.5px solid #7C3AED;
  border-radius: 4px;
  color: #7C3AED;
  padding: 2px 10px;
  font-size: 13px;
  cursor: pointer;
  margin-left: 8px;
  transition: border-color 0.2s, color 0.2s;
}
.ss-capture-btn:hover {
  background: #221c3a;
  color: #fff;
  border-color: #7C3AED;
}
