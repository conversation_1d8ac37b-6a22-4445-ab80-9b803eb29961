:root {
  --main-text-color: #3b3b3b;
  --secondary-text-color: rgba(0, 0, 0, 0.65);
  /* --primary-color:rgba(1, 115, 175, 0.88);
    --secondary-color:rgba(0, 114, 176, 1); */
  --primary-color: #0e76a8;
  --secondary-color: #0e76a8;
  --brown-color: #a00000;
  --red-color: #dc3545;
  --orange-color: #ffc107;
}

#sc-extension-tooltip {
  position: absolute;
  right: 410px;
  z-index: 2147483648;
  font-family: -apple-system, system-ui, BlinkMacSystemFont, Segoe UI, Roboto,
    Helvetica Neue, Fira Sans, Ubuntu, Oxygen, Oxygen Sans, Cantarell,
    Droid Sans, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol,
    Lucida Grande, Helvetica, Arial, sans-serif !important;
}

#sc-extension-tooltip.right-arrow .sc-custom-tooltip-header::after {
  right: -5px;
  margin-top: 7px;
}

#sc-extension-tooltip.left-arrow .sc-custom-tooltip-header::after {
  left: -7px;
  margin-top: 7px;
}

#sc-extension-frame {
  display: block !important;
}

#sc-extension-body .modal-backdrop{
  z-index: 2147483649 !important;
}

#sc-extension-body {
  font-family: -apple-system, system-ui, BlinkMacSystemFont, Segoe UI, Roboto,
    Helvetica Neue, Fira Sans, Ubuntu, Oxygen, Oxygen Sans, Cantarell,
    Droid Sans, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol,
    Lucida Grande, Helvetica, Arial, sans-serif !important;
}

#sc-extension-body pre {
  margin-bottom: 0 !important;
  min-height: 50px;
}

#sc-extension-body ::-webkit-scrollbar {
  width: 7px;
  height: 7px;
}

/* Track */
#sc-extension-body ::-webkit-scrollbar-track {
  background: #f1f1f17a;
  margin: 5px 0;
}

/* Handle */
#sc-extension-body ::-webkit-scrollbar-thumb {
  background: #ccc8c88d;
}

/* Handle on hover */
#sc-extension-body ::-webkit-scrollbar-thumb:hover {
  background: rgb(194, 193, 193);
}

#my-extension-root iframe {
  width: 100%;
  height: 100%;
  border: none;
}
#my-extension-root,
#my-extension-root-dev ul {
  list-style: none;
}

#sc-extension-body {
  width: 100%;
  height: 100%;
  padding: 0;
  margin: 0;
}

#my-extension-root html {
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  -ms-overflow-style: scrollbar;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

#my-extension-root article,
#my-extension-root aside,
#my-extension-root dialog,
#my-extension-root figcaption,
#my-extension-root figure,
#my-extension-root footer,
#my-extension-root header,
#my-extension-root hgroup,
#my-extension-root main,
#my-extension-root nav,
#my-extension-root section {
  display: block;
}

#my-extension-root body {
  margin: 0;
  color: var(--primary-color);
  font-size: 14px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
    'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji',
    'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  font-variant: tabular-nums;
  line-height: 1.5715;
  background-color: #fff;
  -webkit-font-feature-settings: 'tnum';
  font-feature-settings: 'tnum';
}

#my-extension-root {
  width: 400px;
  height: 100%;
  position: fixed;
  top: 0px;
  right: 0px;
  z-index: 2147483647;
  background-color: #00000038;
  padding: 0;
  box-shadow: 0px 0px 5px var(--primary-color);
  animation: fadein 0.5s;
}

#modal-wrapper pre {
  border: none;
}
#modal-wrapper * {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans",
    "Helvetica Neue", sans-serif !important;
}

#modal-wrapper ::-webkit-scrollbar {
  width: 7px !important;
}
/* Track */
#modal-wrapper ::-webkit-scrollbar-track {
  background: #f1f1f17a !important;
  margin: 2px 0;
}

/* Handle */
#modal-wrapper ::-webkit-scrollbar-thumb {
  background: #ccc8c88d !important;
}

/* Handle on hover */
#modal-wrapper ::-webkit-scrollbar-thumb:hover {
  background: rgb(194, 193, 193) !important;
}
